package com.sdwu.kotlin.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.sdwu.kotlin.data.model.*
import com.sdwu.kotlin.data.repository.ECGStats
import kotlin.math.*

/**
 * ECG数据卡片组件
 * 在首页展示ECG统计信息
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ECGDataCard(
    stats: ECGStats?,
    latestData: ECGWaveformData?,
    isLoading: Boolean,
    onCardClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        onClick = onCardClick,
        modifier = modifier
            .fillMaxWidth()
            .height(200.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        if (isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
                // 标题行
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "ECG",
                            tint = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.size(24.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "心电图",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold
                        )
                    }
                    
                    // 信号质量指示器
                    stats?.signalQuality?.let { quality ->
                        SignalQualityIndicator(quality = quality)
                    }
                }
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 统计数据行
                stats?.let { ecgStats ->
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        ECGStatItem(
                            label = "平均心率",
                            value = "${ecgStats.averageHeartRate}",
                            unit = "bpm"
                        )
                        ECGStatItem(
                            label = "最低",
                            value = "${ecgStats.minHeartRate}",
                            unit = "bpm"
                        )
                        ECGStatItem(
                            label = "最高",
                            value = "${ecgStats.maxHeartRate}",
                            unit = "bpm"
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 简化的ECG波形预览
                latestData?.let { data ->
                    ECGWaveformPreview(
                        waveformData = data,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(60.dp)
                    )
                }
            }
        }
    }
}

/**
 * ECG统计项组件
 */
@Composable
private fun ECGStatItem(
    label: String,
    value: String,
    unit: String,
    modifier: Modifier = Modifier
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.titleLarge,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
        )
        Text(
            text = unit,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 信号质量指示器
 */
@Composable
private fun SignalQualityIndicator(
    quality: ECGQuality,
    modifier: Modifier = Modifier
) {
    val (color, text) = when (quality) {
        ECGQuality.EXCELLENT -> Color.Green to "优秀"
        ECGQuality.GOOD -> Color.Blue to "良好"
        ECGQuality.FAIR -> Color.Yellow to "一般"
        ECGQuality.POOR -> Color(0xFFFFA500) to "差"
        ECGQuality.UNUSABLE -> Color.Red to "不可用"
    }
    
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier
            .background(
                color = color.copy(alpha = 0.1f),
                shape = RoundedCornerShape(12.dp)
            )
            .padding(horizontal = 8.dp, vertical = 4.dp)
    ) {
        Box(
            modifier = Modifier
                .size(8.dp)
                .background(color = color, shape = RoundedCornerShape(4.dp))
        )
        Spacer(modifier = Modifier.width(4.dp))
        Text(
            text = text,
            style = MaterialTheme.typography.bodySmall,
            color = color,
            fontSize = 10.sp
        )
    }
}

/**
 * ECG波形预览组件
 */
@Composable
fun ECGWaveformPreview(
    waveformData: ECGWaveformData,
    modifier: Modifier = Modifier
) {
    Canvas(
        modifier = modifier
            .clip(RoundedCornerShape(8.dp))
            .background(MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f))
    ) {
        drawECGWaveform(waveformData.dataPoints.take(100)) // 只显示前100个点
    }
}

/**
 * 绘制ECG波形
 */
private fun DrawScope.drawECGWaveform(dataPoints: List<ECGDataPoint>) {
    if (dataPoints.isEmpty()) return
    
    val path = Path()
    val width = size.width
    val height = size.height
    val centerY = height / 2
    
    // 找到电压的最大值和最小值用于缩放
    val maxVoltage = dataPoints.maxOfOrNull { it.voltage } ?: 1f
    val minVoltage = dataPoints.minOfOrNull { it.voltage } ?: -1f
    val voltageRange = maxVoltage - minVoltage
    
    if (voltageRange == 0f) return
    
    // 绘制网格线
    drawECGGrid()
    
    // 绘制波形
    dataPoints.forEachIndexed { index, point ->
        val x = (index.toFloat() / (dataPoints.size - 1)) * width
        val normalizedVoltage = (point.voltage - minVoltage) / voltageRange
        val y = centerY - (normalizedVoltage - 0.5f) * height * 0.8f
        
        if (index == 0) {
            path.moveTo(x, y)
        } else {
            path.lineTo(x, y)
        }
    }
    
    drawPath(
        path = path,
        color = Color.Red,
        style = Stroke(width = 2.dp.toPx())
    )
}

/**
 * 绘制ECG网格
 */
private fun DrawScope.drawECGGrid() {
    val gridColor = Color.Gray.copy(alpha = 0.3f)
    val strokeWidth = 1.dp.toPx()
    
    // 绘制水平网格线
    for (i in 0..4) {
        val y = (i.toFloat() / 4) * size.height
        drawLine(
            color = gridColor,
            start = Offset(0f, y),
            end = Offset(size.width, y),
            strokeWidth = strokeWidth
        )
    }
    
    // 绘制垂直网格线
    for (i in 0..10) {
        val x = (i.toFloat() / 10) * size.width
        drawLine(
            color = gridColor,
            start = Offset(x, 0f),
            end = Offset(x, size.height),
            strokeWidth = strokeWidth
        )
    }
}

/**
 * 实时ECG监测组件
 */
@Composable
fun RealtimeECGMonitor(
    realtimeData: ECGRealtimeData?,
    isMonitoring: Boolean,
    onStartMonitoring: () -> Unit,
    onStopMonitoring: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "实时监测",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                Button(
                    onClick = if (isMonitoring) onStopMonitoring else onStartMonitoring,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = if (isMonitoring) 
                            MaterialTheme.colorScheme.error 
                        else 
                            MaterialTheme.colorScheme.primary
                    )
                ) {
                    Text(if (isMonitoring) "停止" else "开始")
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            realtimeData?.let { data ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    RealtimeDataItem(
                        icon = Icons.Default.Favorite,
                        label = "心率",
                        value = "${data.heartRate}",
                        unit = "bpm"
                    )
                    RealtimeDataItem(
                        icon = Icons.Default.Refresh,
                        label = "电压",
                        value = String.format("%.2f", data.currentDataPoint.voltage),
                        unit = "mV"
                    )
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 连接状态和电池电量
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = if (data.isConnected) "已连接" else "未连接",
                        style = MaterialTheme.typography.bodySmall,
                        color = if (data.isConnected) Color.Green else Color.Red
                    )
                    Text(
                        text = "电量: ${data.batteryLevel}%",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

/**
 * 实时数据项组件
 */
@Composable
private fun RealtimeDataItem(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    label: String,
    value: String,
    unit: String,
    modifier: Modifier = Modifier
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
    ) {
        Icon(
            imageVector = icon,
            contentDescription = label,
            tint = MaterialTheme.colorScheme.primary,
            modifier = Modifier.size(20.dp)
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = value,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold
        )
        Text(
            text = unit,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}
