package com.sdwu.kotlin.components

import android.util.Log
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.sdwu.kotlin.utils.ErrorLogger
import com.sdwu.kotlin.utils.NavigationErrorHandler

/**
 * 安全的Composable包装器
 * 用于监控Composable的加载状态
 */
@Composable
fun SafeComposable(
    errorMessage: String = "页面加载失败",
    onRetry: (() -> Unit)? = null,
    content: @Composable () -> Unit
) {
    var hasError by remember { mutableStateOf(false) }
    var errorDetails by remember { mutableStateOf("") }

    // 错误状态处理
    if (hasError) {
        ErrorDisplay(
            message = errorMessage,
            details = errorDetails,
            onRetry = {
                hasError = false
                errorDetails = ""
                onRetry?.invoke()
            }
        )
    } else {
        // 初始化日志记录
        LaunchedEffect(Unit) {
            Log.d("SafeComposable", "SafeComposable开始渲染")
            ErrorLogger.logInfo("SafeComposable", "安全组件初始化")
        }

        // 直接渲染内容
        content()
    }
}

/**
 * 错误显示组件
 */
@Composable
fun ErrorDisplay(
    message: String,
    details: String = "",
    onRetry: (() -> Unit)? = null
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.Warning,
            contentDescription = "错误",
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.error
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "出现错误",
            style = MaterialTheme.typography.headlineMedium,
            color = MaterialTheme.colorScheme.error
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = message,
            style = MaterialTheme.typography.bodyLarge,
            textAlign = TextAlign.Center
        )
        
        if (details.isNotEmpty()) {
            Spacer(modifier = Modifier.height(8.dp))
            
            Card(
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.errorContainer
                )
            ) {
                Text(
                    text = "错误详情: $details",
                    modifier = Modifier.padding(12.dp),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onErrorContainer
                )
            }
        }
        
        if (onRetry != null) {
            Spacer(modifier = Modifier.height(16.dp))
            
            Button(
                onClick = onRetry,
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.primary
                )
            ) {
                Icon(
                    imageVector = Icons.Default.Refresh,
                    contentDescription = "重试"
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("重试")
            }
        }
    }
}

/**
 * 安全导航按钮
 * 包装了错误处理的导航按钮
 */
@Composable
fun SafeNavigationButton(
    text: String,
    route: String,
    navController: NavController,
    from: String = "unknown",
    modifier: Modifier = Modifier,
    enabled: Boolean = true
) {
    var isNavigating by remember { mutableStateOf(false) }
    var navigationError by remember { mutableStateOf<String?>(null) }
    
    Button(
        onClick = {
            if (!isNavigating) {
                isNavigating = true
                navigationError = null
                
                val success = NavigationErrorHandler.safeNavigateTo(
                    navController = navController,
                    route = route,
                    from = from
                )
                
                if (!success) {
                    navigationError = "导航到 $route 失败"
                    Log.e("SafeNavigationButton", "导航失败: $from -> $route")
                }
                
                isNavigating = false
            }
        },
        modifier = modifier,
        enabled = enabled && !isNavigating
    ) {
        if (isNavigating) {
            CircularProgressIndicator(
                modifier = Modifier.size(16.dp),
                strokeWidth = 2.dp
            )
            Spacer(modifier = Modifier.width(8.dp))
        }
        Text(text)
    }
    
    // 显示导航错误
    navigationError?.let { error ->
        LaunchedEffect(error) {
            Log.e("SafeNavigationButton", error)
            // 这里可以显示Snackbar或其他错误提示
            kotlinx.coroutines.delay(3000)
            navigationError = null
        }
    }
}

/**
 * 安全返回按钮
 */
@Composable
fun SafeBackButton(
    navController: NavController,
    from: String = "unknown",
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit = {
        Icon(
            imageVector = Icons.Default.ArrowBack,
            contentDescription = "返回"
        )
    }
) {
    var isNavigating by remember { mutableStateOf(false) }
    
    IconButton(
        onClick = {
            if (!isNavigating) {
                isNavigating = true
                
                val success = NavigationErrorHandler.safePopBackStack(navController, from)
                if (!success) {
                    Log.e("SafeBackButton", "返回失败，来源: $from")
                }
                
                isNavigating = false
            }
        },
        modifier = modifier,
        enabled = !isNavigating
    ) {
        if (isNavigating) {
            CircularProgressIndicator(
                modifier = Modifier.size(16.dp),
                strokeWidth = 2.dp
            )
        } else {
            content()
        }
    }
}

/**
 * 导航状态监控组件
 * 用于监控和记录导航状态变化
 */
@Composable
fun NavigationStateMonitor(
    navController: NavController,
    tag: String = "NavigationMonitor"
) {
    val currentDestination = navController.currentDestination
    
    LaunchedEffect(currentDestination) {
        try {
            val route = currentDestination?.route ?: "unknown"
            Log.d(tag, "导航状态变化: 当前路由 = $route")
            ErrorLogger.logInfo(tag, "导航到: $route")
            
            // 生成导航状态报告
            val stateReport = NavigationErrorHandler.getCurrentNavigationState(navController)
            Log.d(tag, "导航状态: $stateReport")
            
        } catch (e: Exception) {
            Log.e(tag, "导航状态监控失败", e)
            ErrorLogger.logError(tag, "导航状态监控失败", e)
        }
    }
}

/**
 * 页面加载监控组件
 * 用于监控页面加载过程
 */
@Composable
fun PageLoadMonitor(
    pageName: String,
    onLoadStart: (() -> Unit)? = null,
    onLoadComplete: (() -> Unit)? = null,
    onLoadError: ((Exception) -> Unit)? = null
) {
    LaunchedEffect(Unit) {
        try {
            Log.d("PageLoadMonitor", "页面开始加载: $pageName")
            ErrorLogger.logInfo("PageLoadMonitor", "页面加载开始: $pageName")
            onLoadStart?.invoke()
            
            // 模拟页面加载完成
            kotlinx.coroutines.delay(100)
            
            Log.d("PageLoadMonitor", "页面加载完成: $pageName")
            ErrorLogger.logInfo("PageLoadMonitor", "页面加载完成: $pageName")
            onLoadComplete?.invoke()
            
        } catch (e: Exception) {
            Log.e("PageLoadMonitor", "页面加载失败: $pageName", e)
            ErrorLogger.logError("PageLoadMonitor", "页面加载失败: $pageName", e)
            onLoadError?.invoke(e)
        }
    }
}
