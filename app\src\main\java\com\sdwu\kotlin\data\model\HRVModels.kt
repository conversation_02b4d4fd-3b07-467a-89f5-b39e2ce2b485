package com.sdwu.kotlin.data.model

/**
 * HRV分析类型
 */
enum class HRVAnalysisType {
    TIME_DOMAIN,     // 时域分析
    FREQUENCY_DOMAIN, // 频域分析
    NONLINEAR,       // 非线性分析
    GEOMETRIC        // 几何分析
}

/**
 * HRV测量状态
 */
enum class HRVMeasurementStatus {
    MEASURING,       // 测量中
    COMPLETED,       // 完成
    FAILED,          // 失败
    CANCELLED        // 取消
}

/**
 * R-R间期数据点
 * 表示相邻R波之间的时间间隔
 */
data class RRInterval(
    val timestamp: Long,    // 时间戳
    val interval: Float,    // R-R间期（毫秒）
    val quality: Float      // 数据质量（0-1）
)

/**
 * HRV时域指标
 */
data class HRVTimeDomainMetrics(
    val meanRR: Float,      // 平均R-R间期（ms）
    val sdnn: Float,        // R-R间期标准差（ms）
    val rmssd: Float,       // 相邻R-R间期差值的均方根（ms）
    val pnn50: Float,       // 相邻R-R间期差值>50ms的百分比
    val triangularIndex: Float, // 三角指数
    val tinn: Float         // 三角插值基线宽度（ms）
)

/**
 * HRV频域指标
 */
data class HRVFrequencyDomainMetrics(
    val totalPower: Float,  // 总功率（ms²）
    val vlf: Float,         // 极低频功率（0.003-0.04 Hz）
    val lf: Float,          // 低频功率（0.04-0.15 Hz）
    val hf: Float,          // 高频功率（0.15-0.4 Hz）
    val lfHfRatio: Float,   // LF/HF比值
    val lfNorm: Float,      // 标准化LF
    val hfNorm: Float       // 标准化HF
)

/**
 * HRV非线性指标
 */
data class HRVNonlinearMetrics(
    val sd1: Float,         // Poincaré图短轴标准差
    val sd2: Float,         // Poincaré图长轴标准差
    val sd1Sd2Ratio: Float, // SD1/SD2比值
    val approximateEntropy: Float, // 近似熵
    val sampleEntropy: Float,      // 样本熵
    val dfa1: Float,        // 去趋势波动分析α1
    val dfa2: Float         // 去趋势波动分析α2
)

/**
 * HRV综合数据
 */
data class HRVData(
    val id: String,                              // HRV数据ID
    val patientId: String,                       // 患者ID
    val sessionId: String,                       // 测量会话ID
    val rrIntervals: List<RRInterval>,          // R-R间期数据
    val timeDomainMetrics: HRVTimeDomainMetrics?, // 时域指标
    val frequencyDomainMetrics: HRVFrequencyDomainMetrics?, // 频域指标
    val nonlinearMetrics: HRVNonlinearMetrics?, // 非线性指标
    val measurementDuration: Long,               // 测量持续时间（毫秒）
    val recordedAt: Long,                        // 记录时间
    val status: HRVMeasurementStatus,           // 测量状态
    val dataQuality: Float                       // 数据质量评分（0-1）
)

/**
 * HRV分析配置
 */
data class HRVAnalysisConfig(
    val analysisTypes: List<HRVAnalysisType>,   // 分析类型
    val minimumDuration: Long,                   // 最小测量时间（毫秒）
    val artifactThreshold: Float,               // 伪迹阈值
    val filterSettings: HRVFilterSettings       // 滤波设置
)

/**
 * HRV滤波设置
 */
data class HRVFilterSettings(
    val enableArtifactRemoval: Boolean,         // 启用伪迹移除
    val rrIntervalRange: Pair<Float, Float>,    // R-R间期有效范围（ms）
    val outlierThreshold: Float,                // 异常值阈值
    val smoothingEnabled: Boolean               // 启用平滑处理
)

/**
 * HRV趋势数据
 * 用于显示长期HRV变化趋势
 */
data class HRVTrendData(
    val date: Long,                 // 日期
    val avgRMSSD: Float,           // 平均RMSSD
    val avgSDNN: Float,            // 平均SDNN
    val avgLFHFRatio: Float,       // 平均LF/HF比值
    val stressScore: Float,        // 压力评分（0-100）
    val recoveryScore: Float,      // 恢复评分（0-100）
    val dataQuality: Float         // 数据质量
)

/**
 * HRV实时数据
 * 用于实时HRV监测
 */
data class HRVRealtimeData(
    val sessionId: String,          // 会话ID
    val currentRMSSD: Float,        // 当前RMSSD值
    val currentHeartRate: Int,      // 当前心率
    val measurementProgress: Float, // 测量进度（0-1）
    val dataQuality: Float,         // 当前数据质量
    val isStable: Boolean,          // 信号是否稳定
    val timestamp: Long             // 时间戳
)
