<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义渐变色 -->
    <linearGradient id="liveDataGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2E7D32;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="composeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6200EA;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3700B3;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="bridgeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF9800;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F57C00;stop-opacity:1" />
    </linearGradient>
    
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
            refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    
    <!-- 数据流箭头 -->
    <marker id="dataArrow" markerWidth="12" markerHeight="8" 
            refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#FF5722" />
    </marker>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="3" dy="3" stdDeviation="4" flood-color="#00000025"/>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="1600" height="1200" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="800" y="35" text-anchor="middle" font-family="Arial, sans-serif" 
        font-size="28" font-weight="bold" fill="#333">
    🔗 observeAsState() 详解：LiveData 与 Compose 的桥梁
  </text>
  
  <!-- 核心作用说明 -->
  <g id="core-function">
    <rect x="100" y="70" width="1400" height="80" fill="url(#bridgeGradient)" rx="8" filter="url(#shadow)"/>
    <text x="800" y="105" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="20" font-weight="bold" fill="white">
      🎯 核心作用：将 LiveData 转换为 Compose State，实现响应式UI更新
    </text>
    <text x="120" y="130" font-family="Arial, sans-serif" font-size="14" fill="white">
      observeAsState() 是 Compose 提供的扩展函数，让传统的 LiveData 能够在 Compose 中触发重组(Recomposition)
    </text>
  </g>
  
  <!-- 工作原理图解 -->
  <g id="working-principle">
    <rect x="100" y="170" width="1400" height="50" fill="#E3F2FD" stroke="#2196F3" stroke-width="2" rx="8"/>
    <text x="800" y="200" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="18" font-weight="bold" fill="#1565C0">
      🔄 工作原理流程图
    </text>
    
    <!-- LiveData 部分 -->
    <rect x="120" y="240" width="300" height="200" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2" rx="8"/>
    <text x="270" y="265" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2E7D32">
      📊 LiveData (ViewModel)
    </text>
    <text x="140" y="290" font-family="Arial, sans-serif" font-size="13" fill="#333">
      <tspan font-weight="bold">特点：</tspan>
    </text>
    <text x="140" y="305" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 生命周期感知
    </text>
    <text x="140" y="320" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 观察者模式
    </text>
    <text x="140" y="335" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 主线程更新
    </text>
    
    <text x="140" y="360" font-family="Arial, sans-serif" font-size="13" fill="#333">
      <tspan font-weight="bold">代码示例：</tspan>
    </text>
    <text x="140" y="375" font-family="Courier New, monospace" font-size="11" fill="#333">
      val user: LiveData&lt;User?&gt;
    </text>
    <text x="140" y="390" font-family="Courier New, monospace" font-size="11" fill="#333">
      user.value = newUser
    </text>
    <text x="140" y="405" font-family="Courier New, monospace" font-size="11" fill="#333">
      // 通知所有观察者
    </text>
    <text x="140" y="425" font-family="Arial, sans-serif" font-size="12" fill="#2E7D32">
      ❌ Compose无法直接观察
    </text>
    
    <!-- observeAsState 桥梁 -->
    <rect x="480" y="240" width="320" height="200" fill="#FFF3E0" stroke="#FF9800" stroke-width="3" rx="8"/>
    <text x="640" y="265" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#F57C00">
      🌉 observeAsState()
    </text>
    <text x="500" y="290" font-family="Arial, sans-serif" font-size="13" fill="#333">
      <tspan font-weight="bold">转换过程：</tspan>
    </text>
    <text x="500" y="305" font-family="Arial, sans-serif" font-size="12" fill="#333">
      1. 订阅 LiveData 变化
    </text>
    <text x="500" y="320" font-family="Arial, sans-serif" font-size="12" fill="#333">
      2. 将值转换为 State&lt;T&gt;
    </text>
    <text x="500" y="335" font-family="Arial, sans-serif" font-size="12" fill="#333">
      3. 触发 Compose 重组
    </text>
    <text x="500" y="350" font-family="Arial, sans-serif" font-size="12" fill="#333">
      4. 自动管理生命周期
    </text>
    
    <text x="500" y="375" font-family="Arial, sans-serif" font-size="13" fill="#333">
      <tspan font-weight="bold">使用方式：</tspan>
    </text>
    <text x="500" y="390" font-family="Courier New, monospace" font-size="11" fill="#333">
      val user by viewModel.user
    </text>
    <text x="500" y="405" font-family="Courier New, monospace" font-size="11" fill="#333">
          .observeAsState()
    </text>
    <text x="500" y="425" font-family="Arial, sans-serif" font-size="12" fill="#F57C00">
      ✅ 完美桥接两个世界
    </text>
    
    <!-- Compose State 部分 -->
    <rect x="860" y="240" width="300" height="200" fill="#F3E5F5" stroke="#6200EA" stroke-width="2" rx="8"/>
    <text x="1010" y="265" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#3700B3">
      🎨 Compose State
    </text>
    <text x="880" y="290" font-family="Arial, sans-serif" font-size="13" fill="#333">
      <tspan font-weight="bold">特点：</tspan>
    </text>
    <text x="880" y="305" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 触发重组
    </text>
    <text x="880" y="320" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 类型安全
    </text>
    <text x="880" y="335" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 声明式更新
    </text>
    
    <text x="880" y="360" font-family="Arial, sans-serif" font-size="13" fill="#333">
      <tspan font-weight="bold">结果：</tspan>
    </text>
    <text x="880" y="375" font-family="Courier New, monospace" font-size="11" fill="#333">
      @Composable
    </text>
    <text x="880" y="390" font-family="Courier New, monospace" font-size="11" fill="#333">
      Text(user?.name ?: "")
    </text>
    <text x="880" y="405" font-family="Courier New, monospace" font-size="11" fill="#333">
      // 自动更新UI
    </text>
    <text x="880" y="425" font-family="Arial, sans-serif" font-size="12" fill="#6200EA">
      ✅ 响应式UI更新
    </text>
    
    <!-- 数据流箭头 -->
    <line x1="420" y1="340" x2="475" y2="340" stroke="#FF5722" stroke-width="4" marker-end="url(#dataArrow)"/>
    <line x1="800" y1="340" x2="855" y2="340" stroke="#FF5722" stroke-width="4" marker-end="url(#dataArrow)"/>
    
    <text x="447" y="330" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#FF5722">
      转换
    </text>
    <text x="827" y="330" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#FF5722">
      重组
    </text>
  </g>
  
  <!-- 代码对比 -->
  <g id="code-comparison">
    <rect x="100" y="460" width="1400" height="50" fill="#E3F2FD" stroke="#2196F3" stroke-width="2" rx="8"/>
    <text x="800" y="490" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="18" font-weight="bold" fill="#1565C0">
      💻 代码对比：传统方式 vs observeAsState()
    </text>
    
    <!-- 传统DataBinding方式 -->
    <rect x="120" y="530" width="680" height="200" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2" rx="8"/>
    <text x="460" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2E7D32">
      📋 传统 DataBinding 方式
    </text>
    <text x="140" y="575" font-family="Arial, sans-serif" font-size="13" fill="#333">
      <tspan font-weight="bold">ViewModel:</tspan>
    </text>
    <text x="140" y="590" font-family="Courier New, monospace" font-size="11" fill="#333">
      val user: LiveData&lt;User?&gt; = _user
    </text>
    <text x="140" y="605" font-family="Arial, sans-serif" font-size="13" fill="#333">
      <tspan font-weight="bold">Activity:</tspan>
    </text>
    <text x="140" y="620" font-family="Courier New, monospace" font-size="11" fill="#333">
      binding.viewModel = viewModel
    </text>
    <text x="140" y="635" font-family="Courier New, monospace" font-size="11" fill="#333">
      binding.lifecycleOwner = this
    </text>
    <text x="140" y="655" font-family="Arial, sans-serif" font-size="13" fill="#333">
      <tspan font-weight="bold">XML:</tspan>
    </text>
    <text x="140" y="670" font-family="Courier New, monospace" font-size="11" fill="#333">
      android:text="@{viewModel.user.name}"
    </text>
    <text x="140" y="690" font-family="Arial, sans-serif" font-size="12" fill="#333">
      ❌ 需要XML绑定表达式
    </text>
    <text x="140" y="705" font-family="Arial, sans-serif" font-size="12" fill="#333">
      ❌ 调试困难
    </text>
    
    <!-- Compose + observeAsState 方式 -->
    <rect x="820" y="530" width="680" height="200" fill="#F3E5F5" stroke="#6200EA" stroke-width="2" rx="8"/>
    <text x="1160" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#3700B3">
      🚀 Compose + observeAsState()
    </text>
    <text x="840" y="575" font-family="Arial, sans-serif" font-size="13" fill="#333">
      <tspan font-weight="bold">ViewModel (不变):</tspan>
    </text>
    <text x="840" y="590" font-family="Courier New, monospace" font-size="11" fill="#333">
      val user: LiveData&lt;User?&gt; = _user  // 完全相同
    </text>
    <text x="840" y="610" font-family="Arial, sans-serif" font-size="13" fill="#333">
      <tspan font-weight="bold">Composable:</tspan>
    </text>
    <text x="840" y="625" font-family="Courier New, monospace" font-size="11" fill="#333">
      @Composable
    </text>
    <text x="840" y="640" font-family="Courier New, monospace" font-size="11" fill="#333">
      fun UserProfile(viewModel: DataBindingViewModel) {
    </text>
    <text x="840" y="655" font-family="Courier New, monospace" font-size="11" fill="#333">
          val user by viewModel.user.observeAsState()
    </text>
    <text x="840" y="670" font-family="Courier New, monospace" font-size="11" fill="#333">
          Text(text = user?.name ?: "Loading...")
    </text>
    <text x="840" y="685" font-family="Courier New, monospace" font-size="11" fill="#333">
      }
    </text>
    <text x="840" y="705" font-family="Arial, sans-serif" font-size="12" fill="#333">
      ✅ 纯Kotlin代码，类型安全
    </text>
    <text x="840" y="720" font-family="Arial, sans-serif" font-size="12" fill="#333">
      ✅ 易于调试和测试
    </text>
  </g>

  <!-- 基于您项目的实际示例 -->
  <g id="project-example">
    <rect x="100" y="750" width="1400" height="50" fill="url(#bridgeGradient)" rx="8" filter="url(#shadow)"/>
    <text x="800" y="780" text-anchor="middle" font-family="Arial, sans-serif"
          font-size="18" font-weight="bold" fill="white">
      🎯 基于您项目的实际示例
    </text>

    <!-- 原有DataBindingViewModel -->
    <rect x="120" y="820" width="680" height="180" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2" rx="8"/>
    <text x="460" y="845" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2E7D32">
      📊 您的 DataBindingViewModel (保持不变)
    </text>
    <text x="140" y="865" font-family="Courier New, monospace" font-size="11" fill="#333">
      class DataBindingViewModel(
    </text>
    <text x="140" y="880" font-family="Courier New, monospace" font-size="11" fill="#333">
          private val userRepository: UserRepositoryInterface
    </text>
    <text x="140" y="895" font-family="Courier New, monospace" font-size="11" fill="#333">
      ) : ViewModel() {
    </text>
    <text x="140" y="915" font-family="Courier New, monospace" font-size="11" fill="#333">
          private val _user = MutableLiveData&lt;User?&gt;()
    </text>
    <text x="140" y="930" font-family="Courier New, monospace" font-size="11" fill="#333">
          val user: LiveData&lt;User?&gt; = _user
    </text>
    <text x="140" y="950" font-family="Courier New, monospace" font-size="11" fill="#333">
          private val _isLoading = MutableLiveData(false)
    </text>
    <text x="140" y="965" font-family="Courier New, monospace" font-size="11" fill="#333">
          val isLoading: LiveData&lt;Boolean&gt; = _isLoading
    </text>
    <text x="140" y="985" font-family="Courier New, monospace" font-size="11" fill="#333">
      }
    </text>

    <!-- Compose中的使用 -->
    <rect x="820" y="820" width="680" height="180" fill="#F3E5F5" stroke="#6200EA" stroke-width="2" rx="8"/>
    <text x="1160" y="845" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#3700B3">
      🚀 在 Compose 中使用 observeAsState()
    </text>
    <text x="840" y="865" font-family="Courier New, monospace" font-size="11" fill="#333">
      @Composable
    </text>
    <text x="840" y="880" font-family="Courier New, monospace" font-size="11" fill="#333">
      fun UserProfileScreen(viewModel: DataBindingViewModel) {
    </text>
    <text x="840" y="900" font-family="Courier New, monospace" font-size="11" fill="#333">
          // 🔗 observeAsState() 在这里发挥作用
    </text>
    <text x="840" y="915" font-family="Courier New, monospace" font-size="11" fill="#333">
          val user by viewModel.user.observeAsState()
    </text>
    <text x="840" y="930" font-family="Courier New, monospace" font-size="11" fill="#333">
          val isLoading by viewModel.isLoading.observeAsState(false)
    </text>
    <text x="840" y="950" font-family="Courier New, monospace" font-size="11" fill="#333">
          if (isLoading) CircularProgressIndicator()
    </text>
    <text x="840" y="965" font-family="Courier New, monospace" font-size="11" fill="#333">
          else Text(user?.name ?: "No user")
    </text>
    <text x="840" y="985" font-family="Courier New, monospace" font-size="11" fill="#333">
      }
    </text>
  </g>

  <!-- 技术细节和最佳实践 -->
  <g id="technical-details">
    <rect x="100" y="1020" width="1400" height="140" fill="#FFF3E0" stroke="#FF9800" stroke-width="2" rx="8" filter="url(#shadow)"/>
    <text x="800" y="1050" text-anchor="middle" font-family="Arial, sans-serif"
          font-size="18" font-weight="bold" fill="#F57C00">
      🔧 技术细节和最佳实践
    </text>

    <text x="120" y="1075" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#F57C00">
      🎯 observeAsState() 的内部机制：
    </text>
    <text x="120" y="1090" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 自动订阅 LiveData 的变化  • 在 Compose 的 DisposableEffect 中管理生命周期  • 当 LiveData 值改变时，触发 Compose 重组
    </text>

    <text x="120" y="1115" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#F57C00">
      💡 最佳实践：
    </text>
    <text x="120" y="1130" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 提供默认值：observeAsState(defaultValue)  • 避免在循环中使用  • 配合 remember 优化性能
    </text>

    <text x="800" y="1075" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#F57C00">
      ⚠️ 注意事项：
    </text>
    <text x="800" y="1090" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 只能在 @Composable 函数中使用  • 会自动处理生命周期，无需手动取消订阅  • 性能优于传统的 Observer 模式
    </text>

    <text x="800" y="1115" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#F57C00">
      🔄 替代方案：
    </text>
    <text x="800" y="1130" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • StateFlow.collectAsState() - 更现代的方式  • 直接使用 State&lt;T&gt; - 纯 Compose 方式
    </text>
  </g>

  <!-- 连接箭头 -->
  <g id="arrows">
    <line x1="460" y1="730" x2="460" y2="745" stroke="#4CAF50" stroke-width="3" marker-end="url(#arrowhead)"/>
    <line x1="1160" y1="730" x2="1160" y2="745" stroke="#6200EA" stroke-width="3" marker-end="url(#arrowhead)"/>

    <!-- 数据流动箭头 -->
    <line x1="800" y1="900" x2="840" y2="900" stroke="#FF5722" stroke-width="3" marker-end="url(#dataArrow)"/>
    <text x="820" y="890" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#FF5722">
      数据流
    </text>
  </g>
</svg>
