package com.sdwu.kotlin.utils;

/**
 * 崩溃处理器
 * 捕获并记录应用程序的未处理异常
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0003\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0007\u0018\u0000 \u00152\u00020\u0001:\u0001\u0015B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\tJ\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\f0\u000bJ\u000e\u0010\r\u001a\u00020\u00072\u0006\u0010\u0003\u001a\u00020\u0004J\u0010\u0010\u000e\u001a\u00020\u00072\u0006\u0010\u000f\u001a\u00020\u0010H\u0002J\u0010\u0010\u0011\u001a\u00020\u00072\u0006\u0010\u000f\u001a\u00020\u0010H\u0002J\u0018\u0010\u0012\u001a\u00020\u00072\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u000f\u001a\u00020\u0010H\u0016R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0005\u001a\u0004\u0018\u00010\u0001X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0016"}, d2 = {"Lcom/sdwu/kotlin/utils/CrashHandler;", "Ljava/lang/Thread$UncaughtExceptionHandler;", "()V", "context", "Landroid/content/Context;", "defaultHandler", "cleanOldCrashLogs", "", "maxFiles", "", "getCrashLogFiles", "", "Ljava/io/File;", "init", "logCrashToFile", "ex", "", "logCrashToLogcat", "uncaughtException", "thread", "Ljava/lang/Thread;", "Companion", "app_debug"})
public final class CrashHandler implements java.lang.Thread.UncaughtExceptionHandler {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "CrashHandler";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String CRASH_LOG_DIR = "crash_logs";
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.sdwu.kotlin.utils.CrashHandler instance;
    @org.jetbrains.annotations.Nullable()
    private android.content.Context context;
    @org.jetbrains.annotations.Nullable()
    private java.lang.Thread.UncaughtExceptionHandler defaultHandler;
    @org.jetbrains.annotations.NotNull()
    public static final com.sdwu.kotlin.utils.CrashHandler.Companion Companion = null;
    
    private CrashHandler() {
        super();
    }
    
    /**
     * 初始化崩溃处理器
     */
    public final void init(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
    
    @java.lang.Override()
    public void uncaughtException(@org.jetbrains.annotations.NotNull()
    java.lang.Thread thread, @org.jetbrains.annotations.NotNull()
    java.lang.Throwable ex) {
    }
    
    /**
     * 将崩溃信息记录到文件
     */
    private final void logCrashToFile(java.lang.Throwable ex) {
    }
    
    /**
     * 将崩溃信息记录到Logcat
     */
    private final void logCrashToLogcat(java.lang.Throwable ex) {
    }
    
    /**
     * 获取崩溃日志文件列表
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.io.File> getCrashLogFiles() {
        return null;
    }
    
    /**
     * 清理旧的崩溃日志文件
     */
    public final void cleanOldCrashLogs(int maxFiles) {
    }
    
    @kotlin.Metadata(mv = {1, 8, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\b\u001a\u00020\u0007R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0006\u001a\u0004\u0018\u00010\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\t"}, d2 = {"Lcom/sdwu/kotlin/utils/CrashHandler$Companion;", "", "()V", "CRASH_LOG_DIR", "", "TAG", "instance", "Lcom/sdwu/kotlin/utils/CrashHandler;", "getInstance", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.sdwu.kotlin.utils.CrashHandler getInstance() {
            return null;
        }
    }
}