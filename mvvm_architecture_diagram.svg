<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 22px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 13px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 11px; fill: #7f8c8d; }
      .code-text { font-family: 'Courier New', monospace; font-size: 10px; fill: #2c3e50; }
      
      .view-box { fill: #e8f5e8; stroke: #27ae60; stroke-width: 3; }
      .viewmodel-box { fill: #e3f2fd; stroke: #2196f3; stroke-width: 3; }
      .model-box { fill: #fff3e0; stroke: #ff9800; stroke-width: 3; }
      .data-box { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 2; }
      .repo-box { fill: #fce4ec; stroke: #e91e63; stroke-width: 2; }
      .db-box { fill: #f1f8e9; stroke: #8bc34a; stroke-width: 2; }
      
      .flow-arrow { stroke: #e74c3c; stroke-width: 3; fill: none; marker-end: url(#redarrow); }
      .data-arrow { stroke: #3498db; stroke-width: 2; fill: none; marker-end: url(#bluearrow); }
      .dependency { stroke: #95a5a6; stroke-width: 1; stroke-dasharray: 5,5; fill: none; }
    </style>
    <marker id="redarrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c" />
    </marker>
    <marker id="bluearrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#3498db" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="800" y="30" text-anchor="middle" class="title">MVVM架构实现 - Kotlin Android项目</text>
  
  <!-- View层 -->
  <g id="view-layer">
    <rect x="50" y="70" width="1500" height="150" class="view-box" rx="10"/>
    <text x="800" y="95" text-anchor="middle" class="subtitle">View层 (Compose UI组件)</text>
    
    <!-- HomeScreen -->
    <rect x="80" y="120" width="300" height="80" fill="white" stroke="#27ae60" stroke-width="2" rx="5"/>
    <text x="230" y="140" text-anchor="middle" class="text">HomeScreen.kt</text>
    <text x="90" y="160" class="code-text">@Composable fun HomeScreen() {</text>
    <text x="90" y="175" class="code-text">  val uiState by viewModel.uiState.collectAsState()</text>
    <text x="90" y="190" class="code-text">  // UI组件渲染</text>
    
    <!-- ProfileScreen -->
    <rect x="400" y="120" width="300" height="80" fill="white" stroke="#27ae60" stroke-width="2" rx="5"/>
    <text x="550" y="140" text-anchor="middle" class="text">ProfileScreen.kt</text>
    <text x="410" y="160" class="code-text">@Composable fun ProfileScreen() {</text>
    <text x="410" y="175" class="code-text">  val uiState by viewModel.uiState.collectAsState()</text>
    <text x="410" y="190" class="code-text">  // 用户信息展示和编辑</text>
    
    <!-- SettingsScreen -->
    <rect x="720" y="120" width="300" height="80" fill="white" stroke="#27ae60" stroke-width="2" rx="5"/>
    <text x="870" y="140" text-anchor="middle" class="text">SettingsScreen.kt</text>
    <text x="730" y="160" class="code-text">@Composable fun SettingsScreen() {</text>
    <text x="730" y="175" class="code-text">  val uiState by viewModel.uiState.collectAsState()</text>
    <text x="730" y="190" class="code-text">  // 设置选项和开关</text>
    
    <!-- DetailScreen -->
    <rect x="1040" y="120" width="300" height="80" fill="white" stroke="#27ae60" stroke-width="2" rx="5"/>
    <text x="1190" y="140" text-anchor="middle" class="text">DetailScreen.kt</text>
    <text x="1050" y="160" class="code-text">@Composable fun DetailScreen() {</text>
    <text x="1050" y="175" class="code-text">  val uiState by viewModel.uiState.collectAsState()</text>
    <text x="1050" y="190" class="code-text">  // 详情内容展示</text>
  </g>
  
  <!-- ViewModel层 -->
  <g id="viewmodel-layer">
    <rect x="50" y="250" width="1500" height="150" class="viewmodel-box" rx="10"/>
    <text x="800" y="275" text-anchor="middle" class="subtitle">ViewModel层 (业务逻辑和状态管理)</text>
    
    <!-- HomeViewModel -->
    <rect x="80" y="300" width="300" height="80" fill="white" stroke="#2196f3" stroke-width="2" rx="5"/>
    <text x="230" y="320" text-anchor="middle" class="text">HomeViewModel.kt</text>
    <text x="90" y="340" class="code-text">class HomeViewModel(homeRepository) {</text>
    <text x="90" y="355" class="code-text">  val uiState: StateFlow&lt;HomeUiState&gt;</text>
    <text x="90" y="370" class="code-text">  fun loadHomeItems(), searchItems()</text>
    
    <!-- ProfileViewModel -->
    <rect x="400" y="300" width="300" height="80" fill="white" stroke="#2196f3" stroke-width="2" rx="5"/>
    <text x="550" y="320" text-anchor="middle" class="text">ProfileViewModel.kt</text>
    <text x="410" y="340" class="code-text">class ProfileViewModel(userRepository) {</text>
    <text x="410" y="355" class="code-text">  val uiState: StateFlow&lt;ProfileUiState&gt;</text>
    <text x="410" y="370" class="code-text">  fun updateUserInfo(), enterEditMode()</text>
    
    <!-- SettingsViewModel -->
    <rect x="720" y="300" width="300" height="80" fill="white" stroke="#2196f3" stroke-width="2" rx="5"/>
    <text x="870" y="320" text-anchor="middle" class="text">SettingsViewModel.kt</text>
    <text x="730" y="340" class="code-text">class SettingsViewModel(settingsRepository) {</text>
    <text x="730" y="355" class="code-text">  val uiState: StateFlow&lt;SettingsUiState&gt;</text>
    <text x="730" y="370" class="code-text">  fun toggleDarkMode(), toggleNotifications()</text>
    
    <!-- DetailViewModel -->
    <rect x="1040" y="300" width="300" height="80" fill="white" stroke="#2196f3" stroke-width="2" rx="5"/>
    <text x="1190" y="320" text-anchor="middle" class="text">DetailViewModel.kt</text>
    <text x="1050" y="340" class="code-text">class DetailViewModel(homeRepository) {</text>
    <text x="1050" y="355" class="code-text">  val uiState: StateFlow&lt;DetailUiState&gt;</text>
    <text x="1050" y="370" class="code-text">  fun loadItemDetail(), toggleFavorite()</text>
  </g>
  
  <!-- Model层 -->
  <g id="model-layer">
    <rect x="50" y="430" width="1500" height="150" class="model-box" rx="10"/>
    <text x="800" y="455" text-anchor="middle" class="subtitle">Model层 (数据模型和Repository)</text>
    
    <!-- Repository层 -->
    <rect x="80" y="480" width="350" height="80" fill="white" stroke="#ff9800" stroke-width="2" rx="5"/>
    <text x="255" y="500" text-anchor="middle" class="text">Repository层</text>
    <text x="90" y="520" class="code-text">UserRepository(userDao)</text>
    <text x="90" y="535" class="code-text">HomeRepository()</text>
    <text x="90" y="550" class="code-text">SettingsRepository(context)</text>
    
    <!-- 数据模型 -->
    <rect x="450" y="480" width="350" height="80" fill="white" stroke="#ff9800" stroke-width="2" rx="5"/>
    <text x="625" y="500" text-anchor="middle" class="text">数据模型</text>
    <text x="460" y="520" class="code-text">data class User, HomeItem</text>
    <text x="460" y="535" class="code-text">data class UserSettings, ItemDetail</text>
    <text x="460" y="550" class="code-text">UI状态: HomeUiState, ProfileUiState</text>
    
    <!-- 依赖注入 -->
    <rect x="820" y="480" width="350" height="80" fill="white" stroke="#ff9800" stroke-width="2" rx="5"/>
    <text x="995" y="500" text-anchor="middle" class="text">依赖注入</text>
    <text x="830" y="520" class="code-text">AppContainer(context)</text>
    <text x="830" y="535" class="code-text">KotlinApplication</text>
    <text x="830" y="550" class="code-text">管理依赖关系</text>
    
    <!-- 导航系统 -->
    <rect x="1190" y="480" width="350" height="80" fill="white" stroke="#ff9800" stroke-width="2" rx="5"/>
    <text x="1365" y="500" text-anchor="middle" class="text">导航系统</text>
    <text x="1200" y="520" class="code-text">NavGraph, Routes</text>
    <text x="1200" y="535" class="code-text">NavigationHelper</text>
    <text x="1200" y="550" class="code-text">页面路由管理</text>
  </g>
  
  <!-- 数据层 -->
  <g id="data-layer">
    <rect x="50" y="610" width="1500" height="150" class="data-box" rx="10"/>
    <text x="800" y="635" text-anchor="middle" class="subtitle">数据层 (持久化和数据源)</text>
    
    <!-- Room数据库 -->
    <rect x="80" y="660" width="350" height="80" fill="white" stroke="#9c27b0" stroke-width="2" rx="5"/>
    <text x="255" y="680" text-anchor="middle" class="text">Room数据库</text>
    <text x="90" y="700" class="code-text">@Database AppDatabase</text>
    <text x="90" y="715" class="code-text">@Dao UserDao</text>
    <text x="90" y="730" class="code-text">@Entity User</text>
    
    <!-- DataStore -->
    <rect x="450" y="660" width="350" height="80" fill="white" stroke="#9c27b0" stroke-width="2" rx="5"/>
    <text x="625" y="680" text-anchor="middle" class="text">DataStore</text>
    <text x="460" y="700" class="code-text">Preferences DataStore</text>
    <text x="460" y="715" class="code-text">用户设置存储</text>
    <text x="460" y="730" class="code-text">键值对存储</text>
    
    <!-- 内存数据 -->
    <rect x="820" y="660" width="350" height="80" fill="white" stroke="#9c27b0" stroke-width="2" rx="5"/>
    <text x="995" y="680" text-anchor="middle" class="text">内存数据</text>
    <text x="830" y="700" class="code-text">模拟数据源</text>
    <text x="830" y="715" class="code-text">HomeItem列表</text>
    <text x="830" y="730" class="code-text">临时数据存储</text>
    
    <!-- 网络层(预留) -->
    <rect x="1190" y="660" width="350" height="80" fill="white" stroke="#9c27b0" stroke-width="2" rx="5"/>
    <text x="1365" y="680" text-anchor="middle" class="text">网络层(预留)</text>
    <text x="1200" y="700" class="code-text">Retrofit API</text>
    <text x="1200" y="715" class="code-text">远程数据源</text>
    <text x="1200" y="730" class="code-text">网络请求</text>
  </g>
  
  <!-- 数据流箭头 -->
  <g id="data-flow">
    <!-- View到ViewModel -->
    <line x1="230" y1="220" x2="230" y2="250" class="flow-arrow"/>
    <line x1="550" y1="220" x2="550" y2="250" class="flow-arrow"/>
    <line x1="870" y1="220" x2="870" y2="250" class="flow-arrow"/>
    <line x1="1190" y1="220" x2="1190" y2="250" class="flow-arrow"/>
    
    <!-- ViewModel到Repository -->
    <line x1="230" y1="400" x2="255" y2="430" class="data-arrow"/>
    <line x1="550" y1="400" x2="255" y2="430" class="data-arrow"/>
    <line x1="870" y1="400" x2="255" y2="430" class="data-arrow"/>
    <line x1="1190" y1="400" x2="255" y2="430" class="data-arrow"/>
    
    <!-- Repository到数据层 -->
    <line x1="255" y1="580" x2="255" y2="610" class="data-arrow"/>
    <line x1="255" y1="580" x2="625" y2="610" class="data-arrow"/>
    <line x1="255" y1="580" x2="995" y2="610" class="data-arrow"/>
  </g>
  
  <!-- 架构说明 -->
  <g id="explanation">
    <rect x="50" y="790" width="750" height="180" fill="none" stroke="#bdc3c7" stroke-width="2" rx="10"/>
    <text x="425" y="815" text-anchor="middle" class="subtitle">MVVM架构优势</text>
    
    <text x="70" y="840" class="text">1. 关注点分离：</text>
    <text x="80" y="860" class="small-text">• View: 只负责UI展示和用户交互</text>
    <text x="80" y="875" class="small-text">• ViewModel: 处理业务逻辑和状态管理</text>
    <text x="80" y="890" class="small-text">• Model: 数据模型和数据访问</text>
    
    <text x="70" y="915" class="text">2. 数据绑定：</text>
    <text x="80" y="935" class="small-text">• StateFlow实现响应式数据绑定</text>
    <text x="80" y="950" class="small-text">• UI自动响应数据变化</text>
    
    <text x="400" y="840" class="text">3. 生命周期感知：</text>
    <text x="410" y="860" class="small-text">• ViewModel在配置变更时保持状态</text>
    <text x="410" y="875" class="small-text">• 自动处理生命周期</text>
    
    <text x="400" y="900" class="text">4. 可测试性：</text>
    <text x="410" y="920" class="small-text">• 业务逻辑与UI分离</text>
    <text x="410" y="935" class="small-text">• 易于单元测试</text>
    <text x="410" y="950" class="small-text">• Mock数据源</text>
  </g>
  
  <!-- 技术栈 -->
  <g id="tech-stack">
    <rect x="820" y="790" width="730" height="180" fill="none" stroke="#bdc3c7" stroke-width="2" rx="10"/>
    <text x="1185" y="815" text-anchor="middle" class="subtitle">技术栈实现</text>
    
    <text x="840" y="840" class="text">核心组件：</text>
    <text x="850" y="860" class="small-text">• Jetpack Compose - 声明式UI</text>
    <text x="850" y="875" class="small-text">• ViewModel + StateFlow - 状态管理</text>
    <text x="850" y="890" class="small-text">• Room Database - 本地数据库</text>
    <text x="850" y="905" class="small-text">• DataStore - 设置存储</text>
    <text x="850" y="920" class="small-text">• Navigation Compose - 导航</text>
    
    <text x="1200" y="840" class="text">架构模式：</text>
    <text x="1210" y="860" class="small-text">• Repository Pattern - 数据访问</text>
    <text x="1210" y="875" class="small-text">• Dependency Injection - 依赖管理</text>
    <text x="1210" y="890" class="small-text">• Observer Pattern - 数据观察</text>
    <text x="1210" y="905" class="small-text">• Single Activity - 架构简化</text>
    <text x="1210" y="920" class="small-text">• Clean Architecture - 分层设计</text>
  </g>
  
  <!-- 图例 -->
  <text x="1450" y="850" class="small-text">实线箭头: 用户交互流</text>
  <text x="1450" y="865" class="small-text">蓝色箭头: 数据流</text>
  <text x="1450" y="880" class="small-text">虚线: 依赖关系</text>
</svg>
