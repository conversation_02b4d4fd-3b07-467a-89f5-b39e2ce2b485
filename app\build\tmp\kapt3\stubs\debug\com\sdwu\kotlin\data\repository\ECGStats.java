package com.sdwu.kotlin.data.repository;

/**
 * ECG统计数据
 * 用于首页展示
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0012\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\b\u0087\b\u0018\u00002\u00020\u0001B5\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0003\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\u0006\u0010\t\u001a\u00020\n\u00a2\u0006\u0002\u0010\u000bJ\t\u0010\u0015\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0016\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0017\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0018\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\bH\u00c6\u0003J\t\u0010\u001a\u001a\u00020\nH\u00c6\u0003JE\u0010\u001b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00032\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\nH\u00c6\u0001J\u0013\u0010\u001c\u001a\u00020\u001d2\b\u0010\u001e\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001f\u001a\u00020\u0003H\u00d6\u0001J\t\u0010 \u001a\u00020!H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\rR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\rR\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\r\u00a8\u0006\""}, d2 = {"Lcom/sdwu/kotlin/data/repository/ECGStats;", "", "averageHeartRate", "", "minHeartRate", "maxHeartRate", "totalRecordings", "lastRecordingTime", "", "signalQuality", "Lcom/sdwu/kotlin/data/model/ECGQuality;", "(IIIIJLcom/sdwu/kotlin/data/model/ECGQuality;)V", "getAverageHeartRate", "()I", "getLastRecordingTime", "()J", "getMaxHeartRate", "getMinHeartRate", "getSignalQuality", "()Lcom/sdwu/kotlin/data/model/ECGQuality;", "getTotalRecordings", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "", "other", "hashCode", "toString", "", "app_debug"})
public final class ECGStats {
    private final int averageHeartRate = 0;
    private final int minHeartRate = 0;
    private final int maxHeartRate = 0;
    private final int totalRecordings = 0;
    private final long lastRecordingTime = 0L;
    @org.jetbrains.annotations.NotNull()
    private final com.sdwu.kotlin.data.model.ECGQuality signalQuality = null;
    
    public ECGStats(int averageHeartRate, int minHeartRate, int maxHeartRate, int totalRecordings, long lastRecordingTime, @org.jetbrains.annotations.NotNull()
    com.sdwu.kotlin.data.model.ECGQuality signalQuality) {
        super();
    }
    
    public final int getAverageHeartRate() {
        return 0;
    }
    
    public final int getMinHeartRate() {
        return 0;
    }
    
    public final int getMaxHeartRate() {
        return 0;
    }
    
    public final int getTotalRecordings() {
        return 0;
    }
    
    public final long getLastRecordingTime() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.sdwu.kotlin.data.model.ECGQuality getSignalQuality() {
        return null;
    }
    
    public final int component1() {
        return 0;
    }
    
    public final int component2() {
        return 0;
    }
    
    public final int component3() {
        return 0;
    }
    
    public final int component4() {
        return 0;
    }
    
    public final long component5() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.sdwu.kotlin.data.model.ECGQuality component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.sdwu.kotlin.data.repository.ECGStats copy(int averageHeartRate, int minHeartRate, int maxHeartRate, int totalRecordings, long lastRecordingTime, @org.jetbrains.annotations.NotNull()
    com.sdwu.kotlin.data.model.ECGQuality signalQuality) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}