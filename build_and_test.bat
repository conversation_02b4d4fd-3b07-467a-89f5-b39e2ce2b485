@echo off
echo ========================================
echo 个人资料页面闪退问题诊断脚本
echo ========================================

echo.
echo 1. 清理项目...
call gradlew clean

echo.
echo 2. 编译项目...
call gradlew assembleDebug
if %ERRORLEVEL% neq 0 (
    echo 编译失败！请检查错误信息。
    pause
    exit /b 1
)

echo.
echo 3. 安装应用到设备...
call gradlew installDebug
if %ERRORLEVEL% neq 0 (
    echo 安装失败！请确保设备已连接并启用USB调试。
    pause
    exit /b 1
)

echo.
echo ========================================
echo 编译和安装完成！
echo ========================================
echo.
echo 接下来请：
echo 1. 打开Android Studio的Logcat
echo 2. 过滤标签：ProfileScreen 或 ErrorLogger
echo 3. 启动应用并点击"个人资料"按钮
echo 4. 观察日志输出，查找错误信息
echo.
echo 如果仍然闪退，请查看以下日志标签：
echo - ProfileScreen
echo - ProfileViewModel  
echo - UserRepository
echo - ErrorLogger
echo - CrashHandler
echo.
pause
