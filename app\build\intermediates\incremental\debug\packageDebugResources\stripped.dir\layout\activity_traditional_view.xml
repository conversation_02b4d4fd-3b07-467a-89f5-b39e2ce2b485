<?xml version="1.0" encoding="utf-8"?>
<!-- 传统View系统布局示例 -->
<!-- 展示典型的View层级结构 -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@android:color/white">

    <!-- 标题区域 -->
    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="传统View系统"
        android:textSize="24sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="24dp"
        android:textColor="@android:color/black" />

    <!-- 内容显示区域 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginBottom="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- 内容文本 -->
            <TextView
                android:id="@+id/tv_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="内容区域"
                android:textSize="16sp"
                android:padding="16dp"
                android:background="@android:color/darker_gray"
                android:textColor="@android:color/white"
                android:minHeight="120dp"
                android:gravity="top" />

            <!-- 进度条 -->
            <ProgressBar
                android:id="@+id/progress_bar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="16dp"
                android:visibility="gone" />

            <!-- 错误信息 -->
            <TextView
                android:id="@+id/tv_error"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="错误信息"
                android:textColor="@android:color/holo_red_dark"
                android:textSize="14sp"
                android:padding="12dp"
                android:background="@android:color/holo_red_light"
                android:layout_marginTop="16dp"
                android:visibility="gone" />

        </LinearLayout>
    </ScrollView>

    <!-- 按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center">

        <!-- 加载按钮 -->
        <Button
            android:id="@+id/btn_load"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="加载数据"
            android:layout_marginEnd="8dp"
            android:textSize="16sp" />

        <!-- 保存按钮 -->
        <Button
            android:id="@+id/btn_save"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="保存数据"
            android:layout_marginStart="8dp"
            android:textSize="16sp" />

    </LinearLayout>

    <!-- 说明文本 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="这是传统View系统示例，展示了命令式UI的特点：\n• 需要手动findViewById\n• 需要手动管理UI状态\n• 需要明确告诉每个View如何更新"
        android:textSize="12sp"
        android:textColor="@android:color/darker_gray"
        android:layout_marginTop="16dp"
        android:padding="8dp"
        android:background="@android:color/background_light" />

</LinearLayout>
