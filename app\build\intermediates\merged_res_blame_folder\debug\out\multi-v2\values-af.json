{"logs": [{"outputFile": "com.sdwu.kotlin.app-mergeDebugResources-70:/values-af/values-af.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1691789794c9b474ae1fde820463afff\\transformed\\material-1.9.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,274,355,435,513,608,696,796,910,991,1055,1143,1209,1272,1358,1420,1481,1539,1605,1668,1723,1841,1898,1960,2015,2084,2203,2291,2374,2513,2596,2677,2764,2822,2873,2939,3008,3084,3170,3244,3323,3396,3467,3554,3625,3714,3804,3876,3951,4038,4089,4156,4237,4321,4383,4447,4510,4614,4723,4819,4930,4992,5047", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,80,79,77,94,87,99,113,80,63,87,65,62,85,61,60,57,65,62,54,117,56,61,54,68,118,87,82,138,82,80,86,57,50,65,68,75,85,73,78,72,70,86,70,88,89,71,74,86,50,66,80,83,61,63,62,103,108,95,110,61,54,76", "endOffsets": "269,350,430,508,603,691,791,905,986,1050,1138,1204,1267,1353,1415,1476,1534,1600,1663,1718,1836,1893,1955,2010,2079,2198,2286,2369,2508,2591,2672,2759,2817,2868,2934,3003,3079,3165,3239,3318,3391,3462,3549,3620,3709,3799,3871,3946,4033,4084,4151,4232,4316,4378,4442,4505,4609,4718,4814,4925,4987,5042,5119"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,48,50,51,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2996,3077,3157,3235,3330,3418,3518,3632,4329,4469,4557,4790,4853,4939,5001,5062,5120,5186,5249,5304,5422,5479,5541,5596,5665,5784,5872,5955,6094,6177,6258,6345,6403,6454,6520,6589,6665,6751,6825,6904,6977,7048,7135,7206,7295,7385,7457,7532,7619,7670,7737,7818,7902,7964,8028,8091,8195,8304,8400,8511,8573,8933", "endLines": "5,33,34,35,36,37,38,39,40,48,50,51,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,108", "endColumns": "12,80,79,77,94,87,99,113,80,63,87,65,62,85,61,60,57,65,62,54,117,56,61,54,68,118,87,82,138,82,80,86,57,50,65,68,75,85,73,78,72,70,86,70,88,89,71,74,86,50,66,80,83,61,63,62,103,108,95,110,61,54,76", "endOffsets": "319,3072,3152,3230,3325,3413,3513,3627,3708,4388,4552,4618,4848,4934,4996,5057,5115,5181,5244,5299,5417,5474,5536,5591,5660,5779,5867,5950,6089,6172,6253,6340,6398,6449,6515,6584,6660,6746,6820,6899,6972,7043,7130,7201,7290,7380,7452,7527,7614,7665,7732,7813,7897,7959,8023,8086,8190,8299,8395,8506,8568,8623,9005"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8d5d80ec9ea4b92e6a394c21abf0511b\\transformed\\core-1.9.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "113", "startColumns": "4", "startOffsets": "9321", "endColumns": "100", "endOffsets": "9417"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bc227b5d8c8bab74364961e342d51b40\\transformed\\appcompat-1.6.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,309,415,500,603,721,798,874,965,1058,1153,1247,1346,1439,1534,1633,1728,1822,1903,2010,2115,2212,2320,2423,2525,2679,2777", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "208,304,410,495,598,716,793,869,960,1053,1148,1242,1341,1434,1529,1628,1723,1817,1898,2005,2110,2207,2315,2418,2520,2674,2772,2853"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "324,432,528,634,719,822,940,1017,1093,1184,1277,1372,1466,1565,1658,1753,1852,1947,2041,2122,2229,2334,2431,2539,2642,2744,2898,9169", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "427,523,629,714,817,935,1012,1088,1179,1272,1367,1461,1560,1653,1748,1847,1942,2036,2117,2224,2329,2426,2534,2637,2739,2893,2991,9245"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2e1e0a755749a1238f97434281347ef6\\transformed\\ui-1.4.2\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,288,385,486,572,648,739,829,915,979,1044,1122,1203,1274,1355,1425", "endColumns": "95,86,96,100,85,75,90,89,85,63,64,77,80,70,80,69,119", "endOffsets": "196,283,380,481,567,643,734,824,910,974,1039,1117,1198,1269,1350,1420,1540"}, "to": {"startLines": "41,42,44,45,47,52,53,104,105,106,107,109,110,112,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3713,3809,3972,4069,4243,4623,4699,8628,8718,8804,8868,9010,9088,9250,9422,9503,9573", "endColumns": "95,86,96,100,85,75,90,89,85,63,64,77,80,70,80,69,119", "endOffsets": "3804,3891,4064,4165,4324,4694,4785,8713,8799,8863,8928,9083,9164,9316,9498,9568,9688"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b599f6252b24759b04dd323019160ed1\\transformed\\material3-1.0.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,131,204", "endColumns": "75,72,75", "endOffsets": "126,199,275"}, "to": {"startLines": "43,46,49", "startColumns": "4,4,4", "startOffsets": "3896,4170,4393", "endColumns": "75,72,75", "endOffsets": "3967,4238,4464"}}]}]}