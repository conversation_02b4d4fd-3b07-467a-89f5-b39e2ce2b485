package com.sdwu.kotlin.utils;

/**
 * Compose专用的导航辅助工具
 * 提供与Compose兼容的安全导航功能
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, xi = 48, d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u00c7\u0002\u0018\u00002\u00020\u0001:\u0001 B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u001a\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\u0004H\u0007JP\u0010\n\u001a\u00020\u00062\u0006\u0010\u000b\u001a\u00020\u00042\u0010\b\u0002\u0010\f\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\r2\u0010\b\u0002\u0010\u000e\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\r2\u001a\b\u0002\u0010\u000f\u001a\u0014\u0012\b\u0012\u00060\u0011j\u0002`\u0012\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0010H\u0007JL\u0010\u0013\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\u0014\u001a\u00020\u00042\b\b\u0002\u0010\u0015\u001a\u00020\u00042\u0010\b\u0002\u0010\u0016\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\r2\u0016\b\u0002\u0010\u0017\u001a\u0010\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0010H\u0007JD\u0010\u0018\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\u0015\u001a\u00020\u00042\u0010\b\u0002\u0010\u0016\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\r2\u0016\b\u0002\u0010\u0017\u001a\u0010\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0010H\u0007J\u0016\u0010\u0019\u001a\u00020\u001a2\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\u0014\u001a\u00020\u0004J(\u0010\u001b\u001a\u00020\u00062\u0006\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\u0014\u001a\u00020\u00042\b\b\u0002\u0010\u0015\u001a\u00020\u0004J\u0012\u0010\u001e\u001a\u00020\u001d2\b\b\u0002\u0010\u001f\u001a\u00020\u0004H\u0007R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006!"}, d2 = {"Lcom/sdwu/kotlin/utils/ComposeNavigationHelper;", "", "()V", "TAG", "", "MonitorNavigationState", "", "navController", "Landroidx/navigation/NavController;", "tag", "MonitorPageLoad", "pageName", "onLoadStart", "Lkotlin/Function0;", "onLoadComplete", "onLoadError", "Lkotlin/Function1;", "Ljava/lang/Exception;", "Lkotlin/Exception;", "SafeNavigate", "route", "from", "onSuccess", "onError", "SafePopBack", "isNavigationSafe", "", "navigateWithState", "navigationState", "Lcom/sdwu/kotlin/utils/ComposeNavigationHelper$NavigationState;", "rememberNavigationState", "initialRoute", "NavigationState", "app_debug"})
public final class ComposeNavigationHelper {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "ComposeNavigationHelper";
    @org.jetbrains.annotations.NotNull()
    public static final com.sdwu.kotlin.utils.ComposeNavigationHelper INSTANCE = null;
    
    private ComposeNavigationHelper() {
        super();
    }
    
    /**
     * 在Compose中安全地执行导航操作
     * @param navController 导航控制器
     * @param route 目标路由
     * @param from 来源页面
     * @param onSuccess 导航成功回调
     * @param onError 导航失败回调
     */
    @androidx.compose.runtime.Composable()
    public final void SafeNavigate(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    java.lang.String route, @org.jetbrains.annotations.NotNull()
    java.lang.String from, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> onSuccess, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onError) {
    }
    
    /**
     * 在Compose中安全地执行返回操作
     * @param navController 导航控制器
     * @param from 来源页面
     * @param onSuccess 返回成功回调
     * @param onError 返回失败回调
     */
    @androidx.compose.runtime.Composable()
    public final void SafePopBack(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    java.lang.String from, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> onSuccess, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onError) {
    }
    
    /**
     * 监控导航状态变化
     * @param navController 导航控制器
     * @param tag 日志标签
     */
    @androidx.compose.runtime.Composable()
    public final void MonitorNavigationState(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    java.lang.String tag) {
    }
    
    /**
     * 监控页面加载
     * @param pageName 页面名称
     * @param onLoadStart 加载开始回调
     * @param onLoadComplete 加载完成回调
     * @param onLoadError 加载错误回调
     */
    @androidx.compose.runtime.Composable()
    public final void MonitorPageLoad(@org.jetbrains.annotations.NotNull()
    java.lang.String pageName, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> onLoadStart, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> onLoadComplete, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super java.lang.Exception, kotlin.Unit> onLoadError) {
    }
    
    /**
     * 创建导航状态
     * @param initialRoute 初始路由
     * @return 导航状态
     */
    @androidx.compose.runtime.Composable()
    @org.jetbrains.annotations.NotNull()
    public final com.sdwu.kotlin.utils.ComposeNavigationHelper.NavigationState rememberNavigationState(@org.jetbrains.annotations.NotNull()
    java.lang.String initialRoute) {
        return null;
    }
    
    /**
     * 执行带状态的导航
     * @param navigationState 导航状态
     * @param navController 导航控制器
     * @param route 目标路由
     * @param from 来源页面
     */
    public final void navigateWithState(@org.jetbrains.annotations.NotNull()
    com.sdwu.kotlin.utils.ComposeNavigationHelper.NavigationState navigationState, @org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    java.lang.String route, @org.jetbrains.annotations.NotNull()
    java.lang.String from) {
    }
    
    /**
     * 检查导航是否安全
     * @param navController 导航控制器
     * @param route 目标路由
     * @return 是否安全
     */
    public final boolean isNavigationSafe(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    java.lang.String route) {
        return false;
    }
    
    /**
     * 导航状态数据类
     */
    @kotlin.Metadata(mv = {1, 8, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0012\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\u001f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\b\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0002\u0010\u0007J\t\u0010\u0011\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010\u0013\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J)\u0010\u0014\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010\u0015\u001a\u00020\u00052\b\u0010\u0016\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0017\u001a\u00020\u0018H\u00d6\u0001J\t\u0010\u0019\u001a\u00020\u0003H\u00d6\u0001R\u001a\u0010\u0002\u001a\u00020\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\b\u0010\t\"\u0004\b\n\u0010\u000bR\u001a\u0010\u0004\u001a\u00020\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0004\u0010\f\"\u0004\b\r\u0010\u000eR\u001c\u0010\u0006\u001a\u0004\u0018\u00010\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u000f\u0010\t\"\u0004\b\u0010\u0010\u000b\u00a8\u0006\u001a"}, d2 = {"Lcom/sdwu/kotlin/utils/ComposeNavigationHelper$NavigationState;", "", "currentRoute", "", "isNavigating", "", "lastError", "(Ljava/lang/String;ZLjava/lang/String;)V", "getCurrentRoute", "()Ljava/lang/String;", "setCurrentRoute", "(Ljava/lang/String;)V", "()Z", "setNavigating", "(Z)V", "getLastError", "setLastError", "component1", "component2", "component3", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
    public static final class NavigationState {
        @org.jetbrains.annotations.NotNull()
        private java.lang.String currentRoute;
        private boolean isNavigating;
        @org.jetbrains.annotations.Nullable()
        private java.lang.String lastError;
        
        public NavigationState(@org.jetbrains.annotations.NotNull()
        java.lang.String currentRoute, boolean isNavigating, @org.jetbrains.annotations.Nullable()
        java.lang.String lastError) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getCurrentRoute() {
            return null;
        }
        
        public final void setCurrentRoute(@org.jetbrains.annotations.NotNull()
        java.lang.String p0) {
        }
        
        public final boolean isNavigating() {
            return false;
        }
        
        public final void setNavigating(boolean p0) {
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String getLastError() {
            return null;
        }
        
        public final void setLastError(@org.jetbrains.annotations.Nullable()
        java.lang.String p0) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        public final boolean component2() {
            return false;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String component3() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.sdwu.kotlin.utils.ComposeNavigationHelper.NavigationState copy(@org.jetbrains.annotations.NotNull()
        java.lang.String currentRoute, boolean isNavigating, @org.jetbrains.annotations.Nullable()
        java.lang.String lastError) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}