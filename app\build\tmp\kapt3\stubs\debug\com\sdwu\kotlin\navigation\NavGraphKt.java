package com.sdwu.kotlin.navigation;

@kotlin.Metadata(mv = {1, 8, 0}, k = 2, xi = 48, d1 = {"\u0000\u000e\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a\u0010\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\u0007\u00a8\u0006\u0004"}, d2 = {"NavGraph", "", "navController", "Landroidx/navigation/NavHostController;", "app_debug"})
public final class NavGraphKt {
    
    /**
     * 应用导航图
     * 定义所有页面的导航路由和页面组件
     */
    @androidx.compose.runtime.Composable()
    public static final void NavGraph(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavHostController navController) {
    }
}