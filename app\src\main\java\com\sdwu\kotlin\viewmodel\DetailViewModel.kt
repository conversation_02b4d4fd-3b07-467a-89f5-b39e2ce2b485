package com.sdwu.kotlin.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.sdwu.kotlin.data.model.ItemDetail
import com.sdwu.kotlin.data.repository.HomeRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
/**
 * 详情页ViewModel
 * 管理详情页的业务逻辑和UI状态
 */
class DetailViewModel(
    private val homeRepository: HomeRepository
) : ViewModel() {
    
    // UI状态
    private val _uiState = MutableStateFlow(DetailUiState())
    val uiState: StateFlow<DetailUiState> = _uiState.asStateFlow()
    
    /**
     * 加载详情数据
     */
    fun loadItemDetail(itemId: String?) {
        if (itemId.isNullOrBlank()) {
            _uiState.value = _uiState.value.copy(
                error = "无效的项目ID"
            )
            return
        }
        
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            try {
                val detail = homeRepository.getItemDetail(itemId)
                
                if (detail != null) {
                    _uiState.value = _uiState.value.copy(
                        itemDetail = detail,
                        isLoading = false,
                        error = null
                    )
                } else {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "未找到相关项目信息"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "加载详情失败"
                )
            }
        }
    }
    
    /**
     * 刷新详情数据
     */
    fun refreshDetail() {
        val currentDetail = _uiState.value.itemDetail
        if (currentDetail != null) {
            loadItemDetail(currentDetail.id)
        }
    }
    
    /**
     * 切换收藏状态
     */
    fun toggleFavorite() {
        val currentDetail = _uiState.value.itemDetail ?: return
        
        viewModelScope.launch {
            try {
                // 这里可以调用Repository的收藏相关方法
                // 目前只是更新UI状态
                _uiState.value = _uiState.value.copy(
                    isFavorite = !_uiState.value.isFavorite
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = e.message ?: "操作失败"
                )
            }
        }
    }
    
    /**
     * 分享项目
     */
    fun shareItem() {
        val currentDetail = _uiState.value.itemDetail ?: return
        
        viewModelScope.launch {
            try {
                // 这里可以实现分享逻辑
                // 目前只是显示分享成功消息
                _uiState.value = _uiState.value.copy(
                    showShareSuccess = true
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = e.message ?: "分享失败"
                )
            }
        }
    }
    
    /**
     * 隐藏分享成功消息
     */
    fun hideShareSuccess() {
        _uiState.value = _uiState.value.copy(showShareSuccess = false)
    }
    
    /**
     * 清除错误状态
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
    
    /**
     * 重置状态
     */
    fun resetState() {
        _uiState.value = DetailUiState()
    }
}

/**
 * 详情页UI状态数据类
 */
data class DetailUiState(
    val itemDetail: ItemDetail? = null,
    val isLoading: Boolean = false,
    val error: String? = null,
    val isFavorite: Boolean = false,
    val showShareSuccess: Boolean = false
)
