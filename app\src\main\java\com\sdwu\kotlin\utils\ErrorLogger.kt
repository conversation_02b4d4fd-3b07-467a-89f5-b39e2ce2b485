package com.sdwu.kotlin.utils

import android.util.Log
import java.io.PrintWriter
import java.io.StringWriter

/**
 * 错误日志工具类
 * 提供统一的错误日志记录和调试功能
 */
object ErrorLogger {
    
    private const val TAG = "ErrorLogger"
    
    /**
     * 记录错误信息
     */
    fun logError(tag: String, message: String, throwable: Throwable? = null) {
        Log.e(tag, message, throwable)
        
        // 打印详细的堆栈信息
        throwable?.let {
            val sw = StringWriter()
            val pw = PrintWriter(sw)
            it.printStackTrace(pw)
            Log.e(tag, "详细堆栈信息: $sw")
        }
    }
    
    /**
     * 记录调试信息
     */
    fun logDebug(tag: String, message: String) {
        Log.d(tag, message)
    }
    
    /**
     * 记录警告信息
     */
    fun logWarning(tag: String, message: String) {
        Log.w(tag, message)
    }
    
    /**
     * 记录信息
     */
    fun logInfo(tag: String, message: String) {
        Log.i(tag, message)
    }
    
    /**
     * 记录详细信息（仅在调试模式下）
     */
    fun logVerbose(tag: String, message: String) {
        Log.v(tag, message)
    }
    
    /**
     * 记录方法进入
     */
    fun logMethodEnter(tag: String, methodName: String) {
        Log.d(tag, "进入方法: $methodName")
    }
    
    /**
     * 记录方法退出
     */
    fun logMethodExit(tag: String, methodName: String) {
        Log.d(tag, "退出方法: $methodName")
    }
    
    /**
     * 记录方法执行时间
     */
    fun logMethodDuration(tag: String, methodName: String, startTime: Long) {
        val duration = System.currentTimeMillis() - startTime
        Log.d(tag, "方法 $methodName 执行时间: ${duration}ms")
    }
    
    /**
     * 记录对象状态
     */
    fun logObjectState(tag: String, objectName: String, state: Any?) {
        Log.d(tag, "$objectName 状态: $state")
    }
    
    /**
     * 记录导航事件
     */
    fun logNavigation(tag: String, from: String, to: String) {
        Log.d(tag, "导航: $from -> $to")
    }
    
    /**
     * 记录数据库操作
     */
    fun logDatabaseOperation(tag: String, operation: String, table: String, result: String? = null) {
        val message = if (result != null) {
            "数据库操作: $operation on $table, 结果: $result"
        } else {
            "数据库操作: $operation on $table"
        }
        Log.d(tag, message)
    }
    
    /**
     * 记录网络请求
     */
    fun logNetworkRequest(tag: String, url: String, method: String, statusCode: Int? = null) {
        val message = if (statusCode != null) {
            "网络请求: $method $url, 状态码: $statusCode"
        } else {
            "网络请求: $method $url"
        }
        Log.d(tag, message)
    }
    
    /**
     * 记录用户操作
     */
    fun logUserAction(tag: String, action: String, details: String? = null) {
        val message = if (details != null) {
            "用户操作: $action, 详情: $details"
        } else {
            "用户操作: $action"
        }
        Log.d(tag, message)
    }
    
    /**
     * 记录性能指标
     */
    fun logPerformance(tag: String, metric: String, value: Any) {
        Log.d(tag, "性能指标: $metric = $value")
    }
    
    /**
     * 记录内存使用情况
     */
    fun logMemoryUsage(tag: String) {
        val runtime = Runtime.getRuntime()
        val usedMemory = runtime.totalMemory() - runtime.freeMemory()
        val maxMemory = runtime.maxMemory()
        val availableMemory = maxMemory - usedMemory
        
        Log.d(tag, "内存使用情况:")
        Log.d(tag, "  已使用: ${usedMemory / 1024 / 1024}MB")
        Log.d(tag, "  最大可用: ${maxMemory / 1024 / 1024}MB")
        Log.d(tag, "  剩余可用: ${availableMemory / 1024 / 1024}MB")
    }
}
