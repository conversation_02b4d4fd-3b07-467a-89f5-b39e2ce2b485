{"logs": [{"outputFile": "com.sdwu.kotlin.app-mergeDebugResources-70:/values-lt/values-lt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1691789794c9b474ae1fde820463afff\\transformed\\material-1.9.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,375,454,532,615,709,799,895,1013,1097,1163,1262,1340,1405,1515,1578,1650,1709,1783,1844,1898,2022,2083,2145,2199,2277,2411,2499,2583,2724,2803,2887,2984,3040,3094,3160,3235,3314,3402,3478,3556,3629,3706,3793,3874,3964,4056,4128,4209,4301,4356,4422,4507,4594,4656,4720,4783,4894,5009,5110,5224,5284,5342", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69", "endColumns": "12,78,77,82,93,89,95,117,83,65,98,77,64,109,62,71,58,73,60,53,123,60,61,53,77,133,87,83,140,78,83,96,55,53,65,74,78,87,75,77,72,76,86,80,89,91,71,80,91,54,65,84,86,61,63,62,110,114,100,113,59,57,81", "endOffsets": "370,449,527,610,704,794,890,1008,1092,1158,1257,1335,1400,1510,1573,1645,1704,1778,1839,1893,2017,2078,2140,2194,2272,2406,2494,2578,2719,2798,2882,2979,3035,3089,3155,3230,3309,3397,3473,3551,3624,3701,3788,3869,3959,4051,4123,4204,4296,4351,4417,4502,4589,4651,4715,4778,4889,5004,5105,5219,5279,5337,5419"}, "to": {"startLines": "2,35,36,37,38,39,40,41,42,50,52,53,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3194,3273,3351,3434,3528,3618,3714,3832,4549,4693,4792,5038,5103,5213,5276,5348,5407,5481,5542,5596,5720,5781,5843,5897,5975,6109,6197,6281,6422,6501,6585,6682,6738,6792,6858,6933,7012,7100,7176,7254,7327,7404,7491,7572,7662,7754,7826,7907,7999,8054,8120,8205,8292,8354,8418,8481,8592,8707,8808,8922,8982,9350", "endLines": "7,35,36,37,38,39,40,41,42,50,52,53,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,110", "endColumns": "12,78,77,82,93,89,95,117,83,65,98,77,64,109,62,71,58,73,60,53,123,60,61,53,77,133,87,83,140,78,83,96,55,53,65,74,78,87,75,77,72,76,86,80,89,91,71,80,91,54,65,84,86,61,63,62,110,114,100,113,59,57,81", "endOffsets": "420,3268,3346,3429,3523,3613,3709,3827,3911,4610,4787,4865,5098,5208,5271,5343,5402,5476,5537,5591,5715,5776,5838,5892,5970,6104,6192,6276,6417,6496,6580,6677,6733,6787,6853,6928,7007,7095,7171,7249,7322,7399,7486,7567,7657,7749,7821,7902,7994,8049,8115,8200,8287,8349,8413,8476,8587,8702,8803,8917,8977,9035,9427"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b599f6252b24759b04dd323019160ed1\\transformed\\material3-1.0.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,133,213", "endColumns": "77,79,77", "endOffsets": "128,208,286"}, "to": {"startLines": "45,48,51", "startColumns": "4,4,4", "startOffsets": "4093,4374,4615", "endColumns": "77,79,77", "endOffsets": "4166,4449,4688"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bc227b5d8c8bab74364961e342d51b40\\transformed\\appcompat-1.6.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,325,438,525,627,749,832,912,1006,1102,1199,1295,1398,1494,1592,1688,1782,1876,1959,2068,2176,2276,2386,2491,2597,2773,2874", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "216,320,433,520,622,744,827,907,1001,1097,1194,1290,1393,1489,1587,1683,1777,1871,1954,2063,2171,2271,2381,2486,2592,2768,2869,2953"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,113", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "425,541,645,758,845,947,1069,1152,1232,1326,1422,1519,1615,1718,1814,1912,2008,2102,2196,2279,2388,2496,2596,2706,2811,2917,3093,9606", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "536,640,753,840,942,1064,1147,1227,1321,1417,1514,1610,1713,1809,1907,2003,2097,2191,2274,2383,2491,2591,2701,2806,2912,3088,3189,9685"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2e1e0a755749a1238f97434281347ef6\\transformed\\ui-1.4.2\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,380,485,580,657,748,835,919,989,1058,1144,1232,1307,1387,1470", "endColumns": "92,83,97,104,94,76,90,86,83,69,68,85,87,74,79,82,121", "endOffsets": "193,277,375,480,575,652,743,830,914,984,1053,1139,1227,1302,1382,1465,1587"}, "to": {"startLines": "43,44,46,47,49,54,55,106,107,108,109,111,112,114,116,117,118", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3916,4009,4171,4269,4454,4870,4947,9040,9127,9211,9281,9432,9518,9690,9866,9946,10029", "endColumns": "92,83,97,104,94,76,90,86,83,69,68,85,87,74,79,82,121", "endOffsets": "4004,4088,4264,4369,4544,4942,5033,9122,9206,9276,9345,9513,9601,9760,9941,10024,10146"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8d5d80ec9ea4b92e6a394c21abf0511b\\transformed\\core-1.9.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "115", "startColumns": "4", "startOffsets": "9765", "endColumns": "100", "endOffsets": "9861"}}]}]}