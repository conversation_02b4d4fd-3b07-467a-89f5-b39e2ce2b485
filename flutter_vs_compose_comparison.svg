<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义渐变色 -->
    <linearGradient id="flutterGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#02569B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0175C2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="composeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6200EA;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3700B3;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="sharedGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF9800;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F57C00;stop-opacity:1" />
    </linearGradient>
    
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
            refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="3" dy="3" stdDeviation="4" flood-color="#00000025"/>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="1600" height="1200" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="800" y="35" text-anchor="middle" font-family="Arial, sans-serif" 
        font-size="28" font-weight="bold" fill="#333">
    🚀 Flutter vs Jetpack Compose：现代声明式UI框架对比
  </text>
  
  <!-- Flutter 部分 (左侧) -->
  <g id="flutter-section">
    <!-- 标题 -->
    <rect x="50" y="70" width="700" height="45" fill="url(#flutterGradient)" rx="8" filter="url(#shadow)"/>
    <text x="400" y="100" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="20" font-weight="bold" fill="white">
      🐦 Flutter (Google - 跨平台)
    </text>
    
    <!-- 平台信息 -->
    <rect x="70" y="130" width="660" height="40" fill="#E3F2FD" stroke="#02569B" stroke-width="2" rx="5"/>
    <text x="90" y="155" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#01579B">
      平台：iOS + Android + Web + Desktop + Embedded (Dart语言)
    </text>
    
    <!-- 架构特点 -->
    <rect x="70" y="185" width="660" height="100" fill="#E3F2FD" stroke="#02569B" stroke-width="2" rx="5"/>
    <text x="90" y="210" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#01579B">
      架构特点
    </text>
    <text x="90" y="230" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • 自绘引擎：Skia渲染引擎，完全控制每个像素
    </text>
    <text x="90" y="245" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • Widget树：一切皆Widget，组合式UI构建
    </text>
    <text x="90" y="260" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • 热重载：毫秒级代码更新，极速开发体验
    </text>
    <text x="90" y="275" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • 单一代码库：一套代码运行多个平台
    </text>
    
    <!-- 状态管理 -->
    <rect x="70" y="300" width="660" height="85" fill="#E3F2FD" stroke="#02569B" stroke-width="2" rx="5"/>
    <text x="90" y="325" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#01579B">
      状态管理
    </text>
    <text x="90" y="345" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • StatefulWidget/StatelessWidget：内置状态管理
    </text>
    <text x="90" y="360" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • Provider/Riverpod/BLoC：第三方状态管理方案
    </text>
    <text x="90" y="375" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • setState()：简单状态更新机制
    </text>
    
    <!-- 代码示例 -->
    <rect x="70" y="400" width="660" height="120" fill="#E3F2FD" stroke="#02569B" stroke-width="2" rx="5"/>
    <text x="90" y="425" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#01579B">
      代码示例 (Dart)
    </text>
    <text x="90" y="445" font-family="Courier New, monospace" font-size="11" fill="#333">
      class UserProfile extends StatelessWidget {
    </text>
    <text x="90" y="460" font-family="Courier New, monospace" font-size="11" fill="#333">
        Widget build(BuildContext context) {
    </text>
    <text x="90" y="475" font-family="Courier New, monospace" font-size="11" fill="#333">
          return Column(children: [
    </text>
    <text x="90" y="490" font-family="Courier New, monospace" font-size="11" fill="#333">
            Text(user.name),
    </text>
    <text x="90" y="505" font-family="Courier New, monospace" font-size="11" fill="#333">
            if (isLoading) CircularProgressIndicator()
    </text>
    <text x="90" y="520" font-family="Courier New, monospace" font-size="11" fill="#333">
          ]);
    </text>
    
    <!-- 优势 -->
    <rect x="70" y="535" width="660" height="120" fill="#E3F2FD" stroke="#02569B" stroke-width="2" rx="5"/>
    <text x="90" y="560" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#01579B">
      核心优势
    </text>
    <text x="90" y="580" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • 🌍 真正的跨平台：一套代码，多端运行
    </text>
    <text x="90" y="595" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • 🎨 像素级控制：自绘引擎，UI完全一致
    </text>
    <text x="90" y="610" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • ⚡ 高性能：60fps流畅动画，接近原生性能
    </text>
    <text x="90" y="625" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • 🔥 热重载：极速开发迭代
    </text>
    <text x="90" y="640" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • 📦 丰富生态：pub.dev上万个包
    </text>
  </g>
  
  <!-- Jetpack Compose 部分 (右侧) -->
  <g id="compose-section">
    <!-- 标题 -->
    <rect x="850" y="70" width="700" height="45" fill="url(#composeGradient)" rx="8" filter="url(#shadow)"/>
    <text x="1200" y="100" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="20" font-weight="bold" fill="white">
      🤖 Jetpack Compose (Google - Android)
    </text>
    
    <!-- 平台信息 -->
    <rect x="870" y="130" width="660" height="40" fill="#F3E5F5" stroke="#6200EA" stroke-width="2" rx="5"/>
    <text x="890" y="155" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#3700B3">
      平台：Android Native + Multiplatform (Kotlin语言)
    </text>
    
    <!-- 架构特点 -->
    <rect x="870" y="185" width="660" height="100" fill="#F3E5F5" stroke="#6200EA" stroke-width="2" rx="5"/>
    <text x="890" y="210" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#3700B3">
      架构特点
    </text>
    <text x="890" y="230" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • 原生渲染：直接使用Android Canvas，原生性能
    </text>
    <text x="890" y="245" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • @Composable函数：函数式UI组合
    </text>
    <text x="890" y="260" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • 智能重组：只更新变化的UI部分
    </text>
    <text x="890" y="275" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • 与Android深度集成：无缝使用Android API
    </text>
    
    <!-- 状态管理 -->
    <rect x="870" y="300" width="660" height="85" fill="#F3E5F5" stroke="#6200EA" stroke-width="2" rx="5"/>
    <text x="890" y="325" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#3700B3">
      状态管理
    </text>
    <text x="890" y="345" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • State/MutableState：内置响应式状态
    </text>
    <text x="890" y="360" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • StateFlow/LiveData：与ViewModel集成
    </text>
    <text x="890" y="375" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • remember：跨重组保持状态
    </text>
    
    <!-- 代码示例 -->
    <rect x="870" y="400" width="660" height="120" fill="#F3E5F5" stroke="#6200EA" stroke-width="2" rx="5"/>
    <text x="890" y="425" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#3700B3">
      代码示例 (Kotlin)
    </text>
    <text x="890" y="445" font-family="Courier New, monospace" font-size="11" fill="#333">
      @Composable
    </text>
    <text x="890" y="460" font-family="Courier New, monospace" font-size="11" fill="#333">
      fun UserProfile(viewModel: ProfileViewModel) {
    </text>
    <text x="890" y="475" font-family="Courier New, monospace" font-size="11" fill="#333">
          val user by viewModel.user.collectAsState()
    </text>
    <text x="890" y="490" font-family="Courier New, monospace" font-size="11" fill="#333">
          Column {
    </text>
    <text x="890" y="505" font-family="Courier New, monospace" font-size="11" fill="#333">
              Text(text = user?.name ?: "")
    </text>
    <text x="890" y="520" font-family="Courier New, monospace" font-size="11" fill="#333">
              if (isLoading) CircularProgressIndicator()
    </text>
    
    <!-- 优势 -->
    <rect x="870" y="535" width="660" height="120" fill="#F3E5F5" stroke="#6200EA" stroke-width="2" rx="5"/>
    <text x="890" y="560" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#3700B3">
      核心优势
    </text>
    <text x="890" y="580" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • 🤖 Android原生：完美的Android集成
    </text>
    <text x="890" y="595" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • ⚡ 极致性能：原生渲染，智能重组
    </text>
    <text x="890" y="610" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • 🔧 类型安全：Kotlin强类型系统
    </text>
    <text x="890" y="625" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • 🏗️ 现代架构：与Jetpack库完美配合
    </text>
    <text x="890" y="640" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • 📱 Material Design：原生Material 3支持
    </text>
  </g>

  <!-- 详细对比表格 -->
  <g id="comparison-table">
    <rect x="100" y="680" width="1400" height="320" fill="#FFFFFF" stroke="#DDD" stroke-width="2" rx="8" filter="url(#shadow)"/>
    <text x="800" y="710" text-anchor="middle" font-family="Arial, sans-serif"
          font-size="20" font-weight="bold" fill="#333">
      📊 Flutter vs Compose 详细对比
    </text>

    <!-- 表格头部 -->
    <rect x="120" y="730" width="200" height="40" fill="#F5F5F5" stroke="#DDD"/>
    <rect x="320" y="730" width="560" height="40" fill="#E3F2FD" stroke="#DDD"/>
    <rect x="880" y="730" width="600" height="40" fill="#F3E5F5" stroke="#DDD"/>

    <text x="220" y="755" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#333">对比维度</text>
    <text x="600" y="755" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#02569B">Flutter</text>
    <text x="1180" y="755" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#6200EA">Jetpack Compose</text>

    <!-- 表格内容行 -->
    <g id="table-rows">
      <!-- 第1行：跨平台支持 -->
      <rect x="120" y="770" width="200" height="35" fill="#FAFAFA" stroke="#DDD"/>
      <rect x="320" y="770" width="560" height="35" fill="#FAFAFA" stroke="#DDD"/>
      <rect x="880" y="770" width="600" height="35" fill="#FAFAFA" stroke="#DDD"/>
      <text x="130" y="792" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">跨平台支持</text>
      <text x="330" y="792" font-family="Arial, sans-serif" font-size="12" fill="#333">⭐⭐⭐⭐⭐ iOS + Android + Web + Desktop + Embedded</text>
      <text x="890" y="792" font-family="Arial, sans-serif" font-size="12" fill="#333">⭐⭐⭐ Android + Multiplatform (实验性)</text>

      <!-- 第2行：性能 -->
      <rect x="120" y="805" width="200" height="35" fill="#FFFFFF" stroke="#DDD"/>
      <rect x="320" y="805" width="560" height="35" fill="#FFFFFF" stroke="#DDD"/>
      <rect x="880" y="805" width="600" height="35" fill="#FFFFFF" stroke="#DDD"/>
      <text x="130" y="827" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">性能表现</text>
      <text x="330" y="827" font-family="Arial, sans-serif" font-size="12" fill="#333">⭐⭐⭐⭐ 接近原生，Skia渲染引擎</text>
      <text x="890" y="827" font-family="Arial, sans-serif" font-size="12" fill="#333">⭐⭐⭐⭐⭐ 原生性能，智能重组优化</text>

      <!-- 第3行：开发语言 -->
      <rect x="120" y="840" width="200" height="35" fill="#FAFAFA" stroke="#DDD"/>
      <rect x="320" y="840" width="560" height="35" fill="#FAFAFA" stroke="#DDD"/>
      <rect x="880" y="840" width="600" height="35" fill="#FAFAFA" stroke="#DDD"/>
      <text x="130" y="862" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">开发语言</text>
      <text x="330" y="862" font-family="Arial, sans-serif" font-size="12" fill="#333">Dart (Google设计的现代语言)</text>
      <text x="890" y="862" font-family="Arial, sans-serif" font-size="12" fill="#333">Kotlin (JetBrains开发，Google官方推荐)</text>

      <!-- 第4行：学习曲线 -->
      <rect x="120" y="875" width="200" height="35" fill="#FFFFFF" stroke="#DDD"/>
      <rect x="320" y="875" width="560" height="35" fill="#FFFFFF" stroke="#DDD"/>
      <rect x="880" y="875" width="600" height="35" fill="#FFFFFF" stroke="#DDD"/>
      <text x="130" y="897" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">学习曲线</text>
      <text x="330" y="897" font-family="Arial, sans-serif" font-size="12" fill="#333">⭐⭐⭐ 需要学习Dart语言和Flutter概念</text>
      <text x="890" y="897" font-family="Arial, sans-serif" font-size="12" fill="#333">⭐⭐ Android开发者容易上手</text>

      <!-- 第5行：生态系统 -->
      <rect x="120" y="910" width="200" height="35" fill="#FAFAFA" stroke="#DDD"/>
      <rect x="320" y="910" width="560" height="35" fill="#FAFAFA" stroke="#DDD"/>
      <rect x="880" y="910" width="600" height="35" fill="#FAFAFA" stroke="#DDD"/>
      <text x="130" y="932" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">生态系统</text>
      <text x="330" y="932" font-family="Arial, sans-serif" font-size="12" fill="#333">⭐⭐⭐⭐⭐ pub.dev 丰富的包生态</text>
      <text x="890" y="932" font-family="Arial, sans-serif" font-size="12" fill="#333">⭐⭐⭐ 快速发展中，与Android生态集成</text>

      <!-- 第6行：UI一致性 -->
      <rect x="120" y="945" width="200" height="35" fill="#FFFFFF" stroke="#DDD"/>
      <rect x="320" y="945" width="560" height="35" fill="#FFFFFF" stroke="#DDD"/>
      <rect x="880" y="945" width="600" height="35" fill="#FFFFFF" stroke="#DDD"/>
      <text x="130" y="967" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">UI一致性</text>
      <text x="330" y="967" font-family="Arial, sans-serif" font-size="12" fill="#333">⭐⭐⭐⭐⭐ 跨平台完全一致</text>
      <text x="890" y="967" font-family="Arial, sans-serif" font-size="12" fill="#333">⭐⭐⭐⭐ Android平台内完美一致</text>
    </g>
  </g>

  <!-- 使用场景推荐 -->
  <g id="use-cases">
    <rect x="100" y="1020" width="1400" height="140" fill="#F8F9FA" stroke="#DDD" stroke-width="2" rx="8" filter="url(#shadow)"/>
    <text x="800" y="1050" text-anchor="middle" font-family="Arial, sans-serif"
          font-size="18" font-weight="bold" fill="#333">
      🎯 选择建议
    </text>

    <!-- Flutter使用场景 -->
    <rect x="120" y="1070" width="660" height="80" fill="#E3F2FD" stroke="#02569B" stroke-width="2" rx="5"/>
    <text x="450" y="1090" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#02569B">
      🐦 选择 Flutter 当你需要
    </text>
    <text x="130" y="1108" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 真正的跨平台开发：一套代码，多端运行
    </text>
    <text x="130" y="1122" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 快速MVP开发：热重载，快速迭代
    </text>
    <text x="130" y="1136" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 统一的UI体验：所有平台保持一致的设计
    </text>

    <!-- Compose使用场景 -->
    <rect x="820" y="1070" width="660" height="80" fill="#F3E5F5" stroke="#6200EA" stroke-width="2" rx="5"/>
    <text x="1150" y="1090" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#6200EA">
      🤖 选择 Compose 当你需要
    </text>
    <text x="830" y="1108" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • Android专业开发：深度集成Android生态
    </text>
    <text x="830" y="1122" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 极致性能：原生性能，复杂动画
    </text>
    <text x="830" y="1136" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 现有Android项目：渐进式迁移到现代UI
    </text>
  </g>

  <!-- 连接线和箭头 -->
  <g id="connections">
    <!-- 对比箭头 -->
    <line x1="750" y1="400" x2="870" y2="400" stroke="#666" stroke-width="3" stroke-dasharray="8,4"/>
    <text x="810" y="395" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#666">
      VS
    </text>

    <!-- 流程箭头 -->
    <line x1="400" y1="170" x2="400" y2="180" stroke="#02569B" stroke-width="3" marker-end="url(#arrowhead)"/>
    <line x1="400" y1="285" x2="400" y2="295" stroke="#02569B" stroke-width="3" marker-end="url(#arrowhead)"/>
    <line x1="400" y1="385" x2="400" y2="395" stroke="#02569B" stroke-width="3" marker-end="url(#arrowhead)"/>
    <line x1="400" y1="520" x2="400" y2="530" stroke="#02569B" stroke-width="3" marker-end="url(#arrowhead)"/>

    <line x1="1200" y1="170" x2="1200" y2="180" stroke="#6200EA" stroke-width="3" marker-end="url(#arrowhead)"/>
    <line x1="1200" y1="285" x2="1200" y2="295" stroke="#6200EA" stroke-width="3" marker-end="url(#arrowhead)"/>
    <line x1="1200" y1="385" x2="1200" y2="395" stroke="#6200EA" stroke-width="3" marker-end="url(#arrowhead)"/>
    <line x1="1200" y1="520" x2="1200" y2="530" stroke="#6200EA" stroke-width="3" marker-end="url(#arrowhead)"/>
  </g>
</svg>
