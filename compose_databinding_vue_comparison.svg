<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义渐变色 -->
    <linearGradient id="composeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6200EA;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3700B3;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="dataBindingGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2E7D32;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="vueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4FC08D;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#42B883;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="sharedGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF9800;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F57C00;stop-opacity:1" />
    </linearGradient>

    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7"
            refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>

    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="3" dy="3" stdDeviation="4" flood-color="#00000025"/>
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="1600" height="1200" fill="#f8f9fa"/>

  <!-- 标题 -->
  <text x="800" y="35" text-anchor="middle" font-family="Arial, sans-serif"
        font-size="28" font-weight="bold" fill="#333">
    🌐 跨平台UI框架对比：Jetpack Compose vs DataBinding vs Vue.js
  </text>

  <!-- Jetpack Compose 部分 (左侧) -->
  <g id="compose-section">
    <!-- 标题 -->
    <rect x="50" y="70" width="480" height="45" fill="url(#composeGradient)" rx="8" filter="url(#shadow)"/>
    <text x="290" y="100" text-anchor="middle" font-family="Arial, sans-serif"
          font-size="18" font-weight="bold" fill="white">
      🚀 Jetpack Compose (Android)
    </text>

    <!-- 平台信息 -->
    <rect x="70" y="130" width="440" height="35" fill="#F3E5F5" stroke="#6200EA" stroke-width="2" rx="5"/>
    <text x="90" y="152" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#3700B3">
      平台：Android Native (Kotlin)
    </text>

    <!-- 架构特点 -->
    <rect x="70" y="180" width="440" height="85" fill="#F3E5F5" stroke="#6200EA" stroke-width="2" rx="5"/>
    <text x="90" y="200" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#3700B3">
      架构特点
    </text>
    <text x="90" y="218" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 声明式UI：@Composable函数描述UI状态
    </text>
    <text x="90" y="233" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 函数式编程：UI = f(state)
    </text>
    <text x="90" y="248" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 智能重组：只更新变化的UI部分
    </text>

    <!-- 状态管理 -->
    <rect x="70" y="280" width="440" height="85" fill="#F3E5F5" stroke="#6200EA" stroke-width="2" rx="5"/>
    <text x="90" y="300" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#3700B3">
      状态管理
    </text>
    <text x="90" y="318" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • State/MutableState：本地状态
    </text>
    <text x="90" y="333" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • StateFlow.collectAsState()：全局状态
    </text>
    <text x="90" y="348" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • remember：跨重组保持状态
    </text>

    <!-- 代码示例 -->
    <rect x="70" y="380" width="440" height="100" fill="#F3E5F5" stroke="#6200EA" stroke-width="2" rx="5"/>
    <text x="90" y="400" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#3700B3">
      代码示例
    </text>
    <text x="90" y="418" font-family="Courier New, monospace" font-size="10" fill="#333">
      @Composable
    </text>
    <text x="90" y="432" font-family="Courier New, monospace" font-size="10" fill="#333">
      fun UserProfile(viewModel: ProfileViewModel) {
    </text>
    <text x="90" y="446" font-family="Courier New, monospace" font-size="10" fill="#333">
          val user by viewModel.user.collectAsState()
    </text>
    <text x="90" y="460" font-family="Courier New, monospace" font-size="10" fill="#333">
          Text(text = user?.name ?: "Loading...")
    </text>
    <text x="90" y="474" font-family="Courier New, monospace" font-size="10" fill="#333">
      }
    </text>
  </g>

  <!-- DataBinding 部分 (中间) -->
  <g id="databinding-section">
    <!-- 标题 -->
    <rect x="560" y="70" width="480" height="45" fill="url(#dataBindingGradient)" rx="8" filter="url(#shadow)"/>
    <text x="800" y="100" text-anchor="middle" font-family="Arial, sans-serif"
          font-size="18" font-weight="bold" fill="white">
      📋 DataBinding (Android)
    </text>

    <!-- 平台信息 -->
    <rect x="580" y="130" width="440" height="35" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2" rx="5"/>
    <text x="600" y="152" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2E7D32">
      平台：Android Native (XML + Kotlin)
    </text>

    <!-- 架构特点 -->
    <rect x="580" y="180" width="440" height="85" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2" rx="5"/>
    <text x="600" y="200" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2E7D32">
      架构特点
    </text>
    <text x="600" y="218" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 混合模式：XML模板 + 绑定表达式
    </text>
    <text x="600" y="233" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 命令式UI：通过绑定连接数据和视图
    </text>
    <text x="600" y="248" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 编译时生成：自动生成绑定类
    </text>

    <!-- 状态管理 -->
    <rect x="580" y="280" width="440" height="85" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2" rx="5"/>
    <text x="600" y="300" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2E7D32">
      状态管理
    </text>
    <text x="600" y="318" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • LiveData：自动观察数据变化
    </text>
    <text x="600" y="333" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • Observable Fields：双向数据绑定
    </text>
    <text x="600" y="348" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • lifecycleOwner：生命周期感知
    </text>

    <!-- 代码示例 -->
    <rect x="580" y="380" width="440" height="100" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2" rx="5"/>
    <text x="600" y="400" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2E7D32">
      代码示例
    </text>
    <text x="600" y="418" font-family="Courier New, monospace" font-size="10" fill="#333">
      &lt;TextView android:text="@{viewModel.user.name}"
    </text>
    <text x="600" y="432" font-family="Courier New, monospace" font-size="10" fill="#333">
                android:visibility="@{viewModel.isLoading
    </text>
    <text x="600" y="446" font-family="Courier New, monospace" font-size="10" fill="#333">
                ? View.VISIBLE : View.GONE}" /&gt;
    </text>
    <text x="600" y="460" font-family="Courier New, monospace" font-size="10" fill="#333">
      // Kotlin: binding.viewModel = viewModel
    </text>
    <text x="600" y="474" font-family="Courier New, monospace" font-size="10" fill="#333">
      // binding.lifecycleOwner = this
    </text>
  </g>

  <!-- Vue.js 部分 (右侧) -->
  <g id="vue-section">
    <!-- 标题 -->
    <rect x="1070" y="70" width="480" height="45" fill="url(#vueGradient)" rx="8" filter="url(#shadow)"/>
    <text x="1310" y="100" text-anchor="middle" font-family="Arial, sans-serif"
          font-size="18" font-weight="bold" fill="white">
      🌿 Vue.js (Web)
    </text>

    <!-- 平台信息 -->
    <rect x="1090" y="130" width="440" height="35" fill="#E8F8F5" stroke="#4FC08D" stroke-width="2" rx="5"/>
    <text x="1110" y="152" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2C5530">
      平台：Web Browser (HTML + JavaScript/TypeScript)
    </text>

    <!-- 架构特点 -->
    <rect x="1090" y="180" width="440" height="85" fill="#E8F8F5" stroke="#4FC08D" stroke-width="2" rx="5"/>
    <text x="1110" y="200" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2C5530">
      架构特点
    </text>
    <text x="1110" y="218" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 渐进式框架：可逐步采用
    </text>
    <text x="1110" y="233" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 模板语法：HTML + 指令系统
    </text>
    <text x="1110" y="248" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 虚拟DOM：高效的DOM更新机制
    </text>

    <!-- 状态管理 -->
    <rect x="1090" y="280" width="440" height="85" fill="#E8F8F5" stroke="#4FC08D" stroke-width="2" rx="5"/>
    <text x="1110" y="300" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2C5530">
      状态管理
    </text>
    <text x="1110" y="318" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • ref/reactive：响应式数据
    </text>
    <text x="1110" y="333" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • computed：计算属性
    </text>
    <text x="1110" y="348" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • Pinia/Vuex：全局状态管理
    </text>

    <!-- 代码示例 -->
    <rect x="1090" y="380" width="440" height="100" fill="#E8F8F5" stroke="#4FC08D" stroke-width="2" rx="5"/>
    <text x="1110" y="400" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2C5530">
      代码示例
    </text>
    <text x="1110" y="418" font-family="Courier New, monospace" font-size="10" fill="#333">
      &lt;template&gt;
    </text>
    <text x="1110" y="432" font-family="Courier New, monospace" font-size="10" fill="#333">
        &lt;div v-if="isLoading"&gt;Loading...&lt;/div&gt;
    </text>
    <text x="1110" y="446" font-family="Courier New, monospace" font-size="10" fill="#333">
        &lt;p&gt;{{ user.name }}&lt;/p&gt;
    </text>
    <text x="1110" y="460" font-family="Courier New, monospace" font-size="10" fill="#333">
      &lt;/template&gt;
    </text>
    <text x="1110" y="474" font-family="Courier New, monospace" font-size="10" fill="#333">
      // script: const user = ref({name: 'John'})
    </text>
  </g>

  <!-- 详细对比表格 -->
  <g id="comparison-table">
    <rect x="100" y="520" width="1400" height="350" fill="#FFFFFF" stroke="#DDD" stroke-width="2" rx="8" filter="url(#shadow)"/>
    <text x="800" y="550" text-anchor="middle" font-family="Arial, sans-serif"
          font-size="20" font-weight="bold" fill="#333">
      📊 三大框架详细对比
    </text>

    <!-- 表格头部 -->
    <rect x="120" y="570" width="180" height="40" fill="#F5F5F5" stroke="#DDD"/>
    <rect x="300" y="570" width="400" height="40" fill="#F3E5F5" stroke="#DDD"/>
    <rect x="700" y="570" width="400" height="40" fill="#E8F5E8" stroke="#DDD"/>
    <rect x="1100" y="570" width="380" height="40" fill="#E8F8F5" stroke="#DDD"/>

    <text x="210" y="595" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#333">特性对比</text>
    <text x="500" y="595" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#6200EA">Jetpack Compose</text>
    <text x="900" y="595" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#4CAF50">DataBinding</text>
    <text x="1290" y="595" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#4FC08D">Vue.js</text>

    <!-- 表格内容行 -->
    <g id="table-content">
      <!-- 第1行：平台支持 -->
      <rect x="120" y="610" width="180" height="35" fill="#FAFAFA" stroke="#DDD"/>
      <rect x="300" y="610" width="400" height="35" fill="#FAFAFA" stroke="#DDD"/>
      <rect x="700" y="610" width="400" height="35" fill="#FAFAFA" stroke="#DDD"/>
      <rect x="1100" y="610" width="380" height="35" fill="#FAFAFA" stroke="#DDD"/>
      <text x="130" y="632" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">平台支持</text>
      <text x="310" y="632" font-family="Arial, sans-serif" font-size="12" fill="#333">Android Native + Multiplatform</text>
      <text x="710" y="632" font-family="Arial, sans-serif" font-size="12" fill="#333">Android Native Only</text>
      <text x="1110" y="632" font-family="Arial, sans-serif" font-size="12" fill="#333">Web + Mobile (Hybrid)</text>

      <!-- 第2行：UI构建方式 -->
      <rect x="120" y="645" width="180" height="35" fill="#FFFFFF" stroke="#DDD"/>
      <rect x="300" y="645" width="400" height="35" fill="#FFFFFF" stroke="#DDD"/>
      <rect x="700" y="645" width="400" height="35" fill="#FFFFFF" stroke="#DDD"/>
      <rect x="1100" y="645" width="380" height="35" fill="#FFFFFF" stroke="#DDD"/>
      <text x="130" y="667" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">UI构建方式</text>
      <text x="310" y="667" font-family="Arial, sans-serif" font-size="12" fill="#333">纯Kotlin @Composable函数</text>
      <text x="710" y="667" font-family="Arial, sans-serif" font-size="12" fill="#333">XML模板 + 绑定表达式</text>
      <text x="1110" y="667" font-family="Arial, sans-serif" font-size="12" fill="#333">HTML模板 + 指令系统</text>

      <!-- 第3行：状态管理 -->
      <rect x="120" y="680" width="180" height="35" fill="#FAFAFA" stroke="#DDD"/>
      <rect x="300" y="680" width="400" height="35" fill="#FAFAFA" stroke="#DDD"/>
      <rect x="700" y="680" width="400" height="35" fill="#FAFAFA" stroke="#DDD"/>
      <rect x="1100" y="680" width="380" height="35" fill="#FAFAFA" stroke="#DDD"/>
      <text x="130" y="702" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">状态管理</text>
      <text x="310" y="702" font-family="Arial, sans-serif" font-size="12" fill="#333">State/StateFlow + collectAsState()</text>
      <text x="710" y="702" font-family="Arial, sans-serif" font-size="12" fill="#333">LiveData + Observable Fields</text>
      <text x="1110" y="702" font-family="Arial, sans-serif" font-size="12" fill="#333">ref/reactive + computed</text>

      <!-- 第4行：性能 -->
      <rect x="120" y="715" width="180" height="35" fill="#FFFFFF" stroke="#DDD"/>
      <rect x="300" y="715" width="400" height="35" fill="#FFFFFF" stroke="#DDD"/>
      <rect x="700" y="715" width="400" height="35" fill="#FFFFFF" stroke="#DDD"/>
      <rect x="1100" y="715" width="380" height="35" fill="#FFFFFF" stroke="#DDD"/>
      <text x="130" y="737" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">性能</text>
      <text x="310" y="737" font-family="Arial, sans-serif" font-size="12" fill="#333">⭐⭐⭐⭐⭐ 智能重组，原生性能</text>
      <text x="710" y="737" font-family="Arial, sans-serif" font-size="12" fill="#333">⭐⭐⭐⭐ 编译时优化</text>
      <text x="1110" y="737" font-family="Arial, sans-serif" font-size="12" fill="#333">⭐⭐⭐⭐ 虚拟DOM优化</text>

      <!-- 第5行：学习曲线 -->
      <rect x="120" y="750" width="180" height="35" fill="#FAFAFA" stroke="#DDD"/>
      <rect x="300" y="750" width="400" height="35" fill="#FAFAFA" stroke="#DDD"/>
      <rect x="700" y="750" width="400" height="35" fill="#FAFAFA" stroke="#DDD"/>
      <rect x="1100" y="750" width="380" height="35" fill="#FAFAFA" stroke="#DDD"/>
      <text x="130" y="772" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">学习曲线</text>
      <text x="310" y="772" font-family="Arial, sans-serif" font-size="12" fill="#333">⭐⭐⭐ 需要学习新范式</text>
      <text x="710" y="772" font-family="Arial, sans-serif" font-size="12" fill="#333">⭐⭐ 基于熟悉的XML</text>
      <text x="1110" y="772" font-family="Arial, sans-serif" font-size="12" fill="#333">⭐⭐ 渐进式学习</text>

      <!-- 第6行：生态系统 -->
      <rect x="120" y="785" width="180" height="35" fill="#FFFFFF" stroke="#DDD"/>
      <rect x="300" y="785" width="400" height="35" fill="#FFFFFF" stroke="#DDD"/>
      <rect x="700" y="785" width="400" height="35" fill="#FFFFFF" stroke="#DDD"/>
      <rect x="1100" y="785" width="380" height="35" fill="#FFFFFF" stroke="#DDD"/>
      <text x="130" y="807" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">生态系统</text>
      <text x="310" y="807" font-family="Arial, sans-serif" font-size="12" fill="#333">⭐⭐⭐ 快速发展中</text>
      <text x="710" y="807" font-family="Arial, sans-serif" font-size="12" fill="#333">⭐⭐⭐⭐ 成熟稳定</text>
      <text x="1110" y="807" font-family="Arial, sans-serif" font-size="12" fill="#333">⭐⭐⭐⭐⭐ 非常丰富</text>

      <!-- 第7行：开发体验 -->
      <rect x="120" y="820" width="180" height="35" fill="#FAFAFA" stroke="#DDD"/>
      <rect x="300" y="820" width="400" height="35" fill="#FAFAFA" stroke="#DDD"/>
      <rect x="700" y="820" width="400" height="35" fill="#FAFAFA" stroke="#DDD"/>
      <rect x="1100" y="820" width="380" height="35" fill="#FAFAFA" stroke="#DDD"/>
      <text x="130" y="842" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">开发体验</text>
      <text x="310" y="842" font-family="Arial, sans-serif" font-size="12" fill="#333">⭐⭐⭐⭐⭐ 现代化工具链</text>
      <text x="710" y="842" font-family="Arial, sans-serif" font-size="12" fill="#333">⭐⭐⭐ 传统开发模式</text>
      <text x="1110" y="842" font-family="Arial, sans-serif" font-size="12" fill="#333">⭐⭐⭐⭐⭐ 优秀的开发工具</text>
    </g>
  </g>

  <!-- 架构理念对比 -->
  <g id="architecture-comparison">
    <rect x="100" y="890" width="1400" height="120" fill="#F8F9FA" stroke="#DDD" stroke-width="2" rx="8" filter="url(#shadow)"/>
    <text x="800" y="920" text-anchor="middle" font-family="Arial, sans-serif"
          font-size="18" font-weight="bold" fill="#333">
      🏗️ 架构理念对比
    </text>

    <!-- Compose理念 -->
    <rect x="120" y="940" width="440" height="60" fill="#F3E5F5" stroke="#6200EA" stroke-width="2" rx="5"/>
    <text x="340" y="960" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#6200EA">
      Compose: 函数式声明式UI
    </text>
    <text x="130" y="978" font-family="Arial, sans-serif" font-size="12" fill="#333">
      UI = f(state) - UI是状态的纯函数
    </text>
    <text x="130" y="992" font-family="Arial, sans-serif" font-size="12" fill="#333">
      不可变状态 + 智能重组 = 高性能UI
    </text>

    <!-- DataBinding理念 -->
    <rect x="580" y="940" width="440" height="60" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2" rx="5"/>
    <text x="800" y="960" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#4CAF50">
      DataBinding: 增强式命令式UI
    </text>
    <text x="590" y="978" font-family="Arial, sans-serif" font-size="12" fill="#333">
      XML模板 + 绑定表达式 = 自动UI更新
    </text>
    <text x="590" y="992" font-family="Arial, sans-serif" font-size="12" fill="#333">
      传统View系统的现代化改进
    </text>

    <!-- Vue理念 -->
    <rect x="1040" y="940" width="440" height="60" fill="#E8F8F5" stroke="#4FC08D" stroke-width="2" rx="5"/>
    <text x="1260" y="960" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#4FC08D">
      Vue: 渐进式响应式框架
    </text>
    <text x="1050" y="978" font-family="Arial, sans-serif" font-size="12" fill="#333">
      响应式数据 + 虚拟DOM = 高效更新
    </text>
    <text x="1050" y="992" font-family="Arial, sans-serif" font-size="12" fill="#333">
      可逐步采用的现代Web框架
    </text>
  </g>

  <!-- 使用场景推荐 -->
  <g id="use-cases">
    <rect x="100" y="1030" width="1400" height="140" fill="#FFFFFF" stroke="#DDD" stroke-width="2" rx="8" filter="url(#shadow)"/>
    <text x="800" y="1060" text-anchor="middle" font-family="Arial, sans-serif"
          font-size="18" font-weight="bold" fill="#333">
      🎯 使用场景推荐
    </text>

    <!-- Compose使用场景 -->
    <rect x="120" y="1080" width="440" height="80" fill="#F3E5F5" stroke="#6200EA" stroke-width="2" rx="5"/>
    <text x="340" y="1100" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#6200EA">
      🚀 选择 Compose 当你需要
    </text>
    <text x="130" y="1118" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 新Android项目开发
    </text>
    <text x="130" y="1132" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 复杂动画和自定义UI
    </text>
    <text x="130" y="1146" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 跨平台开发需求
    </text>

    <!-- DataBinding使用场景 -->
    <rect x="580" y="1080" width="440" height="80" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2" rx="5"/>
    <text x="800" y="1100" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#4CAF50">
      📋 选择 DataBinding 当你需要
    </text>
    <text x="590" y="1118" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 维护现有Android项目
    </text>
    <text x="590" y="1132" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 复杂表单双向绑定
    </text>
    <text x="590" y="1146" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 渐进式从传统View迁移
    </text>

    <!-- Vue使用场景 -->
    <rect x="1040" y="1080" width="440" height="80" fill="#E8F8F5" stroke="#4FC08D" stroke-width="2" rx="5"/>
    <text x="1260" y="1100" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#4FC08D">
      🌿 选择 Vue 当你需要
    </text>
    <text x="1050" y="1118" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • Web应用开发
    </text>
    <text x="1050" y="1132" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 快速原型开发
    </text>
    <text x="1050" y="1146" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 丰富的生态系统支持
    </text>
  </g>

  <!-- 连接线和箭头 -->
  <g id="connections">
    <!-- 对比箭头 -->
    <line x1="530" y1="300" x2="580" y2="300" stroke="#666" stroke-width="2" stroke-dasharray="5,5"/>
    <line x1="1020" y1="300" x2="1090" y2="300" stroke="#666" stroke-width="2" stroke-dasharray="5,5"/>

    <text x="555" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#666">VS</text>
    <text x="1055" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#666">VS</text>

    <!-- 流程箭头 -->
    <line x1="290" y1="165" x2="290" y2="175" stroke="#6200EA" stroke-width="3" marker-end="url(#arrowhead)"/>
    <line x1="290" y1="265" x2="290" y2="275" stroke="#6200EA" stroke-width="3" marker-end="url(#arrowhead)"/>
    <line x1="290" y1="365" x2="290" y2="375" stroke="#6200EA" stroke-width="3" marker-end="url(#arrowhead)"/>

    <line x1="800" y1="165" x2="800" y2="175" stroke="#4CAF50" stroke-width="3" marker-end="url(#arrowhead)"/>
    <line x1="800" y1="265" x2="800" y2="275" stroke="#4CAF50" stroke-width="3" marker-end="url(#arrowhead)"/>
    <line x1="800" y1="365" x2="800" y2="375" stroke="#4CAF50" stroke-width="3" marker-end="url(#arrowhead)"/>

    <line x1="1310" y1="165" x2="1310" y2="175" stroke="#4FC08D" stroke-width="3" marker-end="url(#arrowhead)"/>
    <line x1="1310" y1="265" x2="1310" y2="275" stroke="#4FC08D" stroke-width="3" marker-end="url(#arrowhead)"/>
    <line x1="1310" y1="365" x2="1310" y2="375" stroke="#4FC08D" stroke-width="3" marker-end="url(#arrowhead)"/>
  </g>
</svg>