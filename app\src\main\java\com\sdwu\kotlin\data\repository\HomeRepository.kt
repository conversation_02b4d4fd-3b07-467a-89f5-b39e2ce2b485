package com.sdwu.kotlin.data.repository

import com.sdwu.kotlin.data.model.HomeItem
import com.sdwu.kotlin.data.model.ItemDetail
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
/**
 * 首页数据仓库
 * 管理首页列表和详情数据
 */
class HomeRepository {
    
    // 模拟数据存储
    private val homeItems = mutableListOf<HomeItem>()
    
    init {
        // 初始化模拟数据
        initializeData()
    }
    
    /**
     * 获取首页列表数据
     */
    fun getHomeItems(): Flow<List<HomeItem>> = flow {
        // 模拟网络延迟
        delay(500)
        emit(homeItems.toList())
    }
    
    /**
     * 根据ID获取详情数据
     */
    suspend fun getItemDetail(itemId: String): ItemDetail? {
        // 模拟网络延迟
        delay(300)
        
        val homeItem = homeItems.find { it.id == itemId }
        return homeItem?.let {
            ItemDetail(
                id = it.id,
                title = it.title,
                description = it.description,
                content = "这是 ${it.title} 的详细内容。在这里可以显示更多详细信息，比如描述、图片、相关数据等。\n\n" +
                        "内容包括：\n" +
                        "• 详细描述信息\n" +
                        "• 相关图片和媒体\n" +
                        "• 用户评论和反馈\n" +
                        "• 相关推荐内容",
                imageUrl = it.imageUrl,
                tags = listOf("标签1", "标签2", "推荐"),
                createdAt = it.createdAt,
                updatedAt = System.currentTimeMillis()
            )
        }
    }
    
    /**
     * 添加新项目
     */
    suspend fun addItem(title: String, description: String): HomeItem {
        val newItem = HomeItem(
            id = "item_${System.currentTimeMillis()}",
            title = title,
            description = description
        )
        homeItems.add(newItem)
        return newItem
    }
    
    /**
     * 删除项目
     */
    suspend fun deleteItem(itemId: String): Boolean {
        return homeItems.removeIf { it.id == itemId }
    }
    
    /**
     * 搜索项目
     */
    fun searchItems(query: String): Flow<List<HomeItem>> = flow {
        delay(200)
        val filteredItems = homeItems.filter { 
            it.title.contains(query, ignoreCase = true) || 
            it.description.contains(query, ignoreCase = true) 
        }
        emit(filteredItems)
    }
    
    /**
     * 初始化模拟数据
     */
    private fun initializeData() {
        homeItems.addAll(
            listOf(
                HomeItem(
                    id = "Item_1",
                    title = "Item 1",
                    description = "这是第一个项目的描述信息"
                ),
                HomeItem(
                    id = "Item_2", 
                    title = "Item 2",
                    description = "这是第二个项目的描述信息"
                ),
                HomeItem(
                    id = "Item_3",
                    title = "Item 3", 
                    description = "这是第三个项目的描述信息"
                ),
                HomeItem(
                    id = "Item_4",
                    title = "Item 4",
                    description = "这是第四个项目的描述信息"
                ),
                HomeItem(
                    id = "Item_5",
                    title = "Item 5",
                    description = "这是第五个项目的描述信息"
                )
            )
        )
    }
}
