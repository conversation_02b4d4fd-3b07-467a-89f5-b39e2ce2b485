package com.sdwu.kotlin.utils;

/**
 * 调试工具类
 * 提供各种调试和诊断功能
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0002J\u000e\u0010\t\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\u000bJ\u000e\u0010\f\u001a\u00020\u00062\u0006\u0010\r\u001a\u00020\u000eJ\u001a\u0010\u000f\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\u000b2\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000eJ\u0006\u0010\u0010\u001a\u00020\u0006J\u0006\u0010\u0011\u001a\u00020\u0006J\u000e\u0010\u0012\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\u000bR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0013"}, d2 = {"Lcom/sdwu/kotlin/utils/DebugUtils;", "", "()V", "TAG", "", "checkAppContainer", "", "appContainer", "Lcom/sdwu/kotlin/di/AppContainer;", "checkApplicationState", "context", "Landroid/content/Context;", "checkNavigationState", "navController", "Landroidx/navigation/NavController;", "generateDebugReport", "logMemoryUsage", "logThreadInfo", "testDatabaseConnection", "app_debug"})
public final class DebugUtils {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "DebugUtils";
    @org.jetbrains.annotations.NotNull()
    public static final com.sdwu.kotlin.utils.DebugUtils INSTANCE = null;
    
    private DebugUtils() {
        super();
    }
    
    /**
     * 检查应用程序状态
     */
    public final void checkApplicationState(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
    
    /**
     * 检查AppContainer状态
     */
    private final void checkAppContainer(com.sdwu.kotlin.di.AppContainer appContainer) {
    }
    
    /**
     * 检查导航状态
     */
    public final void checkNavigationState(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController) {
    }
    
    /**
     * 记录内存使用情况
     */
    public final void logMemoryUsage() {
    }
    
    /**
     * 记录线程信息
     */
    public final void logThreadInfo() {
    }
    
    /**
     * 测试数据库连接
     */
    public final void testDatabaseConnection(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
    
    /**
     * 生成调试报告
     */
    public final void generateDebugReport(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    androidx.navigation.NavController navController) {
    }
}