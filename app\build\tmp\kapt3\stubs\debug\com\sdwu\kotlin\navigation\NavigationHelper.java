package com.sdwu.kotlin.navigation;

/**
 * 导航辅助类
 * 提供常用的导航操作方法
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0000\b\u0007\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bJ\u000e\u0010\t\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bJ\u0006\u0010\n\u001a\u00020\u0006J\u000e\u0010\u000b\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bJ\u000e\u0010\f\u001a\u00020\r2\u0006\u0010\u0007\u001a\u00020\bR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000e"}, d2 = {"Lcom/sdwu/kotlin/navigation/NavigationHelper;", "", "navController", "Landroidx/navigation/NavController;", "(Landroidx/navigation/NavController;)V", "navigateAndClearStack", "", "route", "", "navigateAndReplace", "navigateToHome", "navigateWithAnimation", "safeNavigate", "", "app_debug"})
public final class NavigationHelper {
    @org.jetbrains.annotations.NotNull()
    private final androidx.navigation.NavController navController = null;
    
    public NavigationHelper(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController) {
        super();
    }
    
    /**
     * 导航到指定页面，清除返回栈
     */
    public final void navigateAndClearStack(@org.jetbrains.annotations.NotNull()
    java.lang.String route) {
    }
    
    /**
     * 导航到指定页面，替换当前页面
     */
    public final void navigateAndReplace(@org.jetbrains.annotations.NotNull()
    java.lang.String route) {
    }
    
    /**
     * 安全导航（检查目标是否存在）
     */
    public final boolean safeNavigate(@org.jetbrains.annotations.NotNull()
    java.lang.String route) {
        return false;
    }
    
    /**
     * 带动画的导航
     */
    public final void navigateWithAnimation(@org.jetbrains.annotations.NotNull()
    java.lang.String route) {
    }
    
    /**
     * 返回到首页
     */
    public final void navigateToHome() {
    }
}