package com.sdwu.kotlin.di;

/**
 * 应用依赖注入容器
 * 简化版的依赖注入，管理应用中的依赖关系
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, xi = 48, d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\b\u0007\u0018\u0000 \u001f2\u00020\u0001:\u0001\u001fB\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001b\u0010\u0005\u001a\u00020\u00068FX\u0086\u0084\u0002\u00a2\u0006\f\n\u0004\b\t\u0010\n\u001a\u0004\b\u0007\u0010\bR\u001b\u0010\u000b\u001a\u00020\f8FX\u0086\u0084\u0002\u00a2\u0006\f\n\u0004\b\u000f\u0010\n\u001a\u0004\b\r\u0010\u000eR\u001b\u0010\u0010\u001a\u00020\u00118FX\u0086\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0014\u0010\n\u001a\u0004\b\u0012\u0010\u0013R\u001b\u0010\u0015\u001a\u00020\u00168FX\u0086\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0019\u0010\n\u001a\u0004\b\u0017\u0010\u0018R\u001b\u0010\u001a\u001a\u00020\u001b8FX\u0086\u0084\u0002\u00a2\u0006\f\n\u0004\b\u001e\u0010\n\u001a\u0004\b\u001c\u0010\u001d\u00a8\u0006 "}, d2 = {"Lcom/sdwu/kotlin/di/AppContainer;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "ecgRepository", "Lcom/sdwu/kotlin/data/repository/ECGRepository;", "getEcgRepository", "()Lcom/sdwu/kotlin/data/repository/ECGRepository;", "ecgRepository$delegate", "Lkotlin/Lazy;", "homeRepository", "Lcom/sdwu/kotlin/data/repository/HomeRepository;", "getHomeRepository", "()Lcom/sdwu/kotlin/data/repository/HomeRepository;", "homeRepository$delegate", "hrvRepository", "Lcom/sdwu/kotlin/data/repository/HRVRepository;", "getHrvRepository", "()Lcom/sdwu/kotlin/data/repository/HRVRepository;", "hrvRepository$delegate", "settingsRepository", "Lcom/sdwu/kotlin/data/repository/SettingsRepository;", "getSettingsRepository", "()Lcom/sdwu/kotlin/data/repository/SettingsRepository;", "settingsRepository$delegate", "userRepository", "Lcom/sdwu/kotlin/data/repository/UserRepositoryInterface;", "getUserRepository", "()Lcom/sdwu/kotlin/data/repository/UserRepositoryInterface;", "userRepository$delegate", "Companion", "app_debug"})
public final class AppContainer {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "AppContainer";
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy userRepository$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy homeRepository$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy settingsRepository$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy ecgRepository$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy hrvRepository$delegate = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.sdwu.kotlin.di.AppContainer.Companion Companion = null;
    
    public AppContainer(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.sdwu.kotlin.data.repository.UserRepositoryInterface getUserRepository() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.sdwu.kotlin.data.repository.HomeRepository getHomeRepository() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.sdwu.kotlin.data.repository.SettingsRepository getSettingsRepository() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.sdwu.kotlin.data.repository.ECGRepository getEcgRepository() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.sdwu.kotlin.data.repository.HRVRepository getHrvRepository() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 8, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/sdwu/kotlin/di/AppContainer$Companion;", "", "()V", "TAG", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}