package com.sdwu.kotlin.components;

@kotlin.Metadata(mv = {1, 8, 0}, k = 2, xi = 48, d1 = {"\u0000D\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\u001a,\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\u0010\b\u0002\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u0006H\u0007\u001a\u001a\u0010\u0007\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u0003H\u0007\u001aP\u0010\u000b\u001a\u00020\u00012\u0006\u0010\f\u001a\u00020\u00032\u0010\b\u0002\u0010\r\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u00062\u0010\b\u0002\u0010\u000e\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u00062\u001a\b\u0002\u0010\u000f\u001a\u0014\u0012\b\u0012\u00060\u0011j\u0002`\u0012\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u0010H\u0007\u001a9\u0010\u0013\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\u0014\u001a\u00020\u00032\b\b\u0002\u0010\u0015\u001a\u00020\u00162\u0013\b\u0002\u0010\u0017\u001a\r\u0012\u0004\u0012\u00020\u00010\u0006\u00a2\u0006\u0002\b\u0018H\u0007\u001a7\u0010\u0019\u001a\u00020\u00012\b\b\u0002\u0010\u001a\u001a\u00020\u00032\u0010\b\u0002\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u00062\u0011\u0010\u0017\u001a\r\u0012\u0004\u0012\u00020\u00010\u0006\u00a2\u0006\u0002\b\u0018H\u0007\u001a>\u0010\u001b\u001a\u00020\u00012\u0006\u0010\u001c\u001a\u00020\u00032\u0006\u0010\u001d\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\u0014\u001a\u00020\u00032\b\b\u0002\u0010\u0015\u001a\u00020\u00162\b\b\u0002\u0010\u001e\u001a\u00020\u001fH\u0007\u00a8\u0006 "}, d2 = {"ErrorDisplay", "", "message", "", "details", "onRetry", "Lkotlin/Function0;", "NavigationStateMonitor", "navController", "Landroidx/navigation/NavController;", "tag", "PageLoadMonitor", "pageName", "onLoadStart", "onLoadComplete", "onLoadError", "Lkotlin/Function1;", "Ljava/lang/Exception;", "Lkotlin/Exception;", "SafeBackButton", "from", "modifier", "Landroidx/compose/ui/Modifier;", "content", "Landroidx/compose/runtime/Composable;", "SafeComposable", "errorMessage", "SafeNavigationButton", "text", "route", "enabled", "", "app_debug"})
public final class ErrorHandlingComponentsKt {
    
    /**
     * 安全的Composable包装器
     * 用于监控Composable的加载状态
     */
    @androidx.compose.runtime.Composable()
    public static final void SafeComposable(@org.jetbrains.annotations.NotNull()
    java.lang.String errorMessage, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> onRetry, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> content) {
    }
    
    /**
     * 错误显示组件
     */
    @androidx.compose.runtime.Composable()
    public static final void ErrorDisplay(@org.jetbrains.annotations.NotNull()
    java.lang.String message, @org.jetbrains.annotations.NotNull()
    java.lang.String details, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> onRetry) {
    }
    
    /**
     * 安全导航按钮
     * 包装了错误处理的导航按钮
     */
    @androidx.compose.runtime.Composable()
    public static final void SafeNavigationButton(@org.jetbrains.annotations.NotNull()
    java.lang.String text, @org.jetbrains.annotations.NotNull()
    java.lang.String route, @org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    java.lang.String from, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, boolean enabled) {
    }
    
    /**
     * 安全返回按钮
     */
    @androidx.compose.runtime.Composable()
    public static final void SafeBackButton(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    java.lang.String from, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> content) {
    }
    
    /**
     * 导航状态监控组件
     * 用于监控和记录导航状态变化
     */
    @androidx.compose.runtime.Composable()
    public static final void NavigationStateMonitor(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    java.lang.String tag) {
    }
    
    /**
     * 页面加载监控组件
     * 用于监控页面加载过程
     */
    @androidx.compose.runtime.Composable()
    public static final void PageLoadMonitor(@org.jetbrains.annotations.NotNull()
    java.lang.String pageName, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> onLoadStart, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> onLoadComplete, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super java.lang.Exception, kotlin.Unit> onLoadError) {
    }
}