package com.sdwu.kotlin

import android.app.Application
import android.util.Log
import com.sdwu.kotlin.di.AppContainer
import com.sdwu.kotlin.utils.CrashHandler
import com.sdwu.kotlin.utils.ErrorLogger

/**
 * 应用程序类
 * 初始化全局依赖和配置
 */
class KotlinApplication : Application() {

    companion object {
        private const val TAG = "KotlinApplication"

        // 静态实例，方便全局访问
        @Volatile
        private var instance: KotlinApplication? = null

        /**
         * 获取应用程序实例
         */
        fun getInstance(): KotlinApplication? = instance
    }

    // 依赖注入容器
    lateinit var appContainer: AppContainer
        private set

    // 初始化状态标志
    private var isInitialized = false

    override fun onCreate() {
        super.onCreate()
        instance = this

        ErrorLogger.logInfo(TAG, "KotlinApplication onCreate开始")

        try {
            initializeApplication()
            isInitialized = true
            ErrorLogger.logInfo(TAG, "应用程序初始化完成")
        } catch (e: Exception) {
            ErrorLogger.logError(TAG, "应用程序初始化失败", e)
            // 不重新抛出异常，避免应用崩溃
            // 可以设置一个错误状态，让其他组件知道初始化失败
        }

        ErrorLogger.logInfo(TAG, "KotlinApplication onCreate完成")
    }

    /**
     * 初始化应用程序组件
     */
    private fun initializeApplication() {
        // 初始化崩溃处理器
        initializeCrashHandler()

        // 初始化依赖注入容器
        initializeAppContainer()
    }

    /**
     * 初始化崩溃处理器
     */
    private fun initializeCrashHandler() {
        try {
            ErrorLogger.logDebug(TAG, "初始化崩溃处理器")
            CrashHandler.getInstance().init(this)

            // 清理旧的崩溃日志
            CrashHandler.getInstance().cleanOldCrashLogs()

            ErrorLogger.logInfo(TAG, "崩溃处理器初始化成功")
        } catch (e: Exception) {
            ErrorLogger.logError(TAG, "崩溃处理器初始化失败", e)
            throw e
        }
    }

    /**
     * 初始化依赖注入容器
     */
    private fun initializeAppContainer() {
        try {
            ErrorLogger.logDebug(TAG, "初始化AppContainer")
            appContainer = AppContainer(this)
            ErrorLogger.logInfo(TAG, "AppContainer初始化成功")
        } catch (e: Exception) {
            ErrorLogger.logError(TAG, "AppContainer初始化失败", e)
            throw e
        }
    }

    /**
     * 检查应用程序是否已正确初始化
     */
    fun isApplicationInitialized(): Boolean = isInitialized && ::appContainer.isInitialized

    override fun onTerminate() {
        super.onTerminate()
        ErrorLogger.logInfo(TAG, "应用程序正在终止")
        instance = null
    }
}
