package com.sdwu.kotlin.utils

import android.util.Log
import androidx.navigation.NavController
import androidx.navigation.NavDestination
import androidx.navigation.NavGraph

/**
 * 导航错误处理工具类
 * 专门处理导航相关的错误和异常
 */
object NavigationErrorHandler {
    
    private const val TAG = "NavigationErrorHandler"
    
    /**
     * 安全导航到指定路由
     * @param navController 导航控制器
     * @param route 目标路由
     * @param from 来源页面（用于日志记录）
     * @return 导航是否成功
     */
    fun safeNavigateTo(navController: NavController, route: String, from: String = "unknown"): Boolean {
        return try {
            Log.d(TAG, "开始安全导航: $from -> $route")
            ErrorLogger.logNavigation(TAG, from, route)
            
            // 检查NavController状态
            if (!isNavControllerValid(navController)) {
                ErrorLogger.logError(TAG, "NavController状态无效", null)
                return false
            }
            
            // 检查目标路由是否存在
            if (!isRouteValid(navController, route)) {
                ErrorLogger.logError(TAG, "目标路由无效: $route", null)
                return false
            }
            
            // 执行导航
            navController.navigate(route)
            Log.d(TAG, "安全导航成功: $from -> $route")
            ErrorLogger.logInfo(TAG, "导航成功: $from -> $route")
            true
            
        } catch (e: IllegalArgumentException) {
            Log.e(TAG, "导航参数错误: $route", e)
            ErrorLogger.logError(TAG, "导航参数错误: $route", e)
            false
        } catch (e: IllegalStateException) {
            Log.e(TAG, "导航状态错误: $route", e)
            ErrorLogger.logError(TAG, "导航状态错误: $route", e)
            false
        } catch (e: Exception) {
            Log.e(TAG, "导航未知错误: $route", e)
            ErrorLogger.logError(TAG, "导航未知错误: $route", e)
            false
        }
    }
    
    /**
     * 检查NavController是否有效
     */
    private fun isNavControllerValid(navController: NavController): Boolean {
        return try {
            // 尝试访问NavController的基本属性
            val graph = navController.graph
            val currentDestination = navController.currentDestination
            Log.d(TAG, "NavController状态检查 - 当前目的地: ${currentDestination?.route}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "NavController状态检查失败", e)
            false
        }
    }
    
    /**
     * 检查路由是否有效
     */
    private fun isRouteValid(navController: NavController, route: String): Boolean {
        return try {
            val graph = navController.graph
            
            // 检查是否是简单路由
            val destination = graph.findNode(route)
            if (destination != null) {
                Log.d(TAG, "找到目标路由: $route")
                return true
            }
            
            // 检查是否是带参数的路由
            val routePattern = extractRoutePattern(route)
            if (routePattern != route) {
                val patternDestination = graph.findNode(routePattern)
                if (patternDestination != null) {
                    Log.d(TAG, "找到参数化路由: $routePattern")
                    return true
                }
            }
            
            Log.w(TAG, "未找到目标路由: $route")
            false
        } catch (e: Exception) {
            Log.e(TAG, "路由验证失败: $route", e)
            false
        }
    }
    
    /**
     * 从带参数的路由中提取路由模式
     * 例如: "detail/123" -> "detail/{itemId}"
     */
    private fun extractRoutePattern(route: String): String {
        return when {
            route.startsWith("detail/") -> "detail/{itemId}"
            else -> route
        }
    }
    
    /**
     * 安全返回上一页
     */
    fun safePopBackStack(navController: NavController, from: String = "unknown"): Boolean {
        return try {
            Log.d(TAG, "尝试返回上一页，来源: $from")
            ErrorLogger.logNavigation(TAG, from, "back")
            
            if (!isNavControllerValid(navController)) {
                ErrorLogger.logError(TAG, "NavController状态无效，无法返回", null)
                return false
            }
            
            val result = navController.popBackStack()
            if (result) {
                Log.d(TAG, "返回上一页成功")
                ErrorLogger.logInfo(TAG, "返回上一页成功")
            } else {
                Log.w(TAG, "返回上一页失败，可能已在根页面")
                ErrorLogger.logWarning(TAG, "返回上一页失败，可能已在根页面")
            }
            result
            
        } catch (e: Exception) {
            Log.e(TAG, "返回上一页异常", e)
            ErrorLogger.logError(TAG, "返回上一页异常", e)
            false
        }
    }
    
    /**
     * 获取当前导航状态信息
     */
    fun getCurrentNavigationState(navController: NavController): String {
        return try {
            val currentDestination = navController.currentDestination
            val currentRoute = currentDestination?.route ?: "unknown"
            val destinationId = currentDestination?.id ?: -1
            val backStackSize = try {
                // 注意：backQueue是私有的，这里只是示例
                "unknown"
            } catch (e: Exception) {
                "error"
            }
            
            "当前路由: $currentRoute, 目的地ID: $destinationId, 返回栈大小: $backStackSize"
        } catch (e: Exception) {
            "获取导航状态失败: ${e.message}"
        }
    }
    
    /**
     * 记录导航错误详情
     */
    fun logNavigationError(
        operation: String,
        route: String,
        error: Throwable,
        navController: NavController? = null
    ) {
        Log.e(TAG, "=== 导航错误详情 ===")
        Log.e(TAG, "操作: $operation")
        Log.e(TAG, "目标路由: $route")
        Log.e(TAG, "错误类型: ${error.javaClass.simpleName}")
        Log.e(TAG, "错误信息: ${error.message}")
        
        navController?.let {
            Log.e(TAG, "导航状态: ${getCurrentNavigationState(it)}")
        }
        
        Log.e(TAG, "错误堆栈:", error)
        Log.e(TAG, "=== 导航错误详情结束 ===")
        
        // 同时记录到ErrorLogger
        ErrorLogger.logError(TAG, "导航错误 - 操作: $operation, 路由: $route", error)
    }
}
