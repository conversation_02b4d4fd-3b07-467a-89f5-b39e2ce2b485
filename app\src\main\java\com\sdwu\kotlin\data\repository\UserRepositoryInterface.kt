package com.sdwu.kotlin.data.repository

import com.sdwu.kotlin.data.model.User
import kotlinx.coroutines.flow.Flow

/**
 * 用户仓库接口
 * 定义用户数据访问的统一接口
 */
interface UserRepositoryInterface {
    
    /**
     * 初始化默认用户数据
     */
    suspend fun initializeDefaultUser()
    
    /**
     * 获取当前用户
     */
    suspend fun getCurrentUser(): User?
    
    /**
     * 根据ID获取用户
     */
    suspend fun getUserById(userId: String): User?
    
    /**
     * 插入用户
     */
    suspend fun insertUser(user: User)
    
    /**
     * 更新用户信息
     */
    suspend fun updateUser(user: User)
    
    /**
     * 删除用户
     */
    suspend fun deleteUser(userId: String)
    
    /**
     * 获取所有用户
     */
    fun getAllUsers(): Flow<List<User>>
    
    /**
     * 获取用户总数
     */
    suspend fun getUserCount(): Int
    
    /**
     * 清空所有用户
     */
    suspend fun clearAllUsers()

    /**
     * 检查用户是否存在
     */
    suspend fun userExists(userId: String): Bo<PERSON><PERSON>
}
