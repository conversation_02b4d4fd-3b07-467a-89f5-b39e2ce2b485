package com.sdwu.kotlin.utils

import android.content.Context
import android.util.Log
import androidx.navigation.NavController
import com.sdwu.kotlin.KotlinApplication
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 调试工具类
 * 提供各种调试和诊断功能
 */
object DebugUtils {
    
    private const val TAG = "DebugUtils"
    
    /**
     * 检查应用程序状态
     */
    fun checkApplicationState(context: Context) {
        ErrorLogger.logDebug(TAG, "=== 应用程序状态检查 ===")
        
        try {
            // 检查Context
            ErrorLogger.logDebug(TAG, "Context类型: ${context.javaClass.simpleName}")
            ErrorLogger.logDebug(TAG, "ApplicationContext类型: ${context.applicationContext.javaClass.simpleName}")
            
            // 检查KotlinApplication
            val app = context.applicationContext as? KotlinApplication
            if (app != null) {
                ErrorLogger.logDebug(TAG, "KotlinApplication实例: 正常")
                
                // 检查AppContainer
                try {
                    val appContainer = app.appContainer
                    ErrorLogger.logDebug(TAG, "AppContainer: 已初始化")
                    checkAppContainer(appContainer)
                } catch (e: Exception) {
                    ErrorLogger.logError(TAG, "AppContainer: 访问失败", e)
                }
            } else {
                ErrorLogger.logError(TAG, "无法获取KotlinApplication实例")
            }
            
        } catch (e: Exception) {
            ErrorLogger.logError(TAG, "应用程序状态检查失败", e)
        }
        
        ErrorLogger.logDebug(TAG, "=== 状态检查完成 ===")
    }
    
    /**
     * 检查AppContainer状态
     */
    private fun checkAppContainer(appContainer: com.sdwu.kotlin.di.AppContainer) {
        try {
            ErrorLogger.logDebug(TAG, "检查AppContainer组件...")
            
            // 检查UserRepository
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val userRepo = appContainer.userRepository
                    ErrorLogger.logDebug(TAG, "UserRepository: 可访问")
                    
                    // 测试数据库连接
                    val userExists = userRepo.userExists("current_user")
                    ErrorLogger.logDebug(TAG, "数据库连接测试: ${if (userExists) "用户存在" else "用户不存在"}")
                    
                } catch (e: Exception) {
                    ErrorLogger.logError(TAG, "UserRepository检查失败", e)
                }
            }
            
            // 检查其他Repository
            try {
                val homeRepo = appContainer.homeRepository
                ErrorLogger.logDebug(TAG, "HomeRepository: 可访问")
            } catch (e: Exception) {
                ErrorLogger.logError(TAG, "HomeRepository检查失败", e)
            }
            
            try {
                val settingsRepo = appContainer.settingsRepository
                ErrorLogger.logDebug(TAG, "SettingsRepository: 可访问")
            } catch (e: Exception) {
                ErrorLogger.logError(TAG, "SettingsRepository检查失败", e)
            }
            
        } catch (e: Exception) {
            ErrorLogger.logError(TAG, "AppContainer检查失败", e)
        }
    }
    
    /**
     * 检查导航状态
     */
    fun checkNavigationState(navController: NavController) {
        try {
            ErrorLogger.logDebug(TAG, "=== 导航状态检查 ===")
            
            val currentDestination = navController.currentDestination
            if (currentDestination != null) {
                ErrorLogger.logDebug(TAG, "当前目的地: ${currentDestination.route}")
                ErrorLogger.logDebug(TAG, "目的地ID: ${currentDestination.id}")
                ErrorLogger.logDebug(TAG, "目的地标签: ${currentDestination.label}")
            } else {
                ErrorLogger.logWarning(TAG, "当前目的地为null")
            }
            
            // 注意：backQueue是私有的，我们使用其他方式获取导航信息
            try {
                val graph = navController.graph
                ErrorLogger.logDebug(TAG, "导航图起始目的地: ${graph.startDestinationRoute}")
                ErrorLogger.logDebug(TAG, "导航图ID: ${graph.id}")
            } catch (e: Exception) {
                ErrorLogger.logWarning(TAG, "无法获取导航图信息: ${e.message}")
            }
            
        } catch (e: Exception) {
            ErrorLogger.logError(TAG, "导航状态检查失败", e)
        }
    }
    
    /**
     * 记录内存使用情况
     */
    fun logMemoryUsage() {
        try {
            val runtime = Runtime.getRuntime()
            val maxMemory = runtime.maxMemory()
            val totalMemory = runtime.totalMemory()
            val freeMemory = runtime.freeMemory()
            val usedMemory = totalMemory - freeMemory
            
            ErrorLogger.logDebug(TAG, "=== 内存使用情况 ===")
            ErrorLogger.logDebug(TAG, "最大内存: ${maxMemory / 1024 / 1024}MB")
            ErrorLogger.logDebug(TAG, "已分配内存: ${totalMemory / 1024 / 1024}MB")
            ErrorLogger.logDebug(TAG, "空闲内存: ${freeMemory / 1024 / 1024}MB")
            ErrorLogger.logDebug(TAG, "已使用内存: ${usedMemory / 1024 / 1024}MB")
            ErrorLogger.logDebug(TAG, "内存使用率: ${(usedMemory * 100 / maxMemory)}%")
            
        } catch (e: Exception) {
            ErrorLogger.logError(TAG, "内存使用情况记录失败", e)
        }
    }
    
    /**
     * 记录线程信息
     */
    fun logThreadInfo() {
        try {
            ErrorLogger.logDebug(TAG, "=== 线程信息 ===")
            
            val currentThread = Thread.currentThread()
            ErrorLogger.logDebug(TAG, "当前线程: ${currentThread.name}")
            ErrorLogger.logDebug(TAG, "线程ID: ${currentThread.id}")
            ErrorLogger.logDebug(TAG, "线程状态: ${currentThread.state}")
            ErrorLogger.logDebug(TAG, "是否守护线程: ${currentThread.isDaemon}")
            ErrorLogger.logDebug(TAG, "线程优先级: ${currentThread.priority}")
            
            val threadGroup = currentThread.threadGroup
            if (threadGroup != null) {
                ErrorLogger.logDebug(TAG, "线程组: ${threadGroup.name}")
                ErrorLogger.logDebug(TAG, "活跃线程数: ${threadGroup.activeCount()}")
            }
            
        } catch (e: Exception) {
            ErrorLogger.logError(TAG, "线程信息记录失败", e)
        }
    }
    
    /**
     * 测试数据库连接
     */
    fun testDatabaseConnection(context: Context) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                ErrorLogger.logDebug(TAG, "=== 数据库连接测试 ===")
                
                val app = context.applicationContext as? KotlinApplication
                if (app != null) {
                    val userRepo = app.appContainer.userRepository
                    
                    // 测试基本操作
                    ErrorLogger.logDebug(TAG, "测试用户存在性检查...")
                    val exists = userRepo.userExists("current_user")
                    ErrorLogger.logDebug(TAG, "用户存在性检查结果: $exists")
                    
                    if (!exists) {
                        ErrorLogger.logDebug(TAG, "初始化默认用户...")
                        userRepo.initializeDefaultUser()
                        ErrorLogger.logDebug(TAG, "默认用户初始化完成")
                    }
                    
                    ErrorLogger.logDebug(TAG, "获取当前用户...")
                    val user = userRepo.getCurrentUser()
                    ErrorLogger.logDebug(TAG, "当前用户: ${user?.name ?: "null"}")
                    
                    ErrorLogger.logDebug(TAG, "数据库连接测试完成")
                } else {
                    ErrorLogger.logError(TAG, "无法获取应用实例")
                }
                
            } catch (e: Exception) {
                ErrorLogger.logError(TAG, "数据库连接测试失败", e)
            }
        }
    }
    
    /**
     * 生成调试报告
     */
    fun generateDebugReport(context: Context, navController: NavController? = null) {
        ErrorLogger.logInfo(TAG, "=== 开始生成调试报告 ===")
        
        checkApplicationState(context)
        logMemoryUsage()
        logThreadInfo()
        
        navController?.let { checkNavigationState(it) }
        
        testDatabaseConnection(context)
        
        ErrorLogger.logInfo(TAG, "=== 调试报告生成完成 ===")
    }
}
