<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义渐变色 -->
    <linearGradient id="uiGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#81C784;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="viewModelGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2196F3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#64B5F6;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="repositoryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF9800;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFB74D;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="dataGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9C27B0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#BA68C8;stop-opacity:1" />
    </linearGradient>
    
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
            refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    
    <marker id="arrowheadReturn" markerWidth="10" markerHeight="7" 
            refX="1" refY="3.5" orient="auto">
      <polygon points="10 0, 0 3.5, 10 7" fill="#666" />
    </marker>
  </defs>
  
  <!-- 背景 -->
  <rect width="1400" height="1000" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="700" y="30" text-anchor="middle" font-family="Arial, sans-serif" 
        font-size="24" font-weight="bold" fill="#333">
    Android MVVM 业务逻辑层详细架构图
  </text>
  
  <!-- UI层简化 -->
  <g id="ui-layer">
    <rect x="50" y="80" width="1300" height="80" rx="10" 
          fill="url(#uiGradient)" opacity="0.1" stroke="#4CAF50" stroke-width="2"/>
    
    <text x="70" y="105" font-family="Arial, sans-serif" font-size="16" 
          font-weight="bold" fill="#2E7D32">UI层 - Jetpack Compose界面</text>
    
    <rect x="100" y="120" width="120" height="30" rx="5" 
          fill="url(#uiGradient)" stroke="#4CAF50" stroke-width="1"/>
    <text x="160" y="140" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="11" font-weight="bold" fill="white">HomeScreen</text>
    
    <rect x="250" y="120" width="120" height="30" rx="5" 
          fill="url(#uiGradient)" stroke="#4CAF50" stroke-width="1"/>
    <text x="310" y="140" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="11" font-weight="bold" fill="white">ProfileScreen</text>
    
    <rect x="400" y="120" width="120" height="30" rx="5" 
          fill="url(#uiGradient)" stroke="#4CAF50" stroke-width="1"/>
    <text x="460" y="140" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="11" font-weight="bold" fill="white">SettingsScreen</text>
    
    <rect x="550" y="120" width="120" height="30" rx="5" 
          fill="url(#uiGradient)" stroke="#4CAF50" stroke-width="1"/>
    <text x="610" y="140" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="11" font-weight="bold" fill="white">DetailScreen</text>
  </g>
  
  <!-- ViewModel业务逻辑层 - 详细展示 -->
  <g id="viewmodel-layer">
    <!-- ViewModel层背景 -->
    <rect x="50" y="200" width="1300" height="450" rx="10" 
          fill="url(#viewModelGradient)" opacity="0.1" stroke="#2196F3" stroke-width="3"/>
    
    <text x="70" y="230" font-family="Arial, sans-serif" font-size="20" 
          font-weight="bold" fill="#1565C0">ViewModel业务逻辑层 - 核心处理中心</text>
    
    <!-- HomeViewModel详细功能 -->
    <rect x="80" y="250" width="280" height="180" rx="8" 
          fill="url(#viewModelGradient)" stroke="#2196F3" stroke-width="2"/>
    <text x="220" y="275" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="14" font-weight="bold" fill="white">HomeViewModel</text>
    
    <!-- 状态管理 -->
    <rect x="90" y="285" width="260" height="25" rx="3" fill="#E3F2FD" stroke="#1976D2" stroke-width="1"/>
    <text x="95" y="300" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#1565C0">状态管理 (StateFlow)</text>
    <text x="95" y="315" font-family="Arial, sans-serif" font-size="9" fill="#333">• _uiState: MutableStateFlow&lt;HomeUiState&gt;</text>
    <text x="95" y="328" font-family="Arial, sans-serif" font-size="9" fill="#333">• _searchQuery: MutableStateFlow&lt;String&gt;</text>
    
    <!-- 业务方法 -->
    <text x="95" y="345" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">核心业务方法:</text>
    <text x="95" y="360" font-family="Arial, sans-serif" font-size="9" fill="white">• loadHomeItems() - 数据加载与Flow收集</text>
    <text x="95" y="373" font-family="Arial, sans-serif" font-size="9" fill="white">• searchItems(query) - 实时搜索过滤</text>
    <text x="95" y="386" font-family="Arial, sans-serif" font-size="9" fill="white">• refreshData() - 下拉刷新逻辑</text>
    <text x="95" y="399" font-family="Arial, sans-serif" font-size="9" fill="white">• addItem(title, desc) - 新增项目</text>
    <text x="95" y="412" font-family="Arial, sans-serif" font-size="9" fill="white">• deleteItem(id) - 删除项目</text>
    <text x="95" y="425" font-family="Arial, sans-serif" font-size="9" fill="white">• clearError() - 错误状态清理</text>
    
    <!-- ProfileViewModel详细功能 -->
    <rect x="380" y="250" width="280" height="180" rx="8" 
          fill="url(#viewModelGradient)" stroke="#2196F3" stroke-width="2"/>
    <text x="520" y="275" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="14" font-weight="bold" fill="white">ProfileViewModel</text>
    
    <!-- 状态管理 -->
    <rect x="390" y="285" width="260" height="25" rx="3" fill="#E3F2FD" stroke="#1976D2" stroke-width="1"/>
    <text x="395" y="300" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#1565C0">用户状态管理</text>
    <text x="395" y="315" font-family="Arial, sans-serif" font-size="9" fill="#333">• _uiState: MutableStateFlow&lt;ProfileUiState&gt;</text>
    <text x="395" y="328" font-family="Arial, sans-serif" font-size="9" fill="#333">• isEditing, user, error状态</text>
    
    <!-- 业务方法 -->
    <text x="395" y="345" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">用户管理方法:</text>
    <text x="395" y="360" font-family="Arial, sans-serif" font-size="9" fill="white">• loadUserProfile() - 用户数据加载</text>
    <text x="395" y="373" font-family="Arial, sans-serif" font-size="9" fill="white">• updateUserInfo(user) - 用户信息更新</text>
    <text x="395" y="386" font-family="Arial, sans-serif" font-size="9" fill="white">• enterEditMode() - 进入编辑模式</text>
    <text x="395" y="399" font-family="Arial, sans-serif" font-size="9" fill="white">• cancelEdit() - 取消编辑</text>
    <text x="395" y="412" font-family="Arial, sans-serif" font-size="9" fill="white">• validateInput() - 输入验证</text>
    <text x="395" y="425" font-family="Arial, sans-serif" font-size="9" fill="white">• saveChanges() - 保存到数据库</text>
    
    <!-- SettingsViewModel详细功能 -->
    <rect x="680" y="250" width="280" height="180" rx="8" 
          fill="url(#viewModelGradient)" stroke="#2196F3" stroke-width="2"/>
    <text x="820" y="275" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="14" font-weight="bold" fill="white">SettingsViewModel</text>
    
    <!-- 状态管理 -->
    <rect x="690" y="285" width="260" height="25" rx="3" fill="#E3F2FD" stroke="#1976D2" stroke-width="1"/>
    <text x="695" y="300" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#1565C0">设置状态管理</text>
    <text x="695" y="315" font-family="Arial, sans-serif" font-size="9" fill="#333">• _uiState: MutableStateFlow&lt;SettingsUiState&gt;</text>
    <text x="695" y="328" font-family="Arial, sans-serif" font-size="9" fill="#333">• settings, showConfirmation状态</text>
    
    <!-- 业务方法 -->
    <text x="695" y="345" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">设置管理方法:</text>
    <text x="695" y="360" font-family="Arial, sans-serif" font-size="9" fill="white">• loadSettings() - DataStore设置加载</text>
    <text x="695" y="373" font-family="Arial, sans-serif" font-size="9" fill="white">• toggleDarkMode() - 主题切换</text>
    <text x="695" y="386" font-family="Arial, sans-serif" font-size="9" fill="white">• updateLanguage(lang) - 语言设置</text>
    <text x="695" y="399" font-family="Arial, sans-serif" font-size="9" fill="white">• toggleNotifications() - 通知开关</text>
    <text x="695" y="412" font-family="Arial, sans-serif" font-size="9" fill="white">• resetAllSettings() - 重置所有设置</text>
    <text x="695" y="425" font-family="Arial, sans-serif" font-size="9" fill="white">• showResetConfirmation() - 确认对话框</text>
    
    <!-- DetailViewModel详细功能 -->
    <rect x="980" y="250" width="280" height="180" rx="8" 
          fill="url(#viewModelGradient)" stroke="#2196F3" stroke-width="2"/>
    <text x="1120" y="275" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="14" font-weight="bold" fill="white">DetailViewModel</text>
    
    <!-- 状态管理 -->
    <rect x="990" y="285" width="260" height="25" rx="3" fill="#E3F2FD" stroke="#1976D2" stroke-width="1"/>
    <text x="995" y="300" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#1565C0">详情状态管理</text>
    <text x="995" y="315" font-family="Arial, sans-serif" font-size="9" fill="#333">• _uiState: MutableStateFlow&lt;DetailUiState&gt;</text>
    <text x="995" y="328" font-family="Arial, sans-serif" font-size="9" fill="#333">• itemDetail, isLoading, error状态</text>
    
    <!-- 业务方法 -->
    <text x="995" y="345" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">详情处理方法:</text>
    <text x="995" y="360" font-family="Arial, sans-serif" font-size="9" fill="white">• loadItemDetail(id) - 详情数据加载</text>
    <text x="995" y="373" font-family="Arial, sans-serif" font-size="9" fill="white">• validateItemId() - ID有效性验证</text>
    <text x="995" y="386" font-family="Arial, sans-serif" font-size="9" fill="white">• handleNotFound() - 404错误处理</text>
    <text x="995" y="399" font-family="Arial, sans-serif" font-size="9" fill="white">• refreshDetail() - 详情刷新</text>
    <text x="995" y="412" font-family="Arial, sans-serif" font-size="9" fill="white">• shareContent() - 内容分享</text>
    <text x="995" y="425" font-family="Arial, sans-serif" font-size="9" fill="white">• favoriteItem() - 收藏功能</text>
    
    <!-- 业务逻辑核心机制 -->
    <rect x="80" y="450" width="1180" height="180" rx="8" 
          fill="#E8F5E8" stroke="#4CAF50" stroke-width="2"/>
    <text x="670" y="475" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="16" font-weight="bold" fill="#2E7D32">ViewModel业务逻辑核心机制</text>
    
    <!-- 左侧：状态管理机制 -->
    <rect x="100" y="485" width="280" height="130" rx="5" fill="#F3E5F5" stroke="#9C27B0" stroke-width="1"/>
    <text x="240" y="505" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="12" font-weight="bold" fill="#6A1B9A">状态管理机制</text>
    <text x="110" y="525" font-family="Arial, sans-serif" font-size="10" fill="#333">• StateFlow响应式状态流</text>
    <text x="110" y="540" font-family="Arial, sans-serif" font-size="10" fill="#333">• MutableStateFlow内部状态</text>
    <text x="110" y="555" font-family="Arial, sans-serif" font-size="10" fill="#333">• asStateFlow()只读暴露</text>
    <text x="110" y="570" font-family="Arial, sans-serif" font-size="10" fill="#333">• collectAsState()UI观察</text>
    <text x="110" y="585" font-family="Arial, sans-serif" font-size="10" fill="#333">• 自动UI重组更新</text>
    <text x="110" y="600" font-family="Arial, sans-serif" font-size="10" fill="#333">• 配置变更状态保持</text>
    
    <!-- 中间：异步处理机制 -->
    <rect x="400" y="485" width="280" height="130" rx="5" fill="#FFF3E0" stroke="#FF9800" stroke-width="1"/>
    <text x="540" y="505" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="12" font-weight="bold" fill="#E65100">异步处理机制</text>
    <text x="410" y="525" font-family="Arial, sans-serif" font-size="10" fill="#333">• viewModelScope协程作用域</text>
    <text x="410" y="540" font-family="Arial, sans-serif" font-size="10" fill="#333">• launch{}异步任务启动</text>
    <text x="410" y="555" font-family="Arial, sans-serif" font-size="10" fill="#333">• Flow.collect{}数据流收集</text>
    <text x="410" y="570" font-family="Arial, sans-serif" font-size="10" fill="#333">• try-catch异常捕获</text>
    <text x="410" y="585" font-family="Arial, sans-serif" font-size="10" fill="#333">• 自动协程取消</text>
    <text x="410" y="600" font-family="Arial, sans-serif" font-size="10" fill="#333">• 内存泄漏防护</text>
    
    <!-- 右侧：错误处理机制 -->
    <rect x="700" y="485" width="280" height="130" rx="5" fill="#FFEBEE" stroke="#F44336" stroke-width="1"/>
    <text x="840" y="505" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="12" font-weight="bold" fill="#C62828">错误处理机制</text>
    <text x="710" y="525" font-family="Arial, sans-serif" font-size="10" fill="#333">• 统一异常捕获处理</text>
    <text x="710" y="540" font-family="Arial, sans-serif" font-size="10" fill="#333">• 错误状态UI反馈</text>
    <text x="710" y="555" font-family="Arial, sans-serif" font-size="10" fill="#333">• 加载状态管理</text>
    <text x="710" y="570" font-family="Arial, sans-serif" font-size="10" fill="#333">• 用户友好错误信息</text>
    <text x="710" y="585" font-family="Arial, sans-serif" font-size="10" fill="#333">• clearError()错误清理</text>
    <text x="710" y="600" font-family="Arial, sans-serif" font-size="10" fill="#333">• 重试机制支持</text>
    
    <!-- 最右侧：数据验证机制 -->
    <rect x="1000" y="485" width="280" height="130" rx="5" fill="#E0F2F1" stroke="#009688" stroke-width="1"/>
    <text x="1140" y="505" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="12" font-weight="bold" fill="#00695C">数据验证机制</text>
    <text x="1010" y="525" font-family="Arial, sans-serif" font-size="10" fill="#333">• 输入参数验证</text>
    <text x="1010" y="540" font-family="Arial, sans-serif" font-size="10" fill="#333">• 业务规则检查</text>
    <text x="1010" y="555" font-family="Arial, sans-serif" font-size="10" fill="#333">• 数据格式校验</text>
    <text x="1010" y="570" font-family="Arial, sans-serif" font-size="10" fill="#333">• 空值安全处理</text>
    <text x="1010" y="585" font-family="Arial, sans-serif" font-size="10" fill="#333">• 边界条件检查</text>
    <text x="1010" y="600" font-family="Arial, sans-serif" font-size="10" fill="#333">• 实时验证反馈</text>
  </g>
  
  <!-- Repository层简化 -->
  <g id="repository-layer">
    <rect x="50" y="680" width="1300" height="80" rx="10" 
          fill="url(#repositoryGradient)" opacity="0.1" stroke="#FF9800" stroke-width="2"/>
    
    <text x="70" y="705" font-family="Arial, sans-serif" font-size="16" 
          font-weight="bold" fill="#E65100">Repository层 - 数据访问抽象</text>
    
    <rect x="150" y="720" width="180" height="30" rx="5" 
          fill="url(#repositoryGradient)" stroke="#FF9800" stroke-width="1"/>
    <text x="240" y="740" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="11" font-weight="bold" fill="white">HomeRepository</text>
    
    <rect x="360" y="720" width="180" height="30" rx="5" 
          fill="url(#repositoryGradient)" stroke="#FF9800" stroke-width="1"/>
    <text x="450" y="740" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="11" font-weight="bold" fill="white">UserRepository</text>
    
    <rect x="570" y="720" width="180" height="30" rx="5" 
          fill="url(#repositoryGradient)" stroke="#FF9800" stroke-width="1"/>
    <text x="660" y="740" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="11" font-weight="bold" fill="white">SettingsRepository</text>
  </g>
  
  <!-- 数据层简化 -->
  <g id="data-layer">
    <rect x="50" y="780" width="1300" height="80" rx="10" 
          fill="url(#dataGradient)" opacity="0.1" stroke="#9C27B0" stroke-width="2"/>
    
    <text x="70" y="805" font-family="Arial, sans-serif" font-size="16" 
          font-weight="bold" fill="#6A1B9A">数据层 - 持久化存储</text>
    
    <rect x="150" y="820" width="150" height="30" rx="5" 
          fill="url(#dataGradient)" stroke="#9C27B0" stroke-width="1"/>
    <text x="225" y="840" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="11" font-weight="bold" fill="white">Room Database</text>
    
    <rect x="330" y="820" width="150" height="30" rx="5" 
          fill="url(#dataGradient)" stroke="#9C27B0" stroke-width="1"/>
    <text x="405" y="840" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="11" font-weight="bold" fill="white">DataStore</text>
    
    <rect x="510" y="820" width="150" height="30" rx="5" 
          fill="#E1BEE7" stroke="#9C27B0" stroke-width="1"/>
    <text x="585" y="840" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="11" font-weight="bold" fill="#6A1B9A">Network API</text>
  </g>
  
  <!-- 数据流箭头 -->
  <!-- UI到ViewModel -->
  <line x1="160" y1="150" x2="220" y2="250" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="170" y="200" font-family="Arial, sans-serif" font-size="10" fill="#333">用户交互</text>
  
  <line x1="310" y1="150" x2="520" y2="250" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="460" y1="150" x2="820" y2="250" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="610" y1="150" x2="1120" y2="250" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- ViewModel到Repository -->
  <line x1="240" y1="430" x2="240" y2="720" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="250" y="575" font-family="Arial, sans-serif" font-size="10" fill="#333">Repository调用</text>
  
  <line x1="450" y1="430" x2="450" y2="720" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="660" y1="430" x2="660" y2="720" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Repository到数据层 -->
  <line x1="225" y1="750" x2="225" y2="820" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="405" y1="750" x2="405" y2="820" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 数据返回流 -->
  <line x1="260" y1="720" x2="260" y2="430" stroke="#666" stroke-width="2" stroke-dasharray="5,5" marker-end="url(#arrowheadReturn)"/>
  <text x="270" y="575" font-family="Arial, sans-serif" font-size="10" fill="#666">Flow数据流</text>
  
  <line x1="240" y1="250" x2="180" y2="150" stroke="#666" stroke-width="2" stroke-dasharray="5,5" marker-end="url(#arrowheadReturn)"/>
  <text x="190" y="200" font-family="Arial, sans-serif" font-size="10" fill="#666">StateFlow更新</text>
  
  <!-- 说明文字 -->
  <text x="50" y="920" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#333">业务逻辑层核心职责:</text>
  <text x="50" y="940" font-family="Arial, sans-serif" font-size="12" fill="#333">1. 状态管理 - StateFlow响应式状态流，自动UI更新</text>
  <text x="50" y="955" font-family="Arial, sans-serif" font-size="12" fill="#333">2. 异步处理 - viewModelScope协程，Flow数据流收集</text>
  <text x="50" y="970" font-family="Arial, sans-serif" font-size="12" fill="#333">3. 业务逻辑 - 数据验证、转换、缓存、错误处理</text>
  <text x="50" y="985" font-family="Arial, sans-serif" font-size="12" fill="#333">4. Repository调用 - 数据源抽象，统一数据访问接口</text>
</svg>
