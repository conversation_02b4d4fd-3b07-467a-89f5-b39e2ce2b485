package com.sdwu.kotlin.data.repository

import android.util.Log
import com.sdwu.kotlin.data.model.User
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 内存存储的用户仓库实现
 * 替代Room数据库，避免SQLite权限问题
 */
class InMemoryUserRepository : UserRepositoryInterface {

    companion object {
        private const val TAG = "InMemoryUserRepository"
    }

    // 内存中的用户存储
    private val _users = MutableStateFlow<List<User>>(emptyList())
    private val users: Flow<List<User>> = _users.asStateFlow()

    // 当前用户
    private val _currentUser = MutableStateFlow<User?>(null)

    init {
        Log.d(TAG, "InMemoryUserRepository初始化")
    }

    override suspend fun initializeDefaultUser() {
        Log.d(TAG, "初始化默认用户")
        
        // 模拟数据库操作延迟
        delay(100)
        
        if (_currentUser.value == null) {
            val defaultUser = User(
                id = "default_user_001",
                name = "默认用户",
                email = "<EMAIL>",
                registrationDate = "2024-01-01",
                avatarUrl = null
            )
            
            _currentUser.value = defaultUser
            _users.value = listOf(defaultUser)
            
            Log.d(TAG, "默认用户创建成功: ${defaultUser.name}")
        } else {
            Log.d(TAG, "默认用户已存在")
        }
    }

    override suspend fun getCurrentUser(): User? {
        Log.d(TAG, "获取当前用户")
        
        // 模拟数据库查询延迟
        delay(50)
        
        val user = _currentUser.value
        Log.d(TAG, "当前用户: ${user?.name ?: "null"}")
        return user
    }

    override suspend fun getUserById(userId: String): User? {
        Log.d(TAG, "根据ID获取用户: $userId")
        
        delay(50)
        
        val user = _users.value.find { it.id == userId }
        Log.d(TAG, "找到用户: ${user?.name ?: "未找到"}")
        return user
    }

    override suspend fun insertUser(user: User) {
        Log.d(TAG, "插入用户: ${user.name}")
        
        delay(100)
        
        val currentUsers = _users.value.toMutableList()
        
        // 检查是否已存在
        val existingIndex = currentUsers.indexOfFirst { it.id == user.id }
        if (existingIndex >= 0) {
            currentUsers[existingIndex] = user
            Log.d(TAG, "更新现有用户: ${user.name}")
        } else {
            currentUsers.add(user)
            Log.d(TAG, "添加新用户: ${user.name}")
        }
        
        _users.value = currentUsers
        
        // 如果是第一个用户或者ID匹配，设为当前用户
        if (_currentUser.value == null || _currentUser.value?.id == user.id) {
            _currentUser.value = user
        }
    }

    override suspend fun updateUser(user: User) {
        Log.d(TAG, "更新用户: ${user.name}")
        
        delay(100)
        
        val currentUsers = _users.value.toMutableList()
        val index = currentUsers.indexOfFirst { it.id == user.id }
        
        if (index >= 0) {
            currentUsers[index] = user
            _users.value = currentUsers
            
            // 如果是当前用户，也更新当前用户
            if (_currentUser.value?.id == user.id) {
                _currentUser.value = user
            }
            
            Log.d(TAG, "用户更新成功: ${user.name}")
        } else {
            Log.w(TAG, "要更新的用户不存在: ${user.id}")
            // 如果用户不存在，则插入
            insertUser(user)
        }
    }

    override suspend fun deleteUser(userId: String) {
        Log.d(TAG, "删除用户: $userId")
        
        delay(100)
        
        val currentUsers = _users.value.toMutableList()
        val removedUser = currentUsers.removeAll { it.id == userId }
        
        if (removedUser) {
            _users.value = currentUsers
            
            // 如果删除的是当前用户，清空当前用户
            if (_currentUser.value?.id == userId) {
                _currentUser.value = null
            }
            
            Log.d(TAG, "用户删除成功: $userId")
        } else {
            Log.w(TAG, "要删除的用户不存在: $userId")
        }
    }

    override fun getAllUsers(): Flow<List<User>> {
        Log.d(TAG, "获取所有用户流")
        return users
    }

    override suspend fun getUserCount(): Int {
        delay(50)
        val count = _users.value.size
        Log.d(TAG, "用户总数: $count")
        return count
    }

    override suspend fun clearAllUsers() {
        Log.d(TAG, "清空所有用户")
        
        delay(100)
        
        _users.value = emptyList()
        _currentUser.value = null
        
        Log.d(TAG, "所有用户已清空")
    }

    /**
     * 获取内存中的用户列表（用于调试）
     */
    fun getInMemoryUsers(): List<User> {
        return _users.value
    }

    /**
     * 检查用户是否存在
     */
    override suspend fun userExists(userId: String): Boolean {
        delay(50)
        val exists = _users.value.any { it.id == userId }
        Log.d(TAG, "用户是否存在 $userId: $exists")
        return exists
    }

    /**
     * 设置当前用户（用于测试）
     */
    suspend fun setCurrentUser(user: User?) {
        Log.d(TAG, "设置当前用户: ${user?.name}")
        _currentUser.value = user

        if (user != null) {
            insertUser(user)
        }
    }
}
