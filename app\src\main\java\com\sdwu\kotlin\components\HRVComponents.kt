package com.sdwu.kotlin.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AccountBox
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.ArrowForward
import androidx.compose.material.icons.filled.FavoriteBorder
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.Star
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.sdwu.kotlin.data.model.*
import com.sdwu.kotlin.data.repository.HRVStats
import com.sdwu.kotlin.ui.theme.*
import kotlin.math.*

/**
 * HRV数据卡片组件
 * 在首页展示HRV统计信息
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HRVDataCard(
    stats: HRVStats?,
    trendData: List<HRVTrendData>,
    isLoading: Boolean,
    onCardClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        onClick = onCardClick,
        modifier = modifier
            .fillMaxWidth()
            .height(200.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        if (isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
                // 标题行
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "HRV",
                            tint = MaterialTheme.colorScheme.secondary,
                            modifier = Modifier.size(24.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "心率变异性",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold
                        )
                    }
                    
                    // 压力水平指示器
                    stats?.stressLevel?.let { level ->
                        StressLevelIndicator(level = level)
                    }
                }
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 统计数据行
                stats?.let { hrvStats ->
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        HRVStatItem(
                            label = "RMSSD",
                            value = String.format("%.1f", hrvStats.averageRMSSD),
                            unit = "ms"
                        )
                        HRVStatItem(
                            label = "SDNN",
                            value = String.format("%.1f", hrvStats.averageSDNN),
                            unit = "ms"
                        )
                        HRVStatItem(
                            label = "LF/HF",
                            value = String.format("%.2f", hrvStats.averageLFHFRatio),
                            unit = ""
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // HRV趋势图预览
                if (trendData.isNotEmpty()) {
                    HRVTrendPreview(
                        trendData = trendData,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(60.dp)
                    )
                }
            }
        }
    }
}

/**
 * HRV统计项组件
 */
@Composable
private fun HRVStatItem(
    label: String,
    value: String,
    unit: String,
    modifier: Modifier = Modifier
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.titleLarge,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.secondary
        )
        if (unit.isNotEmpty()) {
            Text(
                text = unit,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 压力水平指示器
 */
@Composable
private fun StressLevelIndicator(
    level: Int,
    modifier: Modifier = Modifier
) {
    val (color, text) = when (level) {
        1 -> Color.Green to "很低"
        2 -> Color.Blue to "低"
        3 -> Color.Yellow to "中等"
        4 -> Color(0xFFFFA500) to "高"
        5 -> Color.Red to "很高"
        else -> Color.Gray to "未知"
    }
    
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier
            .background(
                color = color.copy(alpha = 0.1f),
                shape = RoundedCornerShape(12.dp)
            )
            .padding(horizontal = 8.dp, vertical = 4.dp)
    ) {
        Icon(
            imageVector = Icons.Default.FavoriteBorder,
            contentDescription = "压力水平",
            tint = color,
            modifier = Modifier.size(12.dp)
        )
        Spacer(modifier = Modifier.width(4.dp))
        Text(
            text = text,
            style = MaterialTheme.typography.bodySmall,
            color = color,
            fontSize = 10.sp
        )
    }
}

/**
 * HRV趋势预览组件
 */
@Composable
fun HRVTrendPreview(
    trendData: List<HRVTrendData>,
    modifier: Modifier = Modifier
) {
    Canvas(
        modifier = modifier
            .clip(RoundedCornerShape(8.dp))
            .background(MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f))
    ) {
        drawHRVTrend(trendData)
    }
}

/**
 * 绘制HRV趋势图
 */
private fun DrawScope.drawHRVTrend(trendData: List<HRVTrendData>) {
    if (trendData.isEmpty()) return
    
    val width = size.width
    val height = size.height
    val padding = 20f
    
    // 绘制RMSSD趋势线
    val rmssdValues = trendData.map { it.avgRMSSD }
    val maxRMSSD = rmssdValues.maxOrNull() ?: 50f
    val minRMSSD = rmssdValues.minOrNull() ?: 20f
    val rmssdRange = maxRMSSD - minRMSSD
    
    if (rmssdRange > 0) {
        val rmssdPath = Path()
        
        rmssdValues.forEachIndexed { index, value ->
            val x = padding + (index.toFloat() / (rmssdValues.size - 1)) * (width - 2 * padding)
            val normalizedValue = (value - minRMSSD) / rmssdRange
            val y = height - padding - normalizedValue * (height - 2 * padding)
            
            if (index == 0) {
                rmssdPath.moveTo(x, y)
            } else {
                rmssdPath.lineTo(x, y)
            }
        }
        
        drawPath(
            path = rmssdPath,
            color = Color.Blue,
            style = Stroke(width = 2.dp.toPx())
        )
    }
    
    // 绘制数据点
    rmssdValues.forEachIndexed { index, value ->
        val x = padding + (index.toFloat() / (rmssdValues.size - 1)) * (width - 2 * padding)
        val normalizedValue = (value - minRMSSD) / rmssdRange
        val y = height - padding - normalizedValue * (height - 2 * padding)
        
        drawCircle(
            color = Color.Blue,
            radius = 3.dp.toPx(),
            center = Offset(x, y)
        )
    }
}

/**
 * HRV测量进度组件
 */
@Composable
fun HRVMeasurementProgress(
    realtimeData: HRVRealtimeData?,
    isMeasuring: Boolean,
    onStartMeasurement: () -> Unit,
    onStopMeasurement: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "HRV测量",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                Button(
                    onClick = if (isMeasuring) onStopMeasurement else onStartMeasurement,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = if (isMeasuring) 
                            MaterialTheme.colorScheme.error 
                        else 
                            MaterialTheme.colorScheme.secondary
                    )
                ) {
                    Text(if (isMeasuring) "停止测量" else "开始测量")
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            realtimeData?.let { data ->
                // 测量进度
                Column {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = "测量进度",
                            style = MaterialTheme.typography.bodyMedium
                        )
                        Text(
                            text = "${(data.measurementProgress * 100).toInt()}%",
                            style = MaterialTheme.typography.bodyMedium,
                            fontWeight = FontWeight.Bold
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(4.dp))
                    
                    LinearProgressIndicator(
                        progress = data.measurementProgress,
                        modifier = Modifier.fillMaxWidth(),
                        color = MaterialTheme.colorScheme.secondary
                    )
                }
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 实时数据
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    HRVRealtimeDataItem(
                        label = "当前RMSSD",
                        value = String.format("%.1f", data.currentRMSSD),
                        unit = "ms"
                    )
                    HRVRealtimeDataItem(
                        label = "心率",
                        value = "${data.currentHeartRate}",
                        unit = "bpm"
                    )
                    HRVRealtimeDataItem(
                        label = "数据质量",
                        value = "${(data.dataQuality * 100).toInt()}",
                        unit = "%"
                    )
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 信号稳定性
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.Center
                ) {
                    Icon(
                        imageVector = if (data.isStable) Icons.Default.AccountBox else Icons.Default.ArrowForward,
                        contentDescription = "信号状态",
                        tint = if (data.isStable) Color.Green else Color(0xFFFFA500),
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = if (data.isStable) "信号稳定" else "信号不稳定",
                        style = MaterialTheme.typography.bodySmall,
                        color = if (data.isStable) Color.Green else Color(0xFFFFA500)
                    )
                }
            }
        }
    }
}

/**
 * HRV实时数据项组件
 */
@Composable
private fun HRVRealtimeDataItem(
    label: String,
    value: String,
    unit: String,
    modifier: Modifier = Modifier
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.secondary
        )
        if (unit.isNotEmpty()) {
            Text(
                text = unit,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * HRV指标概览卡片 - 粉色主题
 * 展示时域指标、频域指标和自主神经指标
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HRVMetricsOverviewCard(
    stats: HRVStats?,
    trendData: List<HRVTrendData>,
    isLoading: Boolean,
    onCardClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        onClick = onCardClick,
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        if (isLoading) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(300.dp),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(
                    color = HRVPrimary,
                    strokeWidth = 3.dp
                )
            }
        } else {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        brush = Brush.verticalGradient(
                            colors = listOf(
                                PinkGradientStart.copy(alpha = 0.1f),
                                PinkGradientEnd.copy(alpha = 0.05f)
                            )
                        )
                    )
                    .padding(20.dp)
            ) {
                // 标题区域
                HRVCardHeader()

                Spacer(modifier = Modifier.height(20.dp))

                // 三个指标分类
                stats?.let { hrvStats ->
                    // 时域指标
                    HRVMetricSection(
                        title = "时域指标",
                        icon = Icons.Default.Info,
                        metrics = listOf(
                            HRVMetricItem("SDNN", "${String.format("%.1f", hrvStats.averageSDNN)}ms", "30-100ms", HRVPrimary),
                            HRVMetricItem("RMSSD", "${String.format("%.1f", hrvStats.averageRMSSD)}ms", "20-50ms", HRVSecondary),
                            HRVMetricItem("pNN50", "${(hrvStats.averageRMSSD * 0.8).toInt()}%", ">10%", HRVAccent)
                        )
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // 频域指标
                    HRVMetricSection(
                        title = "频域指标",
                        icon = Icons.Default.Star,
                        metrics = listOf(
                            HRVMetricItem("VLF", "${(hrvStats.averageSDNN * 2.1).toInt()}", "0.003-0.04Hz", Color(0xFFE91E63)),
                            HRVMetricItem("LF", "${(hrvStats.averageSDNN * 1.5).toInt()}", "0.04-0.15Hz", Color(0xFFAD1457)),
                            HRVMetricItem("HF", "${(hrvStats.averageSDNN * 1.2).toInt()}", "0.15-0.4Hz", Color(0xFFC2185B)),
                            HRVMetricItem("TP", "${(hrvStats.averageSDNN * 5.8).toInt()}", "1000-4000ms²", Color(0xFF880E4F))
                        )
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // 自主神经指标
                    HRVMetricSection(
                        title = "自主神经指标",
                        icon = Icons.Default.Favorite,
                        metrics = listOf(
                            HRVMetricItem("LF/HF", String.format("%.2f", hrvStats.averageLFHFRatio), "0.5-2", Color(0xFFFF4081)),
                            HRVMetricItem("压力指数", "${hrvStats.stressLevel * 20}", "0-100", Color(0xFFFF80AB)),
                            HRVMetricItem("恢复指数", "${hrvStats.recoveryLevel * 20}", "0-100", Color(0xFFFFAB91))
                        )
                    )
                }

                Spacer(modifier = Modifier.height(20.dp))

                // 趋势图预览
                if (trendData.isNotEmpty()) {
                    HRVTrendChart(
                        trendData = trendData,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(120.dp)
                    )
                }
            }
        }
    }
}

/**
 * HRV卡片标题
 */
@Composable
private fun HRVCardHeader() {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.Favorite,
                contentDescription = "HRV",
                tint = HRVPrimary,
                modifier = Modifier.size(28.dp)
            )
            Spacer(modifier = Modifier.width(12.dp))
            Text(
                text = "HRV指标详解",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold,
                color = HRVPrimary
            )
        }

        // 状态指示器
        Box(
            modifier = Modifier
                .background(
                    color = HRVPrimary.copy(alpha = 0.1f),
                    shape = RoundedCornerShape(20.dp)
                )
                .padding(horizontal = 12.dp, vertical = 6.dp)
        ) {
            Text(
                text = "实时监测",
                style = MaterialTheme.typography.bodySmall,
                color = HRVPrimary,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

/**
 * HRV指标分类组件
 */
@Composable
private fun HRVMetricSection(
    title: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    metrics: List<HRVMetricItem>,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        // 分类标题
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.padding(bottom = 12.dp)
        ) {
            Icon(
                imageVector = icon,
                contentDescription = title,
                tint = HRVPrimary,
                modifier = Modifier.size(20.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold,
                color = HRVPrimary
            )
        }

        // 指标网格
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            items(metrics) { metric ->
                HRVMetricCard(metric = metric)
            }
        }
    }
}

/**
 * 单个HRV指标卡片
 */
@Composable
private fun HRVMetricCard(
    metric: HRVMetricItem,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.width(100.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.padding(12.dp)
        ) {
            Text(
                text = metric.value,
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                color = metric.color,
                textAlign = TextAlign.Center
            )
            Spacer(modifier = Modifier.height(4.dp))
            Text(
                text = metric.name,
                style = MaterialTheme.typography.bodySmall,
                color = Color.Gray,
                textAlign = TextAlign.Center
            )
            Spacer(modifier = Modifier.height(2.dp))
            Text(
                text = metric.range,
                style = MaterialTheme.typography.bodySmall,
                color = Color.Gray.copy(alpha = 0.7f),
                textAlign = TextAlign.Center,
                fontSize = 10.sp
            )
        }
    }
}

/**
 * HRV趋势图表
 */
@Composable
fun HRVTrendChart(
    trendData: List<HRVTrendData>,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "7天趋势",
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.SemiBold,
                color = HRVPrimary,
                modifier = Modifier.padding(bottom = 8.dp)
            )

            Canvas(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(80.dp)
            ) {
                drawHRVTrendChart(trendData)
            }
        }
    }
}

/**
 * 绘制HRV趋势图表
 */
private fun DrawScope.drawHRVTrendChart(trendData: List<HRVTrendData>) {
    if (trendData.isEmpty()) return

    val width = size.width
    val height = size.height
    val padding = 20f

    // 绘制RMSSD趋势线
    val rmssdValues = trendData.map { it.avgRMSSD }
    val maxRMSSD = rmssdValues.maxOrNull() ?: 50f
    val minRMSSD = rmssdValues.minOrNull() ?: 20f
    val rmssdRange = maxRMSSD - minRMSSD

    if (rmssdRange > 0) {
        val rmssdPath = Path()

        rmssdValues.forEachIndexed { index, value ->
            val x = padding + (index.toFloat() / (rmssdValues.size - 1)) * (width - 2 * padding)
            val normalizedValue = (value - minRMSSD) / rmssdRange
            val y = height - padding - normalizedValue * (height - 2 * padding)

            if (index == 0) {
                rmssdPath.moveTo(x, y)
            } else {
                rmssdPath.lineTo(x, y)
            }
        }

        // 绘制渐变填充
        drawPath(
            path = rmssdPath,
            brush = Brush.verticalGradient(
                colors = listOf(
                    androidx.compose.ui.graphics.Color(0xFFE91E63).copy(alpha = 0.3f),
                    androidx.compose.ui.graphics.Color(0xFFE91E63).copy(alpha = 0.1f)
                )
            )
        )

        // 绘制趋势线
        drawPath(
            path = rmssdPath,
            color = androidx.compose.ui.graphics.Color(0xFFE91E63),
            style = Stroke(width = 3.dp.toPx())
        )
    }

    // 绘制数据点
    rmssdValues.forEachIndexed { index, value ->
        val x = padding + (index.toFloat() / (rmssdValues.size - 1)) * (width - 2 * padding)
        val normalizedValue = (value - minRMSSD) / rmssdRange
        val y = height - padding - normalizedValue * (height - 2 * padding)

        drawCircle(
            color = androidx.compose.ui.graphics.Color(0xFFE91E63),
            radius = 4.dp.toPx(),
            center = Offset(x, y)
        )

        // 绘制白色内圈
        drawCircle(
            color = androidx.compose.ui.graphics.Color.White,
            radius = 2.dp.toPx(),
            center = Offset(x, y)
        )
    }
}

/**
 * HRV指标数据类
 */
data class HRVMetricItem(
    val name: String,
    val value: String,
    val range: String,
    val color: Color
)
