package com.sdwu.kotlin.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AccountBox
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.ArrowForward
import androidx.compose.material.icons.filled.FavoriteBorder
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.sdwu.kotlin.data.model.*
import com.sdwu.kotlin.data.repository.HRVStats
import kotlin.math.*

/**
 * HRV数据卡片组件
 * 在首页展示HRV统计信息
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HRVDataCard(
    stats: HRVStats?,
    trendData: List<HRVTrendData>,
    isLoading: Boolean,
    onCardClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        onClick = onCardClick,
        modifier = modifier
            .fillMaxWidth()
            .height(200.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        if (isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
                // 标题行
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "HRV",
                            tint = MaterialTheme.colorScheme.secondary,
                            modifier = Modifier.size(24.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "心率变异性",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold
                        )
                    }
                    
                    // 压力水平指示器
                    stats?.stressLevel?.let { level ->
                        StressLevelIndicator(level = level)
                    }
                }
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 统计数据行
                stats?.let { hrvStats ->
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        HRVStatItem(
                            label = "RMSSD",
                            value = String.format("%.1f", hrvStats.averageRMSSD),
                            unit = "ms"
                        )
                        HRVStatItem(
                            label = "SDNN",
                            value = String.format("%.1f", hrvStats.averageSDNN),
                            unit = "ms"
                        )
                        HRVStatItem(
                            label = "LF/HF",
                            value = String.format("%.2f", hrvStats.averageLFHFRatio),
                            unit = ""
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // HRV趋势图预览
                if (trendData.isNotEmpty()) {
                    HRVTrendPreview(
                        trendData = trendData,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(60.dp)
                    )
                }
            }
        }
    }
}

/**
 * HRV统计项组件
 */
@Composable
private fun HRVStatItem(
    label: String,
    value: String,
    unit: String,
    modifier: Modifier = Modifier
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.titleLarge,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.secondary
        )
        if (unit.isNotEmpty()) {
            Text(
                text = unit,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 压力水平指示器
 */
@Composable
private fun StressLevelIndicator(
    level: Int,
    modifier: Modifier = Modifier
) {
    val (color, text) = when (level) {
        1 -> Color.Green to "很低"
        2 -> Color.Blue to "低"
        3 -> Color.Yellow to "中等"
        4 -> Color(0xFFFFA500) to "高"
        5 -> Color.Red to "很高"
        else -> Color.Gray to "未知"
    }
    
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier
            .background(
                color = color.copy(alpha = 0.1f),
                shape = RoundedCornerShape(12.dp)
            )
            .padding(horizontal = 8.dp, vertical = 4.dp)
    ) {
        Icon(
            imageVector = Icons.Default.FavoriteBorder,
            contentDescription = "压力水平",
            tint = color,
            modifier = Modifier.size(12.dp)
        )
        Spacer(modifier = Modifier.width(4.dp))
        Text(
            text = text,
            style = MaterialTheme.typography.bodySmall,
            color = color,
            fontSize = 10.sp
        )
    }
}

/**
 * HRV趋势预览组件
 */
@Composable
fun HRVTrendPreview(
    trendData: List<HRVTrendData>,
    modifier: Modifier = Modifier
) {
    Canvas(
        modifier = modifier
            .clip(RoundedCornerShape(8.dp))
            .background(MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f))
    ) {
        drawHRVTrend(trendData)
    }
}

/**
 * 绘制HRV趋势图
 */
private fun DrawScope.drawHRVTrend(trendData: List<HRVTrendData>) {
    if (trendData.isEmpty()) return
    
    val width = size.width
    val height = size.height
    val padding = 20f
    
    // 绘制RMSSD趋势线
    val rmssdValues = trendData.map { it.avgRMSSD }
    val maxRMSSD = rmssdValues.maxOrNull() ?: 50f
    val minRMSSD = rmssdValues.minOrNull() ?: 20f
    val rmssdRange = maxRMSSD - minRMSSD
    
    if (rmssdRange > 0) {
        val rmssdPath = Path()
        
        rmssdValues.forEachIndexed { index, value ->
            val x = padding + (index.toFloat() / (rmssdValues.size - 1)) * (width - 2 * padding)
            val normalizedValue = (value - minRMSSD) / rmssdRange
            val y = height - padding - normalizedValue * (height - 2 * padding)
            
            if (index == 0) {
                rmssdPath.moveTo(x, y)
            } else {
                rmssdPath.lineTo(x, y)
            }
        }
        
        drawPath(
            path = rmssdPath,
            color = Color.Blue,
            style = Stroke(width = 2.dp.toPx())
        )
    }
    
    // 绘制数据点
    rmssdValues.forEachIndexed { index, value ->
        val x = padding + (index.toFloat() / (rmssdValues.size - 1)) * (width - 2 * padding)
        val normalizedValue = (value - minRMSSD) / rmssdRange
        val y = height - padding - normalizedValue * (height - 2 * padding)
        
        drawCircle(
            color = Color.Blue,
            radius = 3.dp.toPx(),
            center = Offset(x, y)
        )
    }
}

/**
 * HRV测量进度组件
 */
@Composable
fun HRVMeasurementProgress(
    realtimeData: HRVRealtimeData?,
    isMeasuring: Boolean,
    onStartMeasurement: () -> Unit,
    onStopMeasurement: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "HRV测量",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                Button(
                    onClick = if (isMeasuring) onStopMeasurement else onStartMeasurement,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = if (isMeasuring) 
                            MaterialTheme.colorScheme.error 
                        else 
                            MaterialTheme.colorScheme.secondary
                    )
                ) {
                    Text(if (isMeasuring) "停止测量" else "开始测量")
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            realtimeData?.let { data ->
                // 测量进度
                Column {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = "测量进度",
                            style = MaterialTheme.typography.bodyMedium
                        )
                        Text(
                            text = "${(data.measurementProgress * 100).toInt()}%",
                            style = MaterialTheme.typography.bodyMedium,
                            fontWeight = FontWeight.Bold
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(4.dp))
                    
                    LinearProgressIndicator(
                        progress = data.measurementProgress,
                        modifier = Modifier.fillMaxWidth(),
                        color = MaterialTheme.colorScheme.secondary
                    )
                }
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 实时数据
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    HRVRealtimeDataItem(
                        label = "当前RMSSD",
                        value = String.format("%.1f", data.currentRMSSD),
                        unit = "ms"
                    )
                    HRVRealtimeDataItem(
                        label = "心率",
                        value = "${data.currentHeartRate}",
                        unit = "bpm"
                    )
                    HRVRealtimeDataItem(
                        label = "数据质量",
                        value = "${(data.dataQuality * 100).toInt()}",
                        unit = "%"
                    )
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 信号稳定性
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.Center
                ) {
                    Icon(
                        imageVector = if (data.isStable) Icons.Default.AccountBox else Icons.Default.ArrowForward,
                        contentDescription = "信号状态",
                        tint = if (data.isStable) Color.Green else Color(0xFFFFA500),
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = if (data.isStable) "信号稳定" else "信号不稳定",
                        style = MaterialTheme.typography.bodySmall,
                        color = if (data.isStable) Color.Green else Color(0xFFFFA500)
                    )
                }
            }
        }
    }
}

/**
 * HRV实时数据项组件
 */
@Composable
private fun HRVRealtimeDataItem(
    label: String,
    value: String,
    unit: String,
    modifier: Modifier = Modifier
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.secondary
        )
        if (unit.isNotEmpty()) {
            Text(
                text = unit,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}
