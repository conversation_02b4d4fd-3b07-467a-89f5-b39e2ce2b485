package com.sdwu.kotlin

import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.navigation.compose.rememberNavController
import com.sdwu.kotlin.navigation.NavGraph
import com.sdwu.kotlin.ui.theme.KotlinTheme
import com.sdwu.kotlin.utils.ErrorLogger

class MainActivity : ComponentActivity() {

    companion object {
        private const val TAG = "MainActivity"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.d(TAG, "MainActivity onCreate开始")

        try {
            Log.d(TAG, "开始设置Content")
            setContent {
                Log.d(TAG, "进入Compose内容")
                KotlinTheme {
                    Surface(
                        modifier = Modifier.fillMaxSize(),
                        color = MaterialTheme.colorScheme.background
                    ) {
                        Log.d(TAG, "创建NavController")
                        val navController = rememberNavController()

                        // 使用LaunchedEffect来处理可能的异常
                        LaunchedEffect(Unit) {
                            try {
                                Log.d(TAG, "NavController创建成功，开始初始化导航图")
                                ErrorLogger.logInfo(TAG, "导航系统初始化")
                            } catch (e: Exception) {
                                Log.e(TAG, "导航初始化日志记录失败", e)
                                ErrorLogger.logError(TAG, "导航初始化日志记录失败", e)
                            }
                        }

                        NavGraph(navController = navController)
                    }
                }
            }
            Log.d(TAG, "MainActivity onCreate成功")
        } catch (e: Exception) {
            Log.e(TAG, "MainActivity onCreate失败", e)
            ErrorLogger.logError(TAG, "MainActivity onCreate失败", e)
            // 可以在这里显示错误对话框或重启应用
            throw e
        }
    }
}

