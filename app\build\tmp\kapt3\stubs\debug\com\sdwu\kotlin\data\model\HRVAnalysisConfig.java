package com.sdwu.kotlin.data.model;

/**
 * HRV分析配置
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000f\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0087\b\u0018\u00002\u00020\u0001B+\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\u0006\u0010\t\u001a\u00020\n\u00a2\u0006\u0002\u0010\u000bJ\u000f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u0016\u001a\u00020\bH\u00c6\u0003J\t\u0010\u0017\u001a\u00020\nH\u00c6\u0003J7\u0010\u0018\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\nH\u00c6\u0001J\u0013\u0010\u0019\u001a\u00020\u001a2\b\u0010\u001b\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001c\u001a\u00020\u001dH\u00d6\u0001J\t\u0010\u001e\u001a\u00020\u001fH\u00d6\u0001R\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013\u00a8\u0006 "}, d2 = {"Lcom/sdwu/kotlin/data/model/HRVAnalysisConfig;", "", "analysisTypes", "", "Lcom/sdwu/kotlin/data/model/HRVAnalysisType;", "minimumDuration", "", "artifactThreshold", "", "filterSettings", "Lcom/sdwu/kotlin/data/model/HRVFilterSettings;", "(Ljava/util/List;JFLcom/sdwu/kotlin/data/model/HRVFilterSettings;)V", "getAnalysisTypes", "()Ljava/util/List;", "getArtifactThreshold", "()F", "getFilterSettings", "()Lcom/sdwu/kotlin/data/model/HRVFilterSettings;", "getMinimumDuration", "()J", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "hashCode", "", "toString", "", "app_debug"})
public final class HRVAnalysisConfig {
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.sdwu.kotlin.data.model.HRVAnalysisType> analysisTypes = null;
    private final long minimumDuration = 0L;
    private final float artifactThreshold = 0.0F;
    @org.jetbrains.annotations.NotNull()
    private final com.sdwu.kotlin.data.model.HRVFilterSettings filterSettings = null;
    
    public HRVAnalysisConfig(@org.jetbrains.annotations.NotNull()
    java.util.List<? extends com.sdwu.kotlin.data.model.HRVAnalysisType> analysisTypes, long minimumDuration, float artifactThreshold, @org.jetbrains.annotations.NotNull()
    com.sdwu.kotlin.data.model.HRVFilterSettings filterSettings) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.sdwu.kotlin.data.model.HRVAnalysisType> getAnalysisTypes() {
        return null;
    }
    
    public final long getMinimumDuration() {
        return 0L;
    }
    
    public final float getArtifactThreshold() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.sdwu.kotlin.data.model.HRVFilterSettings getFilterSettings() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.sdwu.kotlin.data.model.HRVAnalysisType> component1() {
        return null;
    }
    
    public final long component2() {
        return 0L;
    }
    
    public final float component3() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.sdwu.kotlin.data.model.HRVFilterSettings component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.sdwu.kotlin.data.model.HRVAnalysisConfig copy(@org.jetbrains.annotations.NotNull()
    java.util.List<? extends com.sdwu.kotlin.data.model.HRVAnalysisType> analysisTypes, long minimumDuration, float artifactThreshold, @org.jetbrains.annotations.NotNull()
    com.sdwu.kotlin.data.model.HRVFilterSettings filterSettings) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}