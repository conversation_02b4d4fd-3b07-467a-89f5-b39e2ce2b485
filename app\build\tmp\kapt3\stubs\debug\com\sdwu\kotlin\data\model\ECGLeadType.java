package com.sdwu.kotlin.data.model;

/**
 * 心电图导联类型
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u000e\b\u0086\u0001\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000e\u00a8\u0006\u000f"}, d2 = {"Lcom/sdwu/kotlin/data/model/ECGLeadType;", "", "(Ljava/lang/String;I)V", "LEAD_I", "LEAD_II", "LEAD_III", "AVR", "AVL", "AVF", "V1", "V2", "V3", "V4", "V5", "V6", "app_debug"})
public enum ECGLeadType {
    /*public static final*/ LEAD_I /* = new LEAD_I() */,
    /*public static final*/ LEAD_II /* = new LEAD_II() */,
    /*public static final*/ LEAD_III /* = new LEAD_III() */,
    /*public static final*/ AVR /* = new AVR() */,
    /*public static final*/ AVL /* = new AVL() */,
    /*public static final*/ AVF /* = new AVF() */,
    /*public static final*/ V1 /* = new V1() */,
    /*public static final*/ V2 /* = new V2() */,
    /*public static final*/ V3 /* = new V3() */,
    /*public static final*/ V4 /* = new V4() */,
    /*public static final*/ V5 /* = new V5() */,
    /*public static final*/ V6 /* = new V6() */;
    
    ECGLeadType() {
    }
}