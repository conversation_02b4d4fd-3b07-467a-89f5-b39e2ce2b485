<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <application
        android:name=".KotlinApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Kotlin"
        tools:targetApi="31">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.Kotlin">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- 传统View系统示例Activity -->
        <activity
            android:name=".TraditionalViewActivity"
            android:exported="false"
            android:label="传统View系统示例"
            android:theme="@style/Theme.Kotlin" />

        <!-- ViewBinding示例Activity -->
        <activity
            android:name=".ViewBindingActivity"
            android:exported="false"
            android:label="ViewBinding示例"
            android:theme="@style/Theme.Kotlin" />

        <!-- DataBinding示例Activity -->
        <activity
            android:name=".DataBindingActivity"
            android:exported="false"
            android:label="DataBinding示例"
            android:theme="@style/Theme.Kotlin" />
    </application>

</manifest>