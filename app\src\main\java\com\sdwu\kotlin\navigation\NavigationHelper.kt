package com.sdwu.kotlin.navigation

import android.util.Log
import androidx.navigation.NavController
import androidx.navigation.NavOptions
import com.sdwu.kotlin.utils.ErrorLogger

/**
 * 导航辅助类
 * 提供常用的导航操作方法
 */
class NavigationHelper(private val navController: NavController) {
    
    /**
     * 导航到指定页面，清除返回栈
     */
    fun navigateAndClearStack(route: String) {
        try {
            Log.d("NavigationHelper", "尝试导航并清除返回栈到: $route")
            ErrorLogger.logNavigation("NavigationHelper", "current", route)
            navController.navigate(route) {
                popUpTo(navController.graph.startDestinationId) {
                    inclusive = true
                }
            }
            Log.d("NavigationHelper", "导航并清除返回栈成功")
        } catch (e: Exception) {
            Log.e("NavigationHelper", "导航并清除返回栈失败: $route", e)
            ErrorLogger.logError("NavigationHelper", "导航并清除返回栈失败: $route", e)
            throw e
        }
    }

    /**
     * 导航到指定页面，替换当前页面
     */
    fun navigateAndReplace(route: String) {
        try {
            Log.d("NavigationHelper", "尝试导航并替换当前页面到: $route")
            ErrorLogger.logNavigation("NavigationHelper", "current", route)
            navController.navigate(route) {
                popUpTo(navController.currentDestination?.id ?: return@navigate) {
                    inclusive = true
                }
            }
            Log.d("NavigationHelper", "导航并替换当前页面成功")
        } catch (e: Exception) {
            Log.e("NavigationHelper", "导航并替换当前页面失败: $route", e)
            ErrorLogger.logError("NavigationHelper", "导航并替换当前页面失败: $route", e)
            throw e
        }
    }

    /**
     * 安全导航（检查目标是否存在）
     */
    fun safeNavigate(route: String): Boolean {
        return try {
            Log.d("NavigationHelper", "尝试安全导航到: $route")
            ErrorLogger.logNavigation("NavigationHelper", "current", route)

            // 检查目标路由是否存在
            val graph = navController.graph
            val destination = graph.findNode(route)
            if (destination == null) {
                Log.w("NavigationHelper", "目标路由不存在: $route")
                ErrorLogger.logWarning("NavigationHelper", "目标路由不存在: $route")
                return false
            }

            navController.navigate(route)
            Log.d("NavigationHelper", "安全导航成功")
            true
        } catch (e: Exception) {
            Log.e("NavigationHelper", "安全导航失败: $route", e)
            ErrorLogger.logError("NavigationHelper", "安全导航失败: $route", e)
            false
        }
    }
    
    /**
     * 带动画的导航
     */
    fun navigateWithAnimation(route: String) {
        val navOptions = NavOptions.Builder()
            .setEnterAnim(android.R.anim.slide_in_left)
            .setExitAnim(android.R.anim.slide_out_right)
            .setPopEnterAnim(android.R.anim.slide_in_left)
            .setPopExitAnim(android.R.anim.slide_out_right)
            .build()
            
        navController.navigate(route, navOptions)
    }
    
    /**
     * 返回到首页
     */
    fun navigateToHome() {
        navController.navigate(Routes.HOME) {
            popUpTo(Routes.HOME) {
                inclusive = true
            }
        }
    }
}
