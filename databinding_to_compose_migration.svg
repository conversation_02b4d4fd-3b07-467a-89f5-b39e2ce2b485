<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义渐变色 -->
    <linearGradient id="dataBindingGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2E7D32;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="composeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6200EA;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3700B3;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="migrationGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF9800;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F57C00;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="warningGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F44336;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#C62828;stop-opacity:1" />
    </linearGradient>
    
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
            refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="3" dy="3" stdDeviation="4" flood-color="#00000025"/>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="1600" height="1400" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="800" y="35" text-anchor="middle" font-family="Arial, sans-serif" 
        font-size="28" font-weight="bold" fill="#333">
    🔄 DataBinding 到 Compose 迁移指南
  </text>
  
  <!-- 迁移可行性分析 -->
  <g id="feasibility-analysis">
    <rect x="100" y="70" width="1400" height="80" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2" rx="8" filter="url(#shadow)"/>
    <text x="800" y="100" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="20" font-weight="bold" fill="#2E7D32">
      ✅ 答案：完全可以！DataBinding项目可以渐进式迁移到Compose
    </text>
    <text x="120" y="125" font-family="Arial, sans-serif" font-size="14" fill="#333">
      • DataBinding和Compose可以在同一个项目中共存  • ViewModel层可以完全复用  • 可以按页面/功能模块逐步迁移  • 无需重写整个应用
    </text>
  </g>
  
  <!-- 迁移策略对比 -->
  <g id="migration-strategies">
    <rect x="100" y="170" width="1400" height="50" fill="url(#migrationGradient)" rx="8" filter="url(#shadow)"/>
    <text x="800" y="200" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="18" font-weight="bold" fill="white">
      🛣️ 三种迁移策略
    </text>
    
    <!-- 策略1：渐进式迁移 -->
    <rect x="120" y="240" width="440" height="200" fill="#FFF3E0" stroke="#FF9800" stroke-width="2" rx="8"/>
    <text x="340" y="265" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#F57C00">
      🐌 渐进式迁移 (推荐)
    </text>
    <text x="140" y="285" font-family="Arial, sans-serif" font-size="13" fill="#333">
      <tspan font-weight="bold">优势：</tspan>
    </text>
    <text x="140" y="300" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 风险最低，可随时回滚
    </text>
    <text x="140" y="315" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 团队学习成本分散
    </text>
    <text x="140" y="330" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 业务功能不受影响
    </text>
    <text x="140" y="350" font-family="Arial, sans-serif" font-size="13" fill="#333">
      <tspan font-weight="bold">适用场景：</tspan>
    </text>
    <text x="140" y="365" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 大型项目
    </text>
    <text x="140" y="380" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 生产环境稳定性要求高
    </text>
    <text x="140" y="395" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 团队对Compose不熟悉
    </text>
    <text x="140" y="415" font-family="Arial, sans-serif" font-size="13" fill="#333">
      <tspan font-weight="bold">时间周期：</tspan> 3-12个月
    </text>
    
    <!-- 策略2：模块化迁移 -->
    <rect x="580" y="240" width="440" height="200" fill="#E3F2FD" stroke="#2196F3" stroke-width="2" rx="8"/>
    <text x="800" y="265" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#1565C0">
      🏗️ 模块化迁移
    </text>
    <text x="600" y="285" font-family="Arial, sans-serif" font-size="13" fill="#333">
      <tspan font-weight="bold">优势：</tspan>
    </text>
    <text x="600" y="300" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 按功能模块划分
    </text>
    <text x="600" y="315" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 便于团队分工
    </text>
    <text x="600" y="330" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 测试范围可控
    </text>
    <text x="600" y="350" font-family="Arial, sans-serif" font-size="13" fill="#333">
      <tspan font-weight="bold">适用场景：</tspan>
    </text>
    <text x="600" y="365" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 中型项目
    </text>
    <text x="600" y="380" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 模块化架构清晰
    </text>
    <text x="600" y="395" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 团队有一定Compose经验
    </text>
    <text x="600" y="415" font-family="Arial, sans-serif" font-size="13" fill="#333">
      <tspan font-weight="bold">时间周期：</tspan> 2-6个月
    </text>
    
    <!-- 策略3：全量重写 -->
    <rect x="1040" y="240" width="440" height="200" fill="#FFEBEE" stroke="#F44336" stroke-width="2" rx="8"/>
    <text x="1260" y="265" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#C62828">
      ⚡ 全量重写 (高风险)
    </text>
    <text x="1060" y="285" font-family="Arial, sans-serif" font-size="13" fill="#333">
      <tspan font-weight="bold">优势：</tspan>
    </text>
    <text x="1060" y="300" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 架构最干净
    </text>
    <text x="1060" y="315" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 技术债务清零
    </text>
    <text x="1060" y="330" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 开发效率最高
    </text>
    <text x="1060" y="350" font-family="Arial, sans-serif" font-size="13" fill="#333">
      <tspan font-weight="bold">风险：</tspan>
    </text>
    <text x="1060" y="365" font-family="Arial, sans-serif" font-size="12" fill="#C62828">
      • 业务功能回归测试量大
    </text>
    <text x="1060" y="380" font-family="Arial, sans-serif" font-size="12" fill="#C62828">
      • 可能引入新bug
    </text>
    <text x="1060" y="395" font-family="Arial, sans-serif" font-size="12" fill="#C62828">
      • 开发周期长
    </text>
    <text x="1060" y="415" font-family="Arial, sans-serif" font-size="13" fill="#333">
      <tspan font-weight="bold">时间周期：</tspan> 6-18个月
    </text>
  </g>
  
  <!-- 渐进式迁移详细步骤 -->
  <g id="migration-steps">
    <rect x="100" y="460" width="1400" height="50" fill="url(#composeGradient)" rx="8" filter="url(#shadow)"/>
    <text x="800" y="490" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="18" font-weight="bold" fill="white">
      📋 渐进式迁移详细步骤 (基于您的项目)
    </text>
    
    <!-- 步骤1：环境准备 -->
    <rect x="120" y="530" width="680" height="120" fill="#F3E5F5" stroke="#6200EA" stroke-width="2" rx="8"/>
    <text x="460" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#6200EA">
      步骤1：环境准备 (1-2天)
    </text>
    <text x="140" y="575" font-family="Arial, sans-serif" font-size="13" fill="#333">
      <tspan font-weight="bold">build.gradle配置：</tspan>
    </text>
    <text x="140" y="590" font-family="Courier New, monospace" font-size="11" fill="#333">
      compileOptions { sourceCompatibility JavaVersion.VERSION_1_8 }
    </text>
    <text x="140" y="605" font-family="Courier New, monospace" font-size="11" fill="#333">
      composeOptions { kotlinCompilerExtensionVersion '1.5.4' }
    </text>
    <text x="140" y="620" font-family="Courier New, monospace" font-size="11" fill="#333">
      buildFeatures { compose true; dataBinding true } // 两者共存
    </text>
    <text x="140" y="635" font-family="Arial, sans-serif" font-size="12" fill="#333">
      ✅ 保持DataBinding启用，添加Compose支持
    </text>
    
    <!-- 步骤2：ViewModel适配 -->
    <rect x="820" y="530" width="680" height="120" fill="#F3E5F5" stroke="#6200EA" stroke-width="2" rx="8"/>
    <text x="1160" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#6200EA">
      步骤2：ViewModel适配 (2-3天)
    </text>
    <text x="840" y="575" font-family="Arial, sans-serif" font-size="13" fill="#333">
      <tspan font-weight="bold">现有ViewModel保持不变：</tspan>
    </text>
    <text x="840" y="590" font-family="Courier New, monospace" font-size="11" fill="#333">
      // DataBindingViewModel.kt 保持原样
    </text>
    <text x="840" y="605" font-family="Courier New, monospace" font-size="11" fill="#333">
      val user: LiveData&lt;User?&gt; = _user // 继续使用
    </text>
    <text x="840" y="620" font-family="Arial, sans-serif" font-size="12" fill="#333">
      ✅ 或者添加StateFlow支持：
    </text>
    <text x="840" y="635" font-family="Courier New, monospace" font-size="11" fill="#333">
      val userState = user.asStateFlow() // 为Compose准备
    </text>
  </g>

  <!-- 步骤3-6 -->
  <g id="migration-steps-2">
    <!-- 步骤3：创建Compose版本 -->
    <rect x="120" y="670" width="680" height="120" fill="#F3E5F5" stroke="#6200EA" stroke-width="2" rx="8"/>
    <text x="460" y="695" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#6200EA">
      步骤3：创建Compose版本 (3-5天)
    </text>
    <text x="140" y="715" font-family="Arial, sans-serif" font-size="13" fill="#333">
      <tspan font-weight="bold">新建ComposeActivity：</tspan>
    </text>
    <text x="140" y="730" font-family="Courier New, monospace" font-size="11" fill="#333">
      @Composable fun UserProfileScreen(viewModel: DataBindingViewModel)
    </text>
    <text x="140" y="745" font-family="Courier New, monospace" font-size="11" fill="#333">
      val user by viewModel.user.observeAsState()
    </text>
    <text x="140" y="760" font-family="Courier New, monospace" font-size="11" fill="#333">
      Text(text = user?.name ?: "Loading...")
    </text>
    <text x="140" y="775" font-family="Arial, sans-serif" font-size="12" fill="#333">
      ✅ 复用现有ViewModel，用observeAsState()桥接
    </text>

    <!-- 步骤4：A/B测试 -->
    <rect x="820" y="670" width="680" height="120" fill="#F3E5F5" stroke="#6200EA" stroke-width="2" rx="8"/>
    <text x="1160" y="695" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#6200EA">
      步骤4：A/B测试对比 (1-2周)
    </text>
    <text x="840" y="715" font-family="Arial, sans-serif" font-size="13" fill="#333">
      <tspan font-weight="bold">同时保持两个版本：</tspan>
    </text>
    <text x="840" y="730" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • DataBindingActivity (原版本)
    </text>
    <text x="840" y="745" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • ComposeActivity (新版本)
    </text>
    <text x="840" y="760" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 通过Feature Flag控制显示哪个版本
    </text>
    <text x="840" y="775" font-family="Arial, sans-serif" font-size="12" fill="#333">
      ✅ 确保功能完全一致后再切换
    </text>

    <!-- 步骤5：逐步替换 -->
    <rect x="120" y="810" width="680" height="120" fill="#F3E5F5" stroke="#6200EA" stroke-width="2" rx="8"/>
    <text x="460" y="835" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#6200EA">
      步骤5：逐步替换 (按优先级)
    </text>
    <text x="140" y="855" font-family="Arial, sans-serif" font-size="13" fill="#333">
      <tspan font-weight="bold">迁移优先级：</tspan>
    </text>
    <text x="140" y="870" font-family="Arial, sans-serif" font-size="12" fill="#333">
      1. 新功能页面 → 直接用Compose开发
    </text>
    <text x="140" y="885" font-family="Arial, sans-serif" font-size="12" fill="#333">
      2. 简单页面 → HomeScreen, SettingsScreen
    </text>
    <text x="140" y="900" font-family="Arial, sans-serif" font-size="12" fill="#333">
      3. 复杂页面 → ProfileScreen (表单较多)
    </text>
    <text x="140" y="915" font-family="Arial, sans-serif" font-size="12" fill="#333">
      4. 核心页面 → 最后迁移，确保稳定性
    </text>

    <!-- 步骤6：清理工作 -->
    <rect x="820" y="810" width="680" height="120" fill="#F3E5F5" stroke="#6200EA" stroke-width="2" rx="8"/>
    <text x="1160" y="835" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#6200EA">
      步骤6：清理工作 (1-2周)
    </text>
    <text x="840" y="855" font-family="Arial, sans-serif" font-size="13" fill="#333">
      <tspan font-weight="bold">迁移完成后：</tspan>
    </text>
    <text x="840" y="870" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 删除旧的XML布局文件
    </text>
    <text x="840" y="885" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 移除DataBinding相关代码
    </text>
    <text x="840" y="900" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 更新build.gradle配置
    </text>
    <text x="840" y="915" font-family="Arial, sans-serif" font-size="12" fill="#333">
      ✅ 保持ViewModel层，只需调整状态管理方式
    </text>
  </g>

  <!-- 代码对比示例 -->
  <g id="code-comparison">
    <rect x="100" y="950" width="1400" height="50" fill="url(#migrationGradient)" rx="8" filter="url(#shadow)"/>
    <text x="800" y="980" text-anchor="middle" font-family="Arial, sans-serif"
          font-size="18" font-weight="bold" fill="white">
      💻 代码迁移对比 (基于您的项目)
    </text>

    <!-- DataBinding原代码 -->
    <rect x="120" y="1020" width="680" height="180" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2" rx="8"/>
    <text x="460" y="1045" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#4CAF50">
      📋 DataBinding 原代码
    </text>
    <text x="140" y="1065" font-family="Arial, sans-serif" font-size="13" fill="#333">
      <tspan font-weight="bold">Activity:</tspan>
    </text>
    <text x="140" y="1080" font-family="Courier New, monospace" font-size="10" fill="#333">
      binding = DataBindingUtil.setContentView(this, R.layout.activity_data_binding)
    </text>
    <text x="140" y="1095" font-family="Courier New, monospace" font-size="10" fill="#333">
      binding.viewModel = viewModel
    </text>
    <text x="140" y="1110" font-family="Courier New, monospace" font-size="10" fill="#333">
      binding.lifecycleOwner = this
    </text>
    <text x="140" y="1130" font-family="Arial, sans-serif" font-size="13" fill="#333">
      <tspan font-weight="bold">XML Layout:</tspan>
    </text>
    <text x="140" y="1145" font-family="Courier New, monospace" font-size="10" fill="#333">
      &lt;TextView android:text="@{viewModel.user.name}"
    </text>
    <text x="140" y="1160" font-family="Courier New, monospace" font-size="10" fill="#333">
                android:visibility="@{viewModel.isLoading ? View.VISIBLE : View.GONE}" /&gt;
    </text>
    <text x="140" y="1180" font-family="Arial, sans-serif" font-size="13" fill="#333">
      <tspan font-weight="bold">ViewModel:</tspan> 保持不变 ✅
    </text>

    <!-- Compose新代码 -->
    <rect x="820" y="1020" width="680" height="180" fill="#F3E5F5" stroke="#6200EA" stroke-width="2" rx="8"/>
    <text x="1160" y="1045" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#6200EA">
      🚀 Compose 新代码
    </text>
    <text x="840" y="1065" font-family="Arial, sans-serif" font-size="13" fill="#333">
      <tspan font-weight="bold">Activity:</tspan>
    </text>
    <text x="840" y="1080" font-family="Courier New, monospace" font-size="10" fill="#333">
      setContent { UserProfileScreen(viewModel) }
    </text>
    <text x="840" y="1100" font-family="Arial, sans-serif" font-size="13" fill="#333">
      <tspan font-weight="bold">Composable:</tspan>
    </text>
    <text x="840" y="1115" font-family="Courier New, monospace" font-size="10" fill="#333">
      @Composable fun UserProfileScreen(viewModel: DataBindingViewModel) {
    </text>
    <text x="840" y="1130" font-family="Courier New, monospace" font-size="10" fill="#333">
          val user by viewModel.user.observeAsState()
    </text>
    <text x="840" y="1145" font-family="Courier New, monospace" font-size="10" fill="#333">
          val isLoading by viewModel.isLoading.observeAsState(false)
    </text>
    <text x="840" y="1160" font-family="Courier New, monospace" font-size="10" fill="#333">
          if (isLoading) Text("Loading...") else Text(user?.name ?: "")
    </text>
    <text x="840" y="1180" font-family="Arial, sans-serif" font-size="13" fill="#333">
      <tspan font-weight="bold">ViewModel:</tspan> 完全复用 ✅
    </text>
  </g>

  <!-- 注意事项和最佳实践 -->
  <g id="best-practices">
    <rect x="100" y="1220" width="1400" height="140" fill="#FFF3E0" stroke="#FF9800" stroke-width="2" rx="8" filter="url(#shadow)"/>
    <text x="800" y="1250" text-anchor="middle" font-family="Arial, sans-serif"
          font-size="18" font-weight="bold" fill="#F57C00">
      ⚠️ 迁移注意事项和最佳实践
    </text>

    <text x="120" y="1275" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#F57C00">
      🔧 技术要点：
    </text>
    <text x="120" y="1290" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 使用 observeAsState() 桥接 LiveData 到 Compose  • ViewModel 层完全不需要修改  • 可以在同一个 Activity 中混用 DataBinding 和 Compose
    </text>

    <text x="120" y="1315" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#F57C00">
      📋 测试策略：
    </text>
    <text x="120" y="1330" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 每个页面迁移后进行完整功能测试  • 保持原有的单元测试和集成测试  • 新增 Compose UI 测试用例
    </text>

    <text x="800" y="1275" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#F57C00">
      🎯 性能优化：
    </text>
    <text x="800" y="1290" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 迁移后性能通常会提升  • 注意 Compose 的重组优化  • 使用 remember 避免不必要的重组
    </text>

    <text x="800" y="1315" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#F57C00">
      👥 团队协作：
    </text>
    <text x="800" y="1330" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 制定迁移计划和时间表  • 团队成员分工明确  • 定期 Code Review 确保质量
    </text>
  </g>

  <!-- 连接箭头 -->
  <g id="arrows">
    <line x1="340" y1="440" x2="340" y2="525" stroke="#FF9800" stroke-width="4" marker-end="url(#arrowhead)"/>
    <line x1="800" y1="440" x2="800" y2="525" stroke="#2196F3" stroke-width="4" marker-end="url(#arrowhead)"/>
    <line x1="1260" y1="440" x2="1260" y2="525" stroke="#F44336" stroke-width="4" marker-end="url(#arrowhead)"/>

    <line x1="460" y1="650" x2="460" y2="665" stroke="#6200EA" stroke-width="3" marker-end="url(#arrowhead)"/>
    <line x1="1160" y1="650" x2="1160" y2="665" stroke="#6200EA" stroke-width="3" marker-end="url(#arrowhead)"/>
    <line x1="460" y1="790" x2="460" y2="805" stroke="#6200EA" stroke-width="3" marker-end="url(#arrowhead)"/>
    <line x1="1160" y1="790" x2="1160" y2="805" stroke="#6200EA" stroke-width="3" marker-end="url(#arrowhead)"/>

    <line x1="800" y1="930" x2="800" y2="945" stroke="#FF9800" stroke-width="4" marker-end="url(#arrowhead)"/>
  </g>
</svg>
