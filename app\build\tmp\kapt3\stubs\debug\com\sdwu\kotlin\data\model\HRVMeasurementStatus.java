package com.sdwu.kotlin.data.model;

/**
 * HRV测量状态
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0006\b\u0086\u0001\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/sdwu/kotlin/data/model/HRVMeasurementStatus;", "", "(Ljava/lang/String;I)V", "MEASURING", "COMPLETED", "FAILED", "CANCELLED", "app_debug"})
public enum HRVMeasurementStatus {
    /*public static final*/ MEASURING /* = new MEASURING() */,
    /*public static final*/ COMPLETED /* = new COMPLETED() */,
    /*public static final*/ FAILED /* = new FAILED() */,
    /*public static final*/ CANCELLED /* = new CANCELLED() */;
    
    HRVMeasurementStatus() {
    }
}