{"logs": [{"outputFile": "com.sdwu.kotlin.app-mergeDebugResources-70:/values-ru/values-ru.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1691789794c9b474ae1fde820463afff\\transformed\\material-1.9.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,381,459,537,621,719,810,907,1044,1136,1202,1301,1378,1441,1559,1620,1685,1742,1812,1873,1927,2043,2100,2162,2216,2290,2418,2506,2592,2729,2813,2898,2989,3043,3094,3160,3232,3310,3406,3486,3562,3639,3716,3805,3878,3968,4063,4137,4218,4311,4366,4432,4518,4603,4665,4729,4792,4890,4990,5085,5187,5245,5300", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69", "endColumns": "12,77,77,83,97,90,96,136,91,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,85,136,83,84,90,53,50,65,71,77,95,79,75,76,76,88,72,89,94,73,80,92,54,65,85,84,61,63,62,97,99,94,101,57,54,79", "endOffsets": "376,454,532,616,714,805,902,1039,1131,1197,1296,1373,1436,1554,1615,1680,1737,1807,1868,1922,2038,2095,2157,2211,2285,2413,2501,2587,2724,2808,2893,2984,3038,3089,3155,3227,3305,3401,3481,3557,3634,3711,3800,3873,3963,4058,4132,4213,4306,4361,4427,4513,4598,4660,4724,4787,4885,4985,5080,5182,5240,5295,5375"}, "to": {"startLines": "2,35,36,37,38,39,40,41,42,50,52,53,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3148,3226,3304,3388,3486,3577,3674,3811,4529,4673,4772,5021,5084,5202,5263,5328,5385,5455,5516,5570,5686,5743,5805,5859,5933,6061,6149,6235,6372,6456,6541,6632,6686,6737,6803,6875,6953,7049,7129,7205,7282,7359,7448,7521,7611,7706,7780,7861,7954,8009,8075,8161,8246,8308,8372,8435,8533,8633,8728,8830,8888,9254", "endLines": "7,35,36,37,38,39,40,41,42,50,52,53,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,110", "endColumns": "12,77,77,83,97,90,96,136,91,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,85,136,83,84,90,53,50,65,71,77,95,79,75,76,76,88,72,89,94,73,80,92,54,65,85,84,61,63,62,97,99,94,101,57,54,79", "endOffsets": "426,3221,3299,3383,3481,3572,3669,3806,3898,4590,4767,4844,5079,5197,5258,5323,5380,5450,5511,5565,5681,5738,5800,5854,5928,6056,6144,6230,6367,6451,6536,6627,6681,6732,6798,6870,6948,7044,7124,7200,7277,7354,7443,7516,7606,7701,7775,7856,7949,8004,8070,8156,8241,8303,8367,8430,8528,8628,8723,8825,8883,8938,9329"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8d5d80ec9ea4b92e6a394c21abf0511b\\transformed\\core-1.9.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "115", "startColumns": "4", "startOffsets": "9659", "endColumns": "100", "endOffsets": "9755"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b599f6252b24759b04dd323019160ed1\\transformed\\material3-1.0.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,132,213", "endColumns": "76,80,77", "endOffsets": "127,208,286"}, "to": {"startLines": "45,48,51", "startColumns": "4,4,4", "startOffsets": "4079,4356,4595", "endColumns": "76,80,77", "endOffsets": "4151,4432,4668"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bc227b5d8c8bab74364961e342d51b40\\transformed\\appcompat-1.6.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,888,980,1074,1169,1262,1357,1451,1547,1642,1734,1826,1915,2021,2128,2226,2335,2442,2556,2722,2822", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "215,317,416,502,607,728,807,883,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,1910,2016,2123,2221,2330,2437,2551,2717,2817,2899"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,113", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "431,546,648,747,833,938,1059,1138,1214,1306,1400,1495,1588,1683,1777,1873,1968,2060,2152,2241,2347,2454,2552,2661,2768,2882,3048,9505", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "541,643,742,828,933,1054,1133,1209,1301,1395,1490,1583,1678,1772,1868,1963,2055,2147,2236,2342,2449,2547,2656,2763,2877,3043,3143,9582"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2e1e0a755749a1238f97434281347ef6\\transformed\\ui-1.4.2\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,379,481,573,655,745,833,915,986,1056,1140,1227,1299,1383,1453", "endColumns": "92,82,97,101,91,81,89,87,81,70,69,83,86,71,83,69,122", "endOffsets": "193,276,374,476,568,650,740,828,910,981,1051,1135,1222,1294,1378,1448,1571"}, "to": {"startLines": "43,44,46,47,49,54,55,106,107,108,109,111,112,114,116,117,118", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3903,3996,4156,4254,4437,4849,4931,8943,9031,9113,9184,9334,9418,9587,9760,9844,9914", "endColumns": "92,82,97,101,91,81,89,87,81,70,69,83,86,71,83,69,122", "endOffsets": "3991,4074,4249,4351,4524,4926,5016,9026,9108,9179,9249,9413,9500,9654,9839,9909,10032"}}]}]}