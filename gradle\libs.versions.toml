[versions] 
agp = "8.3.1"
kotlin = "1.9.0"
coreKtx = "1.10.1"
junit = "4.13.2"
junitVersion = "1.1.5"
espressoCore = "3.5.1"
appcompat = "1.6.1"
material = "1.10.0"
constraintlayout = "2.1.4"
lifecycleKtx = "2.6.2"
fragmentKtx = "1.6.1"
activityKtx = "1.7.2"
kotlinxCoroutinesAndroid = "1.7.3"
annotationVersion = "1.7.0"
commonVersion = "0.2.12-SNAPSHOT"
#websocketVersion = "0.1.1-SNAPSHOT"
jwebsocketVersion = "1.5.7"
autosizeVersion = "1.2.1"
multidexVersion = "2.0.1"
gsonVersion = "2.10.1"
mmkvVersion = "1.2.16"
glideVersion = "4.15.1"
glideTransformations = "4.3.0"
okgoVersion = "3.0.4"
okhttputilsVersion = "2.6.2"
lottieVersion = "6.0.0"
liveEventBusVersion = "1.8.0"
okhttpVersion = "4.10.0"
retrofitVersion = "2.9.0"
cameraxVersion = "1.4.0-beta02"
guavaVersion = "32.0.0-android"
workVersion = "2.9.0"
mpAndroidChartVersion = "v3.1.0"
media3Version = "1.3.1"
lpIotLinkkitVersion = "1.7.3.8"
libphonenumberVersion = "8.13.31"
ccpVersion = "2.7.0"
smartRefreshVersion = "3.0.0-alpha"
pictureselectorVersion = "v3.11.2"

[libraries]
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
androidx-appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
androidx-constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout", version.ref = "constraintlayout" }

lifecycle-viewmodel-ktx = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-ktx", version.ref = "lifecycleKtx" }
lifecycle-livedata-ktx = { group = "androidx.lifecycle", name = "lifecycle-livedata-ktx", version.ref = "lifecycleKtx" }
lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "lifecycleKtx" }
fragment-ktx = { group = "androidx.fragment", name = "fragment-ktx", version.ref = "fragmentKtx" }
activity-ktx = { group = "androidx.activity", name = "activity-ktx", version.ref = "activityKtx" }
kotlinx-coroutines-android = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-android", version.ref = "kotlinxCoroutinesAndroid" }
annotation = { group = "androidx.annotation", name = "annotation", version.ref = "annotationVersion" }
common = { group = "com.airdoc.component", name = "common", version.ref = "commonVersion" }
#websocket = { group = "com.airdoc.component", name = "websocket", version.ref = "websocketVersion" }
java-websocket = { group = "org.java-websocket", name = "Java-WebSocket", version.ref = "jwebsocketVersion" }
autosize = { group = "me.jessyan", name = "autosize", version.ref = "autosizeVersion" }
multidex = { group = "androidx.multidex", name = "multidex", version.ref = "multidexVersion" }
gson = { group = "com.google.code.gson", name = "gson", version.ref = "gsonVersion" }
mmkv-static = { group = "com.tencent", name = "mmkv-static", version.ref = "mmkvVersion" }
glide = { group = "com.github.bumptech.glide", name = "glide", version.ref = "glideVersion" }
glide-okhttp3-integration = { group = "com.github.bumptech.glide", name = "okhttp3-integration", version.ref = "glideVersion" }
glide-compiler = { group = "com.github.bumptech.glide", name = "compiler", version.ref = "glideVersion" }
glide-transformations = { group = "jp.wasabeef", name = "glide-transformations", version.ref = "glideTransformations" }
okgo = { group = "com.lzy.net", name = "okgo", version.ref = "okgoVersion" }
okhttputils = { group = "com.zhy", name = "okhttputils", version.ref = "okhttputilsVersion" }
lottie = { group = "com.airbnb.android", name = "lottie", version.ref = "lottieVersion" }
live-event-bus-x = { group = "io.github.jeremyliao", name = "live-event-bus-x", version.ref = "liveEventBusVersion" }
lebx-processor-gson = { group = "io.github.jeremyliao", name = "lebx-processor-gson", version.ref = "liveEventBusVersion" }
okhttp = { group = "com.squareup.okhttp3", name = "okhttp", version.ref = "okhttpVersion" }
retrofit = { group = "com.squareup.retrofit2", name = "retrofit", version.ref = "retrofitVersion" }
retrofit-converter-gson = { group = "com.squareup.retrofit2", name = "converter-gson", version.ref = "retrofitVersion" }
okhttp-logging-interceptor = { group = "com.squareup.okhttp3", name = "logging-interceptor", version.ref = "okhttpVersion" }

camera-core = { group = "androidx.camera", name = "camera-core", version.ref = "cameraxVersion" }
camera-view = { group = "androidx.camera", name = "camera-view", version.ref = "cameraxVersion" }
camera-camera2 = { group = "androidx.camera", name = "camera-camera2", version.ref = "cameraxVersion" }
camera-lifecycle = { group = "androidx.camera", name = "camera-lifecycle", version.ref = "cameraxVersion" }
guava = { group = "com.google.guava", name = "guava", version.ref = "guavaVersion" }
work-runtime-ktx = { group = "androidx.work", name = "work-runtime-ktx", version.ref = "workVersion" }
mp-android-chart = { group = "com.github.PhilJay", name = "MPAndroidChart", version.ref = "mpAndroidChartVersion" }

media3-exoplayer = { group = "androidx.media3", name = "media3-exoplayer", version.ref = "media3Version" }
media3-ui = { group = "androidx.media3", name = "media3-ui", version.ref = "media3Version" }
media3-common = { group = "androidx.media3", name = "media3-common", version.ref = "media3Version" }

lp-iot-linkkit = { group = "com.aliyun.alink.linksdk", name = "lp-iot-linkkit", version.ref = "lpIotLinkkitVersion" }
libphonenumber = { group = "com.googlecode.libphonenumber", name = "libphonenumber", version.ref = "libphonenumberVersion" }
ccp = { group = "com.hbb20", name = "ccp", version.ref = "ccpVersion" }

#核心必须依赖
refresh-layout-kernel = { group = "io.github.scwang90", name = "refresh-layout-kernel", version.ref = "smartRefreshVersion" }
#经典刷新头
refresh-header-classics = { group = "io.github.scwang90", name = "refresh-header-classics", version.ref = "smartRefreshVersion" }
#雷达刷新头
refresh-header-radar = { group = "io.github.scwang90", name = "refresh-header-radar", version.ref = "smartRefreshVersion" }
#虚拟刷新头
refresh-header-falsify = { group = "io.github.scwang90", name = "refresh-header-falsify", version.ref = "smartRefreshVersion" }
#谷歌刷新头
refresh-header-material = { group = "io.github.scwang90", name = "refresh-header-material", version.ref = "smartRefreshVersion" }
#二级刷新头
refresh-header-two-level = { group = "io.github.scwang90", name = "refresh-header-two-level", version.ref = "smartRefreshVersion" }
#球脉冲加载
refresh-footer-ball = { group = "io.github.scwang90", name = "refresh-footer-ball", version.ref = "smartRefreshVersion" }
#经典加载
refresh-footer-classics = { group = "io.github.scwang90", name = "refresh-footer-classics", version.ref = "smartRefreshVersion" }

#PictureSelector 基础 (必须)
pictureselector = { group = "io.github.lucksiege", name = "pictureselector", version.ref = "pictureselectorVersion" }
#图片压缩 (按需引入)
pictureselector-compress = { group = "io.github.lucksiege", name = "compress", version.ref = "pictureselectorVersion" }
#图片裁剪 (按需引入)
pictureselector-ucrop = { group = "io.github.lucksiege", name = "ucrop", version.ref = "pictureselectorVersion" }
#自定义相机 (按需引入)
pictureselector-camerax = { group = "io.github.lucksiege", name = "camerax", version.ref = "pictureselectorVersion" }

[plugins]
androidApplication = { id = "com.android.application", version.ref = "agp" }
jetbrainsKotlinAndroid = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
androidLibrary = { id = "com.android.library", version.ref = "agp" }
