package com.sdwu.kotlin.screens

import android.util.Log
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.sdwu.kotlin.utils.ErrorLogger
import com.sdwu.kotlin.utils.NavigationErrorHandler

/**
 * 简化的个人资料页面
 * 用于测试导航是否正常工作
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SimpleProfileScreen(navController: NavController) {
    Log.d("SimpleProfileScreen", "=== SimpleProfileScreen开始初始化 ===")
    
    val context = LocalContext.current
    
    // 记录页面加载
    LaunchedEffect(Unit) {
        try {
            Log.d("SimpleProfileScreen", "页面加载开始")
            ErrorLogger.logInfo("SimpleProfileScreen", "简化个人资料页面加载")
            
            // 检查Context
            Log.d("SimpleProfileScreen", "Context类型: ${context.javaClass.name}")
            Log.d("SimpleProfileScreen", "ApplicationContext类型: ${context.applicationContext.javaClass.name}")
            
            Log.d("SimpleProfileScreen", "页面加载完成")
        } catch (e: Exception) {
            Log.e("SimpleProfileScreen", "页面加载失败", e)
            ErrorLogger.logError("SimpleProfileScreen", "页面加载失败", e)
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 顶部导航栏
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = { 
                Log.d("SimpleProfileScreen", "点击返回按钮")
                val success = NavigationErrorHandler.safePopBackStack(navController, "simple_profile")
                Log.d("SimpleProfileScreen", "返回结果: $success")
            }) {
                Icon(Icons.Default.ArrowBack, contentDescription = "返回")
            }
            Text(
                text = "个人资料 (简化版)",
                style = MaterialTheme.typography.headlineMedium,
                modifier = Modifier
                    .padding(start = 8.dp)
                    .weight(1f)
            )
        }

        Spacer(modifier = Modifier.height(32.dp))

        // 简单的内容
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "这是一个简化的个人资料页面",
                    style = MaterialTheme.typography.bodyLarge,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                Text(
                    text = "用于测试导航功能是否正常",
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                Text(
                    text = "如果您能看到这个页面，说明导航成功了！",
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 测试按钮
        Button(
            onClick = { 
                Log.d("SimpleProfileScreen", "点击设置按钮")
                val success = NavigationErrorHandler.safeNavigateTo(
                    navController = navController,
                    route = "settings",
                    from = "simple_profile"
                )
                Log.d("SimpleProfileScreen", "导航到设置结果: $success")
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("前往设置")
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 返回首页按钮
        OutlinedButton(
            onClick = { 
                Log.d("SimpleProfileScreen", "点击返回首页按钮")
                val success = NavigationErrorHandler.safeNavigateTo(
                    navController = navController,
                    route = "home",
                    from = "simple_profile"
                )
                Log.d("SimpleProfileScreen", "返回首页结果: $success")
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("返回首页")
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 调试信息
        Card(
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(
                modifier = Modifier.padding(12.dp)
            ) {
                Text(
                    text = "调试信息",
                    style = MaterialTheme.typography.titleSmall,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                Text(
                    text = "当前路由: ${navController.currentDestination?.route ?: "unknown"}",
                    style = MaterialTheme.typography.bodySmall
                )
                Text(
                    text = "目的地ID: ${navController.currentDestination?.id ?: "unknown"}",
                    style = MaterialTheme.typography.bodySmall
                )
            }
        }
    }
}
