package com.sdwu.kotlin.data.model;

/**
 * HRV综合数据
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, xi = 48, d1 = {"\u0000T\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\b!\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001Bi\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007\u0012\b\u0010\t\u001a\u0004\u0018\u00010\n\u0012\b\u0010\u000b\u001a\u0004\u0018\u00010\f\u0012\b\u0010\r\u001a\u0004\u0018\u00010\u000e\u0012\u0006\u0010\u000f\u001a\u00020\u0010\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0012\u001a\u00020\u0013\u0012\u0006\u0010\u0014\u001a\u00020\u0015\u00a2\u0006\u0002\u0010\u0016J\t\u0010*\u001a\u00020\u0003H\u00c6\u0003J\t\u0010+\u001a\u00020\u0013H\u00c6\u0003J\t\u0010,\u001a\u00020\u0015H\u00c6\u0003J\t\u0010-\u001a\u00020\u0003H\u00c6\u0003J\t\u0010.\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010/\u001a\b\u0012\u0004\u0012\u00020\b0\u0007H\u00c6\u0003J\u000b\u00100\u001a\u0004\u0018\u00010\nH\u00c6\u0003J\u000b\u00101\u001a\u0004\u0018\u00010\fH\u00c6\u0003J\u000b\u00102\u001a\u0004\u0018\u00010\u000eH\u00c6\u0003J\t\u00103\u001a\u00020\u0010H\u00c6\u0003J\t\u00104\u001a\u00020\u0010H\u00c6\u0003J\u0083\u0001\u00105\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u00072\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\f2\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000e2\b\b\u0002\u0010\u000f\u001a\u00020\u00102\b\b\u0002\u0010\u0011\u001a\u00020\u00102\b\b\u0002\u0010\u0012\u001a\u00020\u00132\b\b\u0002\u0010\u0014\u001a\u00020\u0015H\u00c6\u0001J\u0013\u00106\u001a\u0002072\b\u00108\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u00109\u001a\u00020:H\u00d6\u0001J\t\u0010;\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0014\u001a\u00020\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0013\u0010\u000b\u001a\u0004\u0018\u00010\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001aR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001cR\u0011\u0010\u000f\u001a\u00020\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001eR\u0013\u0010\r\u001a\u0004\u0018\u00010\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010 R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u001cR\u0011\u0010\u0011\u001a\u00020\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u001eR\u0017\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010$R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010\u001cR\u0011\u0010\u0012\u001a\u00020\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\'R\u0013\u0010\t\u001a\u0004\u0018\u00010\n\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010)\u00a8\u0006<"}, d2 = {"Lcom/sdwu/kotlin/data/model/HRVData;", "", "id", "", "patientId", "sessionId", "rrIntervals", "", "Lcom/sdwu/kotlin/data/model/RRInterval;", "timeDomainMetrics", "Lcom/sdwu/kotlin/data/model/HRVTimeDomainMetrics;", "frequencyDomainMetrics", "Lcom/sdwu/kotlin/data/model/HRVFrequencyDomainMetrics;", "nonlinearMetrics", "Lcom/sdwu/kotlin/data/model/HRVNonlinearMetrics;", "measurementDuration", "", "recordedAt", "status", "Lcom/sdwu/kotlin/data/model/HRVMeasurementStatus;", "dataQuality", "", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Lcom/sdwu/kotlin/data/model/HRVTimeDomainMetrics;Lcom/sdwu/kotlin/data/model/HRVFrequencyDomainMetrics;Lcom/sdwu/kotlin/data/model/HRVNonlinearMetrics;JJLcom/sdwu/kotlin/data/model/HRVMeasurementStatus;F)V", "getDataQuality", "()F", "getFrequencyDomainMetrics", "()Lcom/sdwu/kotlin/data/model/HRVFrequencyDomainMetrics;", "getId", "()Ljava/lang/String;", "getMeasurementDuration", "()J", "getNonlinearMetrics", "()Lcom/sdwu/kotlin/data/model/HRVNonlinearMetrics;", "getPatientId", "getRecordedAt", "getRrIntervals", "()Ljava/util/List;", "getSessionId", "getStatus", "()Lcom/sdwu/kotlin/data/model/HRVMeasurementStatus;", "getTimeDomainMetrics", "()Lcom/sdwu/kotlin/data/model/HRVTimeDomainMetrics;", "component1", "component10", "component11", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "", "other", "hashCode", "", "toString", "app_debug"})
public final class HRVData {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String id = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String patientId = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String sessionId = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.sdwu.kotlin.data.model.RRInterval> rrIntervals = null;
    @org.jetbrains.annotations.Nullable()
    private final com.sdwu.kotlin.data.model.HRVTimeDomainMetrics timeDomainMetrics = null;
    @org.jetbrains.annotations.Nullable()
    private final com.sdwu.kotlin.data.model.HRVFrequencyDomainMetrics frequencyDomainMetrics = null;
    @org.jetbrains.annotations.Nullable()
    private final com.sdwu.kotlin.data.model.HRVNonlinearMetrics nonlinearMetrics = null;
    private final long measurementDuration = 0L;
    private final long recordedAt = 0L;
    @org.jetbrains.annotations.NotNull()
    private final com.sdwu.kotlin.data.model.HRVMeasurementStatus status = null;
    private final float dataQuality = 0.0F;
    
    public HRVData(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String patientId, @org.jetbrains.annotations.NotNull()
    java.lang.String sessionId, @org.jetbrains.annotations.NotNull()
    java.util.List<com.sdwu.kotlin.data.model.RRInterval> rrIntervals, @org.jetbrains.annotations.Nullable()
    com.sdwu.kotlin.data.model.HRVTimeDomainMetrics timeDomainMetrics, @org.jetbrains.annotations.Nullable()
    com.sdwu.kotlin.data.model.HRVFrequencyDomainMetrics frequencyDomainMetrics, @org.jetbrains.annotations.Nullable()
    com.sdwu.kotlin.data.model.HRVNonlinearMetrics nonlinearMetrics, long measurementDuration, long recordedAt, @org.jetbrains.annotations.NotNull()
    com.sdwu.kotlin.data.model.HRVMeasurementStatus status, float dataQuality) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getPatientId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getSessionId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.sdwu.kotlin.data.model.RRInterval> getRrIntervals() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.sdwu.kotlin.data.model.HRVTimeDomainMetrics getTimeDomainMetrics() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.sdwu.kotlin.data.model.HRVFrequencyDomainMetrics getFrequencyDomainMetrics() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.sdwu.kotlin.data.model.HRVNonlinearMetrics getNonlinearMetrics() {
        return null;
    }
    
    public final long getMeasurementDuration() {
        return 0L;
    }
    
    public final long getRecordedAt() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.sdwu.kotlin.data.model.HRVMeasurementStatus getStatus() {
        return null;
    }
    
    public final float getDataQuality() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.sdwu.kotlin.data.model.HRVMeasurementStatus component10() {
        return null;
    }
    
    public final float component11() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.sdwu.kotlin.data.model.RRInterval> component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.sdwu.kotlin.data.model.HRVTimeDomainMetrics component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.sdwu.kotlin.data.model.HRVFrequencyDomainMetrics component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.sdwu.kotlin.data.model.HRVNonlinearMetrics component7() {
        return null;
    }
    
    public final long component8() {
        return 0L;
    }
    
    public final long component9() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.sdwu.kotlin.data.model.HRVData copy(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String patientId, @org.jetbrains.annotations.NotNull()
    java.lang.String sessionId, @org.jetbrains.annotations.NotNull()
    java.util.List<com.sdwu.kotlin.data.model.RRInterval> rrIntervals, @org.jetbrains.annotations.Nullable()
    com.sdwu.kotlin.data.model.HRVTimeDomainMetrics timeDomainMetrics, @org.jetbrains.annotations.Nullable()
    com.sdwu.kotlin.data.model.HRVFrequencyDomainMetrics frequencyDomainMetrics, @org.jetbrains.annotations.Nullable()
    com.sdwu.kotlin.data.model.HRVNonlinearMetrics nonlinearMetrics, long measurementDuration, long recordedAt, @org.jetbrains.annotations.NotNull()
    com.sdwu.kotlin.data.model.HRVMeasurementStatus status, float dataQuality) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}