package com.sdwu.kotlin.utils

import android.content.Context
import android.util.Log
import java.io.File
import java.io.FileWriter
import java.io.PrintWriter
import java.text.SimpleDateFormat
import java.util.*

/**
 * 崩溃处理器
 * 捕获并记录应用程序的未处理异常
 */
class CrashHandler private constructor() : Thread.UncaughtExceptionHandler {
    
    companion object {
        private const val TAG = "CrashHandler"
        private const val CRASH_LOG_DIR = "crash_logs"
        
        @Volatile
        private var instance: CrashHandler? = null
        
        fun getInstance(): CrashHandler {
            return instance ?: synchronized(this) {
                instance ?: CrashHandler().also { instance = it }
            }
        }
    }
    
    private var context: Context? = null
    private var defaultHandler: Thread.UncaughtExceptionHandler? = null
    
    /**
     * 初始化崩溃处理器
     */
    fun init(context: Context) {
        this.context = context.applicationContext
        defaultHandler = Thread.getDefaultUncaughtExceptionHandler()
        Thread.setDefaultUncaughtExceptionHandler(this)
        Log.d(TAG, "崩溃处理器初始化完成")
    }
    
    override fun uncaughtException(thread: Thread, ex: Throwable) {
        Log.e(TAG, "捕获到未处理异常", ex)
        
        try {
            // 记录崩溃信息到日志
            logCrashToFile(ex)
            
            // 记录到Android日志
            logCrashToLogcat(ex)
            
        } catch (e: Exception) {
            Log.e(TAG, "记录崩溃信息失败", e)
        }
        
        // 调用默认的异常处理器
        defaultHandler?.uncaughtException(thread, ex)
    }
    
    /**
     * 将崩溃信息记录到文件
     */
    private fun logCrashToFile(ex: Throwable) {
        try {
            val context = this.context ?: return
            
            // 创建崩溃日志目录
            val crashDir = File(context.filesDir, CRASH_LOG_DIR)
            if (!crashDir.exists()) {
                crashDir.mkdirs()
            }
            
            // 生成日志文件名
            val dateFormat = SimpleDateFormat("yyyy-MM-dd_HH-mm-ss", Locale.getDefault())
            val fileName = "crash_${dateFormat.format(Date())}.log"
            val logFile = File(crashDir, fileName)
            
            // 写入崩溃信息
            FileWriter(logFile, true).use { fileWriter ->
                PrintWriter(fileWriter).use { printWriter ->
                    printWriter.println("=== 崩溃报告 ===")
                    printWriter.println("时间: ${Date()}")
                    printWriter.println("线程: ${Thread.currentThread().name}")
                    printWriter.println()
                    
                    // 设备信息
                    printWriter.println("=== 设备信息 ===")
                    printWriter.println("Android版本: ${android.os.Build.VERSION.RELEASE}")
                    printWriter.println("API级别: ${android.os.Build.VERSION.SDK_INT}")
                    printWriter.println("设备型号: ${android.os.Build.MODEL}")
                    printWriter.println("设备制造商: ${android.os.Build.MANUFACTURER}")
                    printWriter.println()
                    
                    // 应用信息
                    printWriter.println("=== 应用信息 ===")
                    val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
                    printWriter.println("应用版本: ${packageInfo.versionName}")
                    printWriter.println("版本代码: ${packageInfo.versionCode}")
                    printWriter.println("包名: ${context.packageName}")
                    printWriter.println()
                    
                    // 内存信息
                    printWriter.println("=== 内存信息 ===")
                    val runtime = Runtime.getRuntime()
                    printWriter.println("最大内存: ${runtime.maxMemory() / 1024 / 1024}MB")
                    printWriter.println("总内存: ${runtime.totalMemory() / 1024 / 1024}MB")
                    printWriter.println("空闲内存: ${runtime.freeMemory() / 1024 / 1024}MB")
                    printWriter.println()
                    
                    // 异常信息
                    printWriter.println("=== 异常信息 ===")
                    ex.printStackTrace(printWriter)
                    printWriter.println()
                    printWriter.println("=== 报告结束 ===")
                }
            }
            
            Log.d(TAG, "崩溃信息已保存到: ${logFile.absolutePath}")
            
        } catch (e: Exception) {
            Log.e(TAG, "保存崩溃信息到文件失败", e)
        }
    }
    
    /**
     * 将崩溃信息记录到Logcat
     */
    private fun logCrashToLogcat(ex: Throwable) {
        Log.e(TAG, "=== 应用崩溃 ===")
        Log.e(TAG, "异常类型: ${ex.javaClass.simpleName}")
        Log.e(TAG, "异常消息: ${ex.message}")
        Log.e(TAG, "线程名称: ${Thread.currentThread().name}")
        
        // 记录堆栈跟踪
        val stackTrace = ex.stackTrace
        Log.e(TAG, "堆栈跟踪:")
        for (element in stackTrace) {
            Log.e(TAG, "  at $element")
        }
        
        // 记录原因链
        var cause = ex.cause
        var level = 1
        while (cause != null && level <= 5) { // 限制层级避免无限循环
            Log.e(TAG, "原因 $level: ${cause.javaClass.simpleName}: ${cause.message}")
            cause = cause.cause
            level++
        }
        
        Log.e(TAG, "=== 崩溃信息结束 ===")
    }
    
    /**
     * 获取崩溃日志文件列表
     */
    fun getCrashLogFiles(): List<File> {
        val context = this.context ?: return emptyList()
        val crashDir = File(context.filesDir, CRASH_LOG_DIR)
        
        return if (crashDir.exists() && crashDir.isDirectory) {
            crashDir.listFiles()?.toList() ?: emptyList()
        } else {
            emptyList()
        }
    }
    
    /**
     * 清理旧的崩溃日志文件
     */
    fun cleanOldCrashLogs(maxFiles: Int = 10) {
        try {
            val logFiles = getCrashLogFiles()
            if (logFiles.size > maxFiles) {
                // 按修改时间排序，删除最旧的文件
                val sortedFiles = logFiles.sortedBy { it.lastModified() }
                val filesToDelete = sortedFiles.take(logFiles.size - maxFiles)
                
                for (file in filesToDelete) {
                    if (file.delete()) {
                        Log.d(TAG, "删除旧崩溃日志: ${file.name}")
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "清理旧崩溃日志失败", e)
        }
    }
}
