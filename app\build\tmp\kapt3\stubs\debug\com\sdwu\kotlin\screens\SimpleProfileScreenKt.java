package com.sdwu.kotlin.screens;

@kotlin.Metadata(mv = {1, 8, 0}, k = 2, xi = 48, d1 = {"\u0000\u000e\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a\u0010\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\u0007\u00a8\u0006\u0004"}, d2 = {"SimpleProfileScreen", "", "navController", "Landroidx/navigation/NavController;", "app_debug"})
public final class SimpleProfileScreenKt {
    
    /**
     * 简化的个人资料页面
     * 用于测试导航是否正常工作
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void SimpleProfileScreen(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController) {
    }
}