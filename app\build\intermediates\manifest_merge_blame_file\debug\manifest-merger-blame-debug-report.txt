1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.sdwu.kotlin"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="29"
8-->D:\kotlin\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="33" />
9-->D:\kotlin\app\src\main\AndroidManifest.xml
10
11    <permission
11-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d5d80ec9ea4b92e6a394c21abf0511b\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
12        android:name="com.sdwu.kotlin.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d5d80ec9ea4b92e6a394c21abf0511b\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d5d80ec9ea4b92e6a394c21abf0511b\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.sdwu.kotlin.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d5d80ec9ea4b92e6a394c21abf0511b\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d5d80ec9ea4b92e6a394c21abf0511b\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
16
17    <application
17-->D:\kotlin\app\src\main\AndroidManifest.xml:5:5-48:19
18        android:name="com.sdwu.kotlin.KotlinApplication"
18-->D:\kotlin\app\src\main\AndroidManifest.xml:6:9-42
19        android:allowBackup="true"
19-->D:\kotlin\app\src\main\AndroidManifest.xml:7:9-35
20        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
20-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d5d80ec9ea4b92e6a394c21abf0511b\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
21        android:dataExtractionRules="@xml/data_extraction_rules"
21-->D:\kotlin\app\src\main\AndroidManifest.xml:8:9-65
22        android:debuggable="true"
23        android:extractNativeLibs="false"
24        android:fullBackupContent="@xml/backup_rules"
24-->D:\kotlin\app\src\main\AndroidManifest.xml:9:9-54
25        android:icon="@mipmap/ic_launcher"
25-->D:\kotlin\app\src\main\AndroidManifest.xml:10:9-43
26        android:label="@string/app_name"
26-->D:\kotlin\app\src\main\AndroidManifest.xml:11:9-41
27        android:roundIcon="@mipmap/ic_launcher_round"
27-->D:\kotlin\app\src\main\AndroidManifest.xml:12:9-54
28        android:supportsRtl="true"
28-->D:\kotlin\app\src\main\AndroidManifest.xml:13:9-35
29        android:testOnly="true"
30        android:theme="@style/Theme.Kotlin" >
30-->D:\kotlin\app\src\main\AndroidManifest.xml:14:9-44
31        <activity
31-->D:\kotlin\app\src\main\AndroidManifest.xml:16:9-26:20
32            android:name="com.sdwu.kotlin.MainActivity"
32-->D:\kotlin\app\src\main\AndroidManifest.xml:17:13-41
33            android:exported="true"
33-->D:\kotlin\app\src\main\AndroidManifest.xml:18:13-36
34            android:label="@string/app_name"
34-->D:\kotlin\app\src\main\AndroidManifest.xml:19:13-45
35            android:theme="@style/Theme.Kotlin" >
35-->D:\kotlin\app\src\main\AndroidManifest.xml:20:13-48
36            <intent-filter>
36-->D:\kotlin\app\src\main\AndroidManifest.xml:21:13-25:29
37                <action android:name="android.intent.action.MAIN" />
37-->D:\kotlin\app\src\main\AndroidManifest.xml:22:17-69
37-->D:\kotlin\app\src\main\AndroidManifest.xml:22:25-66
38
39                <category android:name="android.intent.category.LAUNCHER" />
39-->D:\kotlin\app\src\main\AndroidManifest.xml:24:17-77
39-->D:\kotlin\app\src\main\AndroidManifest.xml:24:27-74
40            </intent-filter>
41        </activity>
42
43        <!-- 传统View系统示例Activity -->
44        <activity
44-->D:\kotlin\app\src\main\AndroidManifest.xml:29:9-33:51
45            android:name="com.sdwu.kotlin.TraditionalViewActivity"
45-->D:\kotlin\app\src\main\AndroidManifest.xml:30:13-52
46            android:exported="false"
46-->D:\kotlin\app\src\main\AndroidManifest.xml:31:13-37
47            android:label="传统View系统示例"
47-->D:\kotlin\app\src\main\AndroidManifest.xml:32:13-39
48            android:theme="@style/Theme.Kotlin" />
48-->D:\kotlin\app\src\main\AndroidManifest.xml:33:13-48
49
50        <!-- ViewBinding示例Activity -->
51        <activity
51-->D:\kotlin\app\src\main\AndroidManifest.xml:36:9-40:51
52            android:name="com.sdwu.kotlin.ViewBindingActivity"
52-->D:\kotlin\app\src\main\AndroidManifest.xml:37:13-48
53            android:exported="false"
53-->D:\kotlin\app\src\main\AndroidManifest.xml:38:13-37
54            android:label="ViewBinding示例"
54-->D:\kotlin\app\src\main\AndroidManifest.xml:39:13-42
55            android:theme="@style/Theme.Kotlin" />
55-->D:\kotlin\app\src\main\AndroidManifest.xml:40:13-48
56
57        <!-- DataBinding示例Activity -->
58        <activity
58-->D:\kotlin\app\src\main\AndroidManifest.xml:43:9-47:51
59            android:name="com.sdwu.kotlin.DataBindingActivity"
59-->D:\kotlin\app\src\main\AndroidManifest.xml:44:13-48
60            android:exported="false"
60-->D:\kotlin\app\src\main\AndroidManifest.xml:45:13-37
61            android:label="DataBinding示例"
61-->D:\kotlin\app\src\main\AndroidManifest.xml:46:13-42
62            android:theme="@style/Theme.Kotlin" />
62-->D:\kotlin\app\src\main\AndroidManifest.xml:47:13-48
63        <activity
63-->[androidx.compose.ui:ui-test-manifest:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\2612807d36fe3e67805289f3d7106e95\transformed\ui-test-manifest-1.4.2\AndroidManifest.xml:23:9-25:39
64            android:name="androidx.activity.ComponentActivity"
64-->[androidx.compose.ui:ui-test-manifest:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\2612807d36fe3e67805289f3d7106e95\transformed\ui-test-manifest-1.4.2\AndroidManifest.xml:24:13-63
65            android:exported="true" />
65-->[androidx.compose.ui:ui-test-manifest:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\2612807d36fe3e67805289f3d7106e95\transformed\ui-test-manifest-1.4.2\AndroidManifest.xml:25:13-36
66
67        <provider
67-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e6ecd4995b1c36752fd1d689defa1b6\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
68            android:name="androidx.startup.InitializationProvider"
68-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e6ecd4995b1c36752fd1d689defa1b6\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
69            android:authorities="com.sdwu.kotlin.androidx-startup"
69-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e6ecd4995b1c36752fd1d689defa1b6\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
70            android:exported="false" >
70-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e6ecd4995b1c36752fd1d689defa1b6\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
71            <meta-data
71-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e6ecd4995b1c36752fd1d689defa1b6\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
72                android:name="androidx.emoji2.text.EmojiCompatInitializer"
72-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e6ecd4995b1c36752fd1d689defa1b6\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
73                android:value="androidx.startup" />
73-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e6ecd4995b1c36752fd1d689defa1b6\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
74            <meta-data
74-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8cd6f891d9b7224515830d4f287d3799\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
75                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
75-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8cd6f891d9b7224515830d4f287d3799\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
76                android:value="androidx.startup" />
76-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8cd6f891d9b7224515830d4f287d3799\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
77            <meta-data
77-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
78                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
79                android:value="androidx.startup" />
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
80        </provider>
81
82        <activity
82-->[androidx.compose.ui:ui-tooling:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\989e0b2ad691e9eadb8e98e96dfffb07\transformed\ui-tooling-1.4.2\AndroidManifest.xml:23:9-25:39
83            android:name="androidx.compose.ui.tooling.PreviewActivity"
83-->[androidx.compose.ui:ui-tooling:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\989e0b2ad691e9eadb8e98e96dfffb07\transformed\ui-tooling-1.4.2\AndroidManifest.xml:24:13-71
84            android:exported="true" />
84-->[androidx.compose.ui:ui-tooling:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\989e0b2ad691e9eadb8e98e96dfffb07\transformed\ui-tooling-1.4.2\AndroidManifest.xml:25:13-36
85
86        <receiver
86-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
87            android:name="androidx.profileinstaller.ProfileInstallReceiver"
87-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
88            android:directBootAware="false"
88-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
89            android:enabled="true"
89-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
90            android:exported="true"
90-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
91            android:permission="android.permission.DUMP" >
91-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
92            <intent-filter>
92-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
93                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
93-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
93-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
94            </intent-filter>
95            <intent-filter>
95-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
96                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
96-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
96-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
97            </intent-filter>
98            <intent-filter>
98-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
99                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
99-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
99-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
100            </intent-filter>
101            <intent-filter>
101-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
102                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
102-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
102-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
103            </intent-filter>
104        </receiver>
105    </application>
106
107</manifest>
