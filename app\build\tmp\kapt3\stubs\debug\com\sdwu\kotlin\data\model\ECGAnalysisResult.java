package com.sdwu.kotlin.data.model;

/**
 * 心电图分析结果
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, xi = 48, d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0014\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B;\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\t\u0012\u0006\u0010\u000b\u001a\u00020\u0005\u0012\u0006\u0010\f\u001a\u00020\r\u00a2\u0006\u0002\u0010\u000eJ\t\u0010\u001a\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u0007H\u00c6\u0003J\u000f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\n0\tH\u00c6\u0003J\t\u0010\u001e\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001f\u001a\u00020\rH\u00c6\u0003JK\u0010 \u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\u000e\b\u0002\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\t2\b\b\u0002\u0010\u000b\u001a\u00020\u00052\b\b\u0002\u0010\f\u001a\u00020\rH\u00c6\u0001J\u0013\u0010!\u001a\u00020\"2\b\u0010#\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010$\u001a\u00020%H\u00d6\u0001J\t\u0010&\u001a\u00020\u0003H\u00d6\u0001R\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\u000b\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0014R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019\u00a8\u0006\'"}, d2 = {"Lcom/sdwu/kotlin/data/model/ECGAnalysisResult;", "", "waveformId", "", "heartRateVariability", "", "rhythmType", "Lcom/sdwu/kotlin/data/model/ECGRhythmType;", "abnormalities", "", "Lcom/sdwu/kotlin/data/model/ECGAbnormality;", "confidence", "analyzedAt", "", "(Ljava/lang/String;FLcom/sdwu/kotlin/data/model/ECGRhythmType;Ljava/util/List;FJ)V", "getAbnormalities", "()Ljava/util/List;", "getAnalyzedAt", "()J", "getConfidence", "()F", "getHeartRateVariability", "getRhythmType", "()Lcom/sdwu/kotlin/data/model/ECGRhythmType;", "getWaveformId", "()Ljava/lang/String;", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "", "other", "hashCode", "", "toString", "app_debug"})
public final class ECGAnalysisResult {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String waveformId = null;
    private final float heartRateVariability = 0.0F;
    @org.jetbrains.annotations.NotNull()
    private final com.sdwu.kotlin.data.model.ECGRhythmType rhythmType = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.sdwu.kotlin.data.model.ECGAbnormality> abnormalities = null;
    private final float confidence = 0.0F;
    private final long analyzedAt = 0L;
    
    public ECGAnalysisResult(@org.jetbrains.annotations.NotNull()
    java.lang.String waveformId, float heartRateVariability, @org.jetbrains.annotations.NotNull()
    com.sdwu.kotlin.data.model.ECGRhythmType rhythmType, @org.jetbrains.annotations.NotNull()
    java.util.List<com.sdwu.kotlin.data.model.ECGAbnormality> abnormalities, float confidence, long analyzedAt) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getWaveformId() {
        return null;
    }
    
    public final float getHeartRateVariability() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.sdwu.kotlin.data.model.ECGRhythmType getRhythmType() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.sdwu.kotlin.data.model.ECGAbnormality> getAbnormalities() {
        return null;
    }
    
    public final float getConfidence() {
        return 0.0F;
    }
    
    public final long getAnalyzedAt() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    public final float component2() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.sdwu.kotlin.data.model.ECGRhythmType component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.sdwu.kotlin.data.model.ECGAbnormality> component4() {
        return null;
    }
    
    public final float component5() {
        return 0.0F;
    }
    
    public final long component6() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.sdwu.kotlin.data.model.ECGAnalysisResult copy(@org.jetbrains.annotations.NotNull()
    java.lang.String waveformId, float heartRateVariability, @org.jetbrains.annotations.NotNull()
    com.sdwu.kotlin.data.model.ECGRhythmType rhythmType, @org.jetbrains.annotations.NotNull()
    java.util.List<com.sdwu.kotlin.data.model.ECGAbnormality> abnormalities, float confidence, long analyzedAt) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}