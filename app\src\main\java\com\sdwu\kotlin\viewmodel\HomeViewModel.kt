package com.sdwu.kotlin.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.sdwu.kotlin.data.model.HomeItem
import com.sdwu.kotlin.data.repository.HomeRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
/**
 * 首页ViewModel
 * 管理首页的业务逻辑和UI状态
 */
class HomeViewModel(
    private val homeRepository: HomeRepository
) : ViewModel() {
    
    // UI状态
    private val _uiState = MutableStateFlow(HomeUiState())
    val uiState: StateFlow<HomeUiState> = _uiState.asStateFlow()
    
    // 搜索查询
    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<String> = _searchQuery.asStateFlow()
    
    init {
        loadHomeItems()
    }
    
    /**
     * 加载首页数据
     */
    fun loadHomeItems() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            try {
                homeRepository.getHomeItems().collect { items ->
                    _uiState.value = _uiState.value.copy(
                        items = items,
                        isLoading = false,
                        error = null
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "加载数据失败"
                )
            }
        }
    }
    
    /**
     * 刷新数据
     */
    fun refreshData() {
        loadHomeItems()
    }
    
    /**
     * 搜索项目
     */
    fun searchItems(query: String) {
        _searchQuery.value = query
        
        if (query.isBlank()) {
            loadHomeItems()
            return
        }
        
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            try {
                homeRepository.searchItems(query).collect { items ->
                    _uiState.value = _uiState.value.copy(
                        items = items,
                        isLoading = false,
                        error = null
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "搜索失败"
                )
            }
        }
    }
    
    /**
     * 清除搜索
     */
    fun clearSearch() {
        _searchQuery.value = ""
        loadHomeItems()
    }
    
    /**
     * 添加新项目
     */
    fun addItem(title: String, description: String) {
        viewModelScope.launch {
            try {
                homeRepository.addItem(title, description)
                loadHomeItems() // 重新加载数据
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = e.message ?: "添加项目失败"
                )
            }
        }
    }
    
    /**
     * 删除项目
     */
    fun deleteItem(itemId: String) {
        viewModelScope.launch {
            try {
                homeRepository.deleteItem(itemId)
                loadHomeItems() // 重新加载数据
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = e.message ?: "删除项目失败"
                )
            }
        }
    }
    
    /**
     * 清除错误状态
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}

/**
 * 首页UI状态数据类
 */
data class HomeUiState(
    val items: List<HomeItem> = emptyList(),
    val isLoading: Boolean = false,
    val error: String? = null
)
