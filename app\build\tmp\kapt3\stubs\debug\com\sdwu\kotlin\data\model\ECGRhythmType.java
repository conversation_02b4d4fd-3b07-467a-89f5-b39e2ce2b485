package com.sdwu.kotlin.data.model;

/**
 * 心电图节律类型
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u000b\b\u0086\u0001\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000b\u00a8\u0006\f"}, d2 = {"Lcom/sdwu/kotlin/data/model/ECGRhythmType;", "", "(Ljava/lang/String;I)V", "NORMAL_SINUS", "SINUS_BRADYCARDIA", "SINUS_TACHYCARDIA", "ATRIAL_FIBRILLATION", "ATRIAL_FLUTTER", "VENTRICULAR_TACHYCARDIA", "VENTRICULAR_FIBRILLATION", "ASYSTOLE", "UNKNOWN", "app_debug"})
public enum ECGRhythmType {
    /*public static final*/ NORMAL_SINUS /* = new NORMAL_SINUS() */,
    /*public static final*/ SINUS_BRADYCARDIA /* = new SINUS_BRADYCARDIA() */,
    /*public static final*/ SINUS_TACHYCARDIA /* = new SINUS_TACHYCARDIA() */,
    /*public static final*/ ATRIAL_FIBRILLATION /* = new ATRIAL_FIBRILLATION() */,
    /*public static final*/ ATRIAL_FLUTTER /* = new ATRIAL_FLUTTER() */,
    /*public static final*/ VENTRICULAR_TACHYCARDIA /* = new VENTRICULAR_TACHYCARDIA() */,
    /*public static final*/ VENTRICULAR_FIBRILLATION /* = new VENTRICULAR_FIBRILLATION() */,
    /*public static final*/ ASYSTOLE /* = new ASYSTOLE() */,
    /*public static final*/ UNKNOWN /* = new UNKNOWN() */;
    
    ECGRhythmType() {
    }
}