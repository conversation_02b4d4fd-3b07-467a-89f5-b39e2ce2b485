package com.sdwu.kotlin.ui.theme;

@kotlin.Metadata(mv = {1, 8, 0}, k = 2, xi = 48, d1 = {"\u0000\n\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u001c\"\u0016\u0010\u0000\u001a\u00020\u0001\u00f8\u0001\u0000\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0002\u0010\u0003\"\u0016\u0010\u0005\u001a\u00020\u0001\u00f8\u0001\u0000\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0006\u0010\u0003\"\u0016\u0010\u0007\u001a\u00020\u0001\u00f8\u0001\u0000\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\b\u0010\u0003\"\u0016\u0010\t\u001a\u00020\u0001\u00f8\u0001\u0000\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\n\u0010\u0003\"\u0016\u0010\u000b\u001a\u00020\u0001\u00f8\u0001\u0000\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\f\u0010\u0003\"\u0016\u0010\r\u001a\u00020\u0001\u00f8\u0001\u0000\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u000e\u0010\u0003\"\u0016\u0010\u000f\u001a\u00020\u0001\u00f8\u0001\u0000\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0010\u0010\u0003\"\u0016\u0010\u0011\u001a\u00020\u0001\u00f8\u0001\u0000\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0012\u0010\u0003\"\u0016\u0010\u0013\u001a\u00020\u0001\u00f8\u0001\u0000\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0014\u0010\u0003\"\u0016\u0010\u0015\u001a\u00020\u0001\u00f8\u0001\u0000\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0016\u0010\u0003\"\u0016\u0010\u0017\u001a\u00020\u0001\u00f8\u0001\u0000\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0018\u0010\u0003\"\u0016\u0010\u0019\u001a\u00020\u0001\u00f8\u0001\u0000\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u001a\u0010\u0003\"\u0016\u0010\u001b\u001a\u00020\u0001\u00f8\u0001\u0000\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u001c\u0010\u0003\u0082\u0002\u0004\n\u0002\b\u0019\u00a8\u0006\u001d"}, d2 = {"HRVAccent", "Landroidx/compose/ui/graphics/Color;", "getHRVAccent", "()J", "J", "HRVBackground", "getHRVBackground", "HRVPrimary", "getHRVPrimary", "HRVSecondary", "getHRVSecondary", "HRVSurface", "getHRVSurface", "Pink40", "getPink40", "Pink80", "getPink80", "PinkGradientEnd", "getPinkGradientEnd", "PinkGradientStart", "getPinkGradientStart", "PinkGrey40", "getPinkGrey40", "PinkGrey80", "getPinkGrey80", "Rose40", "getRose40", "Rose80", "getRose80", "app_debug"})
public final class ColorKt {
    private static final long Pink80 = 0L;
    private static final long PinkGrey80 = 0L;
    private static final long Rose80 = 0L;
    private static final long Pink40 = 0L;
    private static final long PinkGrey40 = 0L;
    private static final long Rose40 = 0L;
    private static final long HRVPrimary = 0L;
    private static final long HRVSecondary = 0L;
    private static final long HRVAccent = 0L;
    private static final long HRVBackground = 0L;
    private static final long HRVSurface = 0L;
    private static final long PinkGradientStart = 0L;
    private static final long PinkGradientEnd = 0L;
    
    public static final long getPink80() {
        return 0L;
    }
    
    public static final long getPinkGrey80() {
        return 0L;
    }
    
    public static final long getRose80() {
        return 0L;
    }
    
    public static final long getPink40() {
        return 0L;
    }
    
    public static final long getPinkGrey40() {
        return 0L;
    }
    
    public static final long getRose40() {
        return 0L;
    }
    
    public static final long getHRVPrimary() {
        return 0L;
    }
    
    public static final long getHRVSecondary() {
        return 0L;
    }
    
    public static final long getHRVAccent() {
        return 0L;
    }
    
    public static final long getHRVBackground() {
        return 0L;
    }
    
    public static final long getHRVSurface() {
        return 0L;
    }
    
    public static final long getPinkGradientStart() {
        return 0L;
    }
    
    public static final long getPinkGradientEnd() {
        return 0L;
    }
}