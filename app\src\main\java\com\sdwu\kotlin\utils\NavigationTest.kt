package com.sdwu.kotlin.utils

import android.content.Context
import android.util.Log
import androidx.navigation.NavController
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 导航测试工具
 * 用于测试导航功能和错误处理
 */
object NavigationTest {
    
    private const val TAG = "NavigationTest"
    
    /**
     * 运行所有导航测试
     */
    fun runAllNavigationTests(context: Context, navController: NavController) {
        ErrorLogger.logInfo(TAG, "=== 开始导航测试 ===")
        
        CoroutineScope(Dispatchers.Main).launch {
            try {
                // 1. 测试基本导航功能
                testBasicNavigation(navController)
                
                // 2. 测试错误处理
                testNavigationErrorHandling(navController)
                
                // 3. 测试路由验证
                testRouteValidation(navController)
                
                // 4. 测试返回栈操作
                testBackStackOperations(navController)
                
                ErrorLogger.logInfo(TAG, "=== 导航测试完成 ===")
                
            } catch (e: Exception) {
                ErrorLogger.logError(TAG, "导航测试失败", e)
            }
        }
    }
    
    /**
     * 测试基本导航功能
     */
    private fun testBasicNavigation(navController: NavController) {
        ErrorLogger.logInfo(TAG, "--- 测试基本导航功能 ---")
        
        try {
            // 获取当前导航状态
            val currentState = NavigationErrorHandler.getCurrentNavigationState(navController)
            ErrorLogger.logDebug(TAG, "当前导航状态: $currentState")
            
            // 测试NavController是否有效
            val graph = navController.graph
            ErrorLogger.logDebug(TAG, "导航图起始目的地: ${graph.startDestinationRoute}")
            
            val currentDestination = navController.currentDestination
            ErrorLogger.logDebug(TAG, "当前目的地: ${currentDestination?.route}")
            
            ErrorLogger.logInfo(TAG, "✓ 基本导航功能测试通过")
            
        } catch (e: Exception) {
            ErrorLogger.logError(TAG, "基本导航功能测试失败", e)
        }
    }
    
    /**
     * 测试导航错误处理
     */
    private fun testNavigationErrorHandling(navController: NavController) {
        ErrorLogger.logInfo(TAG, "--- 测试导航错误处理 ---")
        
        try {
            // 测试导航到无效路由
            val invalidRoutes = listOf(
                "invalid_route",
                "nonexistent_page",
                "detail/", // 缺少参数
                "", // 空路由
                "profile/extra/params" // 多余参数
            )
            
            for (route in invalidRoutes) {
                ErrorLogger.logDebug(TAG, "测试无效路由: $route")
                val success = NavigationErrorHandler.safeNavigateTo(
                    navController = navController,
                    route = route,
                    from = "test"
                )
                if (!success) {
                    ErrorLogger.logDebug(TAG, "✓ 正确处理无效路由: $route")
                } else {
                    ErrorLogger.logWarning(TAG, "⚠ 无效路由未被拦截: $route")
                }
            }
            
            ErrorLogger.logInfo(TAG, "✓ 导航错误处理测试完成")
            
        } catch (e: Exception) {
            ErrorLogger.logError(TAG, "导航错误处理测试失败", e)
        }
    }
    
    /**
     * 测试路由验证
     */
    private fun testRouteValidation(navController: NavController) {
        ErrorLogger.logInfo(TAG, "--- 测试路由验证 ---")
        
        try {
            // 测试有效路由
            val validRoutes = listOf(
                "home",
                "profile",
                "settings",
                "detail/123"
            )
            
            for (route in validRoutes) {
                ErrorLogger.logDebug(TAG, "验证有效路由: $route")
                // 注意：这里只是验证路由格式，不实际导航
                val isValid = isRouteFormatValid(route)
                if (isValid) {
                    ErrorLogger.logDebug(TAG, "✓ 路由格式有效: $route")
                } else {
                    ErrorLogger.logWarning(TAG, "⚠ 路由格式无效: $route")
                }
            }
            
            ErrorLogger.logInfo(TAG, "✓ 路由验证测试完成")
            
        } catch (e: Exception) {
            ErrorLogger.logError(TAG, "路由验证测试失败", e)
        }
    }
    
    /**
     * 测试返回栈操作
     */
    private fun testBackStackOperations(navController: NavController) {
        ErrorLogger.logInfo(TAG, "--- 测试返回栈操作 ---")
        
        try {
            // 测试安全返回
            ErrorLogger.logDebug(TAG, "测试安全返回操作")
            val canGoBack = NavigationErrorHandler.safePopBackStack(navController, "test")
            ErrorLogger.logDebug(TAG, "返回操作结果: $canGoBack")
            
            ErrorLogger.logInfo(TAG, "✓ 返回栈操作测试完成")
            
        } catch (e: Exception) {
            ErrorLogger.logError(TAG, "返回栈操作测试失败", e)
        }
    }
    
    /**
     * 检查路由格式是否有效
     */
    private fun isRouteFormatValid(route: String): Boolean {
        return when {
            route.isBlank() -> false
            route == "home" -> true
            route == "profile" -> true
            route == "settings" -> true
            route.startsWith("detail/") && route.length > 7 -> true
            else -> false
        }
    }
    
    /**
     * 模拟导航错误场景
     */
    fun simulateNavigationErrors(navController: NavController) {
        ErrorLogger.logInfo(TAG, "=== 模拟导航错误场景 ===")
        
        CoroutineScope(Dispatchers.Main).launch {
            try {
                // 场景1: 快速连续导航
                ErrorLogger.logDebug(TAG, "场景1: 快速连续导航")
                repeat(5) { i ->
                    NavigationErrorHandler.safeNavigateTo(
                        navController = navController,
                        route = "profile",
                        from = "test_$i"
                    )
                }
                
                // 场景2: 导航到不存在的路由
                ErrorLogger.logDebug(TAG, "场景2: 导航到不存在的路由")
                NavigationErrorHandler.safeNavigateTo(
                    navController = navController,
                    route = "nonexistent_route_12345",
                    from = "test"
                )
                
                // 场景3: 参数错误的路由
                ErrorLogger.logDebug(TAG, "场景3: 参数错误的路由")
                NavigationErrorHandler.safeNavigateTo(
                    navController = navController,
                    route = "detail/",
                    from = "test"
                )
                
                ErrorLogger.logInfo(TAG, "=== 导航错误场景模拟完成 ===")
                
            } catch (e: Exception) {
                ErrorLogger.logError(TAG, "导航错误场景模拟失败", e)
            }
        }
    }
    
    /**
     * 生成导航诊断报告
     */
    fun generateNavigationDiagnosticReport(navController: NavController): String {
        val report = StringBuilder()
        
        try {
            report.appendLine("=== 导航诊断报告 ===")
            report.appendLine("生成时间: ${System.currentTimeMillis()}")
            report.appendLine()
            
            // 当前导航状态
            report.appendLine("当前导航状态:")
            report.appendLine(NavigationErrorHandler.getCurrentNavigationState(navController))
            report.appendLine()
            
            // 导航图信息
            val graph = navController.graph
            report.appendLine("导航图信息:")
            report.appendLine("  起始目的地: ${graph.startDestinationRoute}")
            report.appendLine("  导航图ID: ${graph.id}")
            report.appendLine()
            
            // 当前目的地信息
            val currentDestination = navController.currentDestination
            if (currentDestination != null) {
                report.appendLine("当前目的地信息:")
                report.appendLine("  路由: ${currentDestination.route}")
                report.appendLine("  ID: ${currentDestination.id}")
                report.appendLine("  标签: ${currentDestination.label}")
            } else {
                report.appendLine("当前目的地: null")
            }
            
            report.appendLine("=== 导航诊断报告结束 ===")
            
        } catch (e: Exception) {
            report.appendLine("生成导航诊断报告时发生错误: ${e.message}")
            ErrorLogger.logError(TAG, "生成导航诊断报告失败", e)
        }
        
        val reportString = report.toString()
        ErrorLogger.logInfo(TAG, reportString)
        return reportString
    }
}
