package com.sdwu.kotlin.viewmodel;

/**
 * ECG UI状态
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, xi = 48, d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0017\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001BY\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b\u0012\u000e\b\u0002\u0010\t\u001a\b\u0012\u0004\u0012\u00020\b0\n\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\f\u0012\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000e\u00a2\u0006\u0002\u0010\u000fJ\t\u0010\u001b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\u001d\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u000b\u0010\u001e\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\u000f\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\b0\nH\u00c6\u0003J\u000b\u0010 \u001a\u0004\u0018\u00010\fH\u00c6\u0003J\u000b\u0010!\u001a\u0004\u0018\u00010\u000eH\u00c6\u0003J]\u0010\"\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b2\u000e\b\u0002\u0010\t\u001a\b\u0012\u0004\u0012\u00020\b0\n2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\f2\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000eH\u00c6\u0001J\u0013\u0010#\u001a\u00020\u00032\b\u0010$\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010%\u001a\u00020&H\u00d6\u0001J\t\u0010\'\u001a\u00020\u000eH\u00d6\u0001R\u0013\u0010\u000b\u001a\u0004\u0018\u00010\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0013\u0010\r\u001a\u0004\u0018\u00010\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0017\u0010\t\u001a\b\u0012\u0004\u0012\u00020\b0\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010\u0016R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0004\u0010\u0016R\u0013\u0010\u0007\u001a\u0004\u0018\u00010\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0013\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001a\u00a8\u0006("}, d2 = {"Lcom/sdwu/kotlin/viewmodel/ECGUiState;", "", "isLoading", "", "isMonitoring", "stats", "Lcom/sdwu/kotlin/data/repository/ECGStats;", "latestWaveformData", "Lcom/sdwu/kotlin/data/model/ECGWaveformData;", "historicalData", "", "analysisResult", "Lcom/sdwu/kotlin/data/model/ECGAnalysisResult;", "error", "", "(ZZLcom/sdwu/kotlin/data/repository/ECGStats;Lcom/sdwu/kotlin/data/model/ECGWaveformData;Ljava/util/List;Lcom/sdwu/kotlin/data/model/ECGAnalysisResult;Ljava/lang/String;)V", "getAnalysisResult", "()Lcom/sdwu/kotlin/data/model/ECGAnalysisResult;", "getError", "()Ljava/lang/String;", "getHistoricalData", "()Ljava/util/List;", "()Z", "getLatestWaveformData", "()Lcom/sdwu/kotlin/data/model/ECGWaveformData;", "getStats", "()Lcom/sdwu/kotlin/data/repository/ECGStats;", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class ECGUiState {
    private final boolean isLoading = false;
    private final boolean isMonitoring = false;
    @org.jetbrains.annotations.Nullable()
    private final com.sdwu.kotlin.data.repository.ECGStats stats = null;
    @org.jetbrains.annotations.Nullable()
    private final com.sdwu.kotlin.data.model.ECGWaveformData latestWaveformData = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.sdwu.kotlin.data.model.ECGWaveformData> historicalData = null;
    @org.jetbrains.annotations.Nullable()
    private final com.sdwu.kotlin.data.model.ECGAnalysisResult analysisResult = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String error = null;
    
    public ECGUiState(boolean isLoading, boolean isMonitoring, @org.jetbrains.annotations.Nullable()
    com.sdwu.kotlin.data.repository.ECGStats stats, @org.jetbrains.annotations.Nullable()
    com.sdwu.kotlin.data.model.ECGWaveformData latestWaveformData, @org.jetbrains.annotations.NotNull()
    java.util.List<com.sdwu.kotlin.data.model.ECGWaveformData> historicalData, @org.jetbrains.annotations.Nullable()
    com.sdwu.kotlin.data.model.ECGAnalysisResult analysisResult, @org.jetbrains.annotations.Nullable()
    java.lang.String error) {
        super();
    }
    
    public final boolean isLoading() {
        return false;
    }
    
    public final boolean isMonitoring() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.sdwu.kotlin.data.repository.ECGStats getStats() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.sdwu.kotlin.data.model.ECGWaveformData getLatestWaveformData() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.sdwu.kotlin.data.model.ECGWaveformData> getHistoricalData() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.sdwu.kotlin.data.model.ECGAnalysisResult getAnalysisResult() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getError() {
        return null;
    }
    
    public ECGUiState() {
        super();
    }
    
    public final boolean component1() {
        return false;
    }
    
    public final boolean component2() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.sdwu.kotlin.data.repository.ECGStats component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.sdwu.kotlin.data.model.ECGWaveformData component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.sdwu.kotlin.data.model.ECGWaveformData> component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.sdwu.kotlin.data.model.ECGAnalysisResult component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.sdwu.kotlin.viewmodel.ECGUiState copy(boolean isLoading, boolean isMonitoring, @org.jetbrains.annotations.Nullable()
    com.sdwu.kotlin.data.repository.ECGStats stats, @org.jetbrains.annotations.Nullable()
    com.sdwu.kotlin.data.model.ECGWaveformData latestWaveformData, @org.jetbrains.annotations.NotNull()
    java.util.List<com.sdwu.kotlin.data.model.ECGWaveformData> historicalData, @org.jetbrains.annotations.Nullable()
    com.sdwu.kotlin.data.model.ECGAnalysisResult analysisResult, @org.jetbrains.annotations.Nullable()
    java.lang.String error) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}