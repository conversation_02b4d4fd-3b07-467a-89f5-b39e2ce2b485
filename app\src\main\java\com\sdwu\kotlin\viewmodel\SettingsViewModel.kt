package com.sdwu.kotlin.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.sdwu.kotlin.data.model.UserSettings
import com.sdwu.kotlin.data.repository.SettingsRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
/**
 * 设置页面ViewModel
 * 管理用户设置的业务逻辑和UI状态
 */
class SettingsViewModel(
    private val settingsRepository: SettingsRepository
) : ViewModel() {
    
    // UI状态
    private val _uiState = MutableStateFlow(SettingsUiState())
    val uiState: StateFlow<SettingsUiState> = _uiState.asStateFlow()
    
    init {
        loadSettings()
    }
    
    /**
     * 加载用户设置
     */
    private fun loadSettings() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            try {
                settingsRepository.getUserSettings().collect { settings ->
                    _uiState.value = _uiState.value.copy(
                        settings = settings,
                        isLoading = false,
                        error = null
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "加载设置失败"
                )
            }
        }
    }
    
    /**
     * 切换深色模式
     */
    fun toggleDarkMode(enabled: Boolean) {
        viewModelScope.launch {
            try {
                settingsRepository.updateDarkMode(enabled)
                // 状态会通过Flow自动更新
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = e.message ?: "更新深色模式设置失败"
                )
            }
        }
    }
    
    /**
     * 切换通知设置
     */
    fun toggleNotifications(enabled: Boolean) {
        viewModelScope.launch {
            try {
                settingsRepository.updateNotifications(enabled)
                // 状态会通过Flow自动更新
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = e.message ?: "更新通知设置失败"
                )
            }
        }
    }
    
    /**
     * 更新语言设置
     */
    fun updateLanguage(language: String) {
        viewModelScope.launch {
            try {
                settingsRepository.updateLanguage(language)
                // 状态会通过Flow自动更新
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = e.message ?: "更新语言设置失败"
                )
            }
        }
    }
    
    /**
     * 重置所有设置
     */
    fun resetAllSettings() {
        viewModelScope.launch {
            try {
                settingsRepository.resetSettings()
                _uiState.value = _uiState.value.copy(
                    showResetConfirmation = false
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = e.message ?: "重置设置失败"
                )
            }
        }
    }
    
    /**
     * 显示重置确认对话框
     */
    fun showResetConfirmation() {
        _uiState.value = _uiState.value.copy(showResetConfirmation = true)
    }
    
    /**
     * 隐藏重置确认对话框
     */
    fun hideResetConfirmation() {
        _uiState.value = _uiState.value.copy(showResetConfirmation = false)
    }
    
    /**
     * 清除错误状态
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
    
    /**
     * 获取可用语言列表
     */
    fun getAvailableLanguages(): List<Pair<String, String>> {
        return listOf(
            "zh-CN" to "简体中文",
            "en-US" to "English",
            "ja-JP" to "日本語"
        )
    }
}

/**
 * 设置页面UI状态数据类
 */
data class SettingsUiState(
    val settings: UserSettings = UserSettings(),
    val isLoading: Boolean = false,
    val error: String? = null,
    val showResetConfirmation: Boolean = false
)
