package com.sdwu.kotlin.components;

@kotlin.Metadata(mv = {1, 8, 0}, k = 2, xi = 48, d1 = {"\u0000Z\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\b\u0010\u0000\u001a\u00020\u0001H\u0003\u001a@\u0010\u0002\u001a\u00020\u00012\b\u0010\u0003\u001a\u0004\u0018\u00010\u00042\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\b\u001a\u00020\t2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00010\u000b2\b\b\u0002\u0010\f\u001a\u00020\rH\u0007\u001a@\u0010\u000e\u001a\u00020\u00012\b\u0010\u000f\u001a\u0004\u0018\u00010\u00102\u0006\u0010\u0011\u001a\u00020\t2\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00010\u000b2\f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00010\u000b2\b\b\u0002\u0010\f\u001a\u00020\rH\u0007\u001a\u001a\u0010\u0014\u001a\u00020\u00012\u0006\u0010\u0015\u001a\u00020\u00162\b\b\u0002\u0010\f\u001a\u00020\rH\u0003\u001a0\u0010\u0017\u001a\u00020\u00012\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u001b2\f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00160\u00062\b\b\u0002\u0010\f\u001a\u00020\rH\u0003\u001a@\u0010\u001d\u001a\u00020\u00012\b\u0010\u0003\u001a\u0004\u0018\u00010\u00042\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\b\u001a\u00020\t2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00010\u000b2\b\b\u0002\u0010\f\u001a\u00020\rH\u0007\u001a*\u0010\u001e\u001a\u00020\u00012\u0006\u0010\u001f\u001a\u00020\u00192\u0006\u0010 \u001a\u00020\u00192\u0006\u0010!\u001a\u00020\u00192\b\b\u0002\u0010\f\u001a\u00020\rH\u0003\u001a*\u0010\"\u001a\u00020\u00012\u0006\u0010\u001f\u001a\u00020\u00192\u0006\u0010 \u001a\u00020\u00192\u0006\u0010!\u001a\u00020\u00192\b\b\u0002\u0010\f\u001a\u00020\rH\u0003\u001a \u0010#\u001a\u00020\u00012\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\b\b\u0002\u0010\f\u001a\u00020\rH\u0007\u001a \u0010$\u001a\u00020\u00012\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\b\b\u0002\u0010\f\u001a\u00020\rH\u0007\u001a\u001a\u0010%\u001a\u00020\u00012\u0006\u0010&\u001a\u00020\'2\b\b\u0002\u0010\f\u001a\u00020\rH\u0003\u001a\u001a\u0010(\u001a\u00020\u0001*\u00020)2\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006H\u0002\u001a\u001a\u0010*\u001a\u00020\u0001*\u00020)2\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006H\u0002\u00a8\u0006+"}, d2 = {"HRVCardHeader", "", "HRVDataCard", "stats", "Lcom/sdwu/kotlin/data/repository/HRVStats;", "trendData", "", "Lcom/sdwu/kotlin/data/model/HRVTrendData;", "isLoading", "", "onCardClick", "Lkotlin/Function0;", "modifier", "Landroidx/compose/ui/Modifier;", "HRVMeasurementProgress", "realtimeData", "Lcom/sdwu/kotlin/data/model/HRVRealtimeData;", "isMeasuring", "onStartMeasurement", "onStopMeasurement", "HRVMetricCard", "metric", "Lcom/sdwu/kotlin/components/HRVMetricItem;", "HRVMetricSection", "title", "", "icon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "metrics", "HRVMetricsOverviewCard", "HRVRealtimeDataItem", "label", "value", "unit", "HRVStatItem", "HRVTrendChart", "HRVTrendPreview", "StressLevelIndicator", "level", "", "drawHRVTrend", "Landroidx/compose/ui/graphics/drawscope/DrawScope;", "drawHRVTrendChart", "app_debug"})
public final class HRVComponentsKt {
    
    /**
     * HRV数据卡片组件
     * 在首页展示HRV统计信息
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void HRVDataCard(@org.jetbrains.annotations.Nullable()
    com.sdwu.kotlin.data.repository.HRVStats stats, @org.jetbrains.annotations.NotNull()
    java.util.List<com.sdwu.kotlin.data.model.HRVTrendData> trendData, boolean isLoading, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onCardClick, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * HRV统计项组件
     */
    @androidx.compose.runtime.Composable()
    private static final void HRVStatItem(java.lang.String label, java.lang.String value, java.lang.String unit, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 压力水平指示器
     */
    @androidx.compose.runtime.Composable()
    private static final void StressLevelIndicator(int level, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * HRV趋势预览组件
     */
    @androidx.compose.runtime.Composable()
    public static final void HRVTrendPreview(@org.jetbrains.annotations.NotNull()
    java.util.List<com.sdwu.kotlin.data.model.HRVTrendData> trendData, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 绘制HRV趋势图
     */
    private static final void drawHRVTrend(androidx.compose.ui.graphics.drawscope.DrawScope $this$drawHRVTrend, java.util.List<com.sdwu.kotlin.data.model.HRVTrendData> trendData) {
    }
    
    /**
     * HRV测量进度组件
     */
    @androidx.compose.runtime.Composable()
    public static final void HRVMeasurementProgress(@org.jetbrains.annotations.Nullable()
    com.sdwu.kotlin.data.model.HRVRealtimeData realtimeData, boolean isMeasuring, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onStartMeasurement, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onStopMeasurement, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * HRV实时数据项组件
     */
    @androidx.compose.runtime.Composable()
    private static final void HRVRealtimeDataItem(java.lang.String label, java.lang.String value, java.lang.String unit, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * HRV指标概览卡片 - 粉色主题
     * 展示时域指标、频域指标和自主神经指标
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void HRVMetricsOverviewCard(@org.jetbrains.annotations.Nullable()
    com.sdwu.kotlin.data.repository.HRVStats stats, @org.jetbrains.annotations.NotNull()
    java.util.List<com.sdwu.kotlin.data.model.HRVTrendData> trendData, boolean isLoading, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onCardClick, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * HRV卡片标题
     */
    @androidx.compose.runtime.Composable()
    private static final void HRVCardHeader() {
    }
    
    /**
     * HRV指标分类组件
     */
    @androidx.compose.runtime.Composable()
    private static final void HRVMetricSection(java.lang.String title, androidx.compose.ui.graphics.vector.ImageVector icon, java.util.List<com.sdwu.kotlin.components.HRVMetricItem> metrics, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 单个HRV指标卡片
     */
    @androidx.compose.runtime.Composable()
    private static final void HRVMetricCard(com.sdwu.kotlin.components.HRVMetricItem metric, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * HRV趋势图表
     */
    @androidx.compose.runtime.Composable()
    public static final void HRVTrendChart(@org.jetbrains.annotations.NotNull()
    java.util.List<com.sdwu.kotlin.data.model.HRVTrendData> trendData, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 绘制HRV趋势图表
     */
    private static final void drawHRVTrendChart(androidx.compose.ui.graphics.drawscope.DrawScope $this$drawHRVTrendChart, java.util.List<com.sdwu.kotlin.data.model.HRVTrendData> trendData) {
    }
}