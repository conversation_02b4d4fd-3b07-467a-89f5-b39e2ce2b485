package com.sdwu.kotlin.components;

@kotlin.Metadata(mv = {1, 8, 0}, k = 2, xi = 48, d1 = {"\u0000H\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a@\u0010\u0000\u001a\u00020\u00012\b\u0010\u0002\u001a\u0004\u0018\u00010\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\u0006\u0010\u0007\u001a\u00020\b2\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00010\n2\b\b\u0002\u0010\u000b\u001a\u00020\fH\u0007\u001a@\u0010\r\u001a\u00020\u00012\b\u0010\u000e\u001a\u0004\u0018\u00010\u000f2\u0006\u0010\u0010\u001a\u00020\b2\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00010\n2\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00010\n2\b\b\u0002\u0010\u000b\u001a\u00020\fH\u0007\u001a*\u0010\u0013\u001a\u00020\u00012\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u00152\u0006\u0010\u0017\u001a\u00020\u00152\b\b\u0002\u0010\u000b\u001a\u00020\fH\u0003\u001a*\u0010\u0018\u001a\u00020\u00012\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u00152\u0006\u0010\u0017\u001a\u00020\u00152\b\b\u0002\u0010\u000b\u001a\u00020\fH\u0003\u001a \u0010\u0019\u001a\u00020\u00012\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\b\b\u0002\u0010\u000b\u001a\u00020\fH\u0007\u001a\u001a\u0010\u001a\u001a\u00020\u00012\u0006\u0010\u001b\u001a\u00020\u001c2\b\b\u0002\u0010\u000b\u001a\u00020\fH\u0003\u001a\u001a\u0010\u001d\u001a\u00020\u0001*\u00020\u001e2\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u0002\u00a8\u0006\u001f"}, d2 = {"HRVDataCard", "", "stats", "Lcom/sdwu/kotlin/data/repository/HRVStats;", "trendData", "", "Lcom/sdwu/kotlin/data/model/HRVTrendData;", "isLoading", "", "onCardClick", "Lkotlin/Function0;", "modifier", "Landroidx/compose/ui/Modifier;", "HRVMeasurementProgress", "realtimeData", "Lcom/sdwu/kotlin/data/model/HRVRealtimeData;", "isMeasuring", "onStartMeasurement", "onStopMeasurement", "HRVRealtimeDataItem", "label", "", "value", "unit", "HRVStatItem", "HRVTrendPreview", "StressLevelIndicator", "level", "", "drawHRVTrend", "Landroidx/compose/ui/graphics/drawscope/DrawScope;", "app_debug"})
public final class HRVComponentsKt {
    
    /**
     * HRV数据卡片组件
     * 在首页展示HRV统计信息
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void HRVDataCard(@org.jetbrains.annotations.Nullable()
    com.sdwu.kotlin.data.repository.HRVStats stats, @org.jetbrains.annotations.NotNull()
    java.util.List<com.sdwu.kotlin.data.model.HRVTrendData> trendData, boolean isLoading, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onCardClick, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * HRV统计项组件
     */
    @androidx.compose.runtime.Composable()
    private static final void HRVStatItem(java.lang.String label, java.lang.String value, java.lang.String unit, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 压力水平指示器
     */
    @androidx.compose.runtime.Composable()
    private static final void StressLevelIndicator(int level, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * HRV趋势预览组件
     */
    @androidx.compose.runtime.Composable()
    public static final void HRVTrendPreview(@org.jetbrains.annotations.NotNull()
    java.util.List<com.sdwu.kotlin.data.model.HRVTrendData> trendData, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 绘制HRV趋势图
     */
    private static final void drawHRVTrend(androidx.compose.ui.graphics.drawscope.DrawScope $this$drawHRVTrend, java.util.List<com.sdwu.kotlin.data.model.HRVTrendData> trendData) {
    }
    
    /**
     * HRV测量进度组件
     */
    @androidx.compose.runtime.Composable()
    public static final void HRVMeasurementProgress(@org.jetbrains.annotations.Nullable()
    com.sdwu.kotlin.data.model.HRVRealtimeData realtimeData, boolean isMeasuring, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onStartMeasurement, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onStopMeasurement, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * HRV实时数据项组件
     */
    @androidx.compose.runtime.Composable()
    private static final void HRVRealtimeDataItem(java.lang.String label, java.lang.String value, java.lang.String unit, androidx.compose.ui.Modifier modifier) {
    }
}