-- Merging decision tree log ---
manifest
ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:2:1-50:12
INJECTED from D:\kotlin\app\src\main\AndroidManifest.xml:2:1-50:12
INJECTED from D:\kotlin\app\src\main\AndroidManifest.xml:2:1-50:12
INJECTED from D:\kotlin\app\src\main\AndroidManifest.xml:2:1-50:12
MERGED from [androidx.databinding:databinding-adapters:7.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\7cba6d1d50107772cbdccf5730c7b0d0\transformed\databinding-adapters-7.4.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-ktx:7.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\9c243a274344d3cff9ad13476bef664d\transformed\databinding-ktx-7.4.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-runtime:7.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\0a63cdb034580af66a7d7bbb98748e8e\transformed\databinding-runtime-7.4.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:viewbinding:7.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\5a48a72824caccc0a0b209c71679e22e\transformed\viewbinding-7.4.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\bc4489298d587de27050a8722972e255\transformed\navigation-common-2.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4e9c948669127a86352a47b550f04ea\transformed\navigation-runtime-2.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\a69fde0e27b5cd5b9fb0e3f1cdfb5218\transformed\navigation-common-ktx-2.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f8bc8630387cdd342816ed037599ee8\transformed\navigation-runtime-ktx-2.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-compose:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\950a0cf5c30b352203b3921859734d45\transformed\navigation-compose-2.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material3:material3:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\b599f6252b24759b04dd323019160ed1\transformed\material3-1.0.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-ripple:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\3c6c905bc8ff15b1962a067a93371957\transformed\material-ripple-1.4.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-icons-core:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\3007b05ab8637ca40532a9780ba8e6d3\transformed\material-icons-core-1.4.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\e6b6c65cc66fb105eb79ffee46f53e09\transformed\material-1.4.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.animation:animation-core:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\18e13c2d56608b06ff1b9bca4dae5070\transformed\animation-core-1.4.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.animation:animation:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\6e75e18b2f71f3b0425a7fcb371765c9\transformed\animation-1.4.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.foundation:foundation-layout:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\39ab7a0754950697df7dcd96ce6b97cd\transformed\foundation-layout-1.4.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.foundation:foundation:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\f0f60d84375b87dca194f9502a29a651\transformed\foundation-1.4.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-data:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\9b65950f1ea2bfbbac498015515f20c1\transformed\ui-tooling-data-1.4.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-unit:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\1dd7c75426d658f6907c6a1498c9ae72\transformed\ui-unit-1.4.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-geometry:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\fa4784411229205cfe72f2e599ff5e7f\transformed\ui-geometry-1.4.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-util:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\cf6f68681c2b97f7c01d1faee5024981\transformed\ui-util-1.4.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-text:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\b739ddf5a02ad80c3c9d2ff0563d3db8\transformed\ui-text-1.4.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-preview:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\9a5a932042ca3e3d1aece17042a86878\transformed\ui-tooling-preview-1.4.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\2612807d36fe3e67805289f3d7106e95\transformed\ui-test-manifest-1.4.2\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\014b0ee46d8850a2460e2fc49385341b\transformed\ui-graphics-1.4.2\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\1691789794c9b474ae1fde820463afff\transformed\material-1.9.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03203728a12ba003217fa2050e281895\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\d8da8574d7719bfc535718af78797121\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\bc227b5d8c8bab74364961e342d51b40\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b09a2abe8efccf0a75ad733256a66598\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\e42f31351760b4fa20e1e3e66b86d48e\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\b1f14132fe204d986ec2c8823e879161\transformed\activity-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\508da8809b00a85ba24edeb29e50c8a5\transformed\activity-ktx-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-compose:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\006b0a896555d551c6886ea3299dd657\transformed\activity-compose-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\dcb04e3df3850e91504661affb76e7a5\transformed\runtime-saveable-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\af56ec1a1210c92d81ccb23867a629ea\transformed\runtime-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-livedata:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\c1929efc9164d3c9b3947e92a2c3f9c5\transformed\runtime-livedata-1.4.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a5683d67a3fd9cba1a19f5cc0a7079c7\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a9da685414e3b79c379e8174cf4dde0f\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\5f8a6576588630b56d2ccae8fee59c54\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\33fbc14a9dd06ad6d3881ba939131e54\transformed\lifecycle-livedata-core-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a4d00cc5374daccbd011f42370418362\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\37a1f4fb8846d24ad3cba56fd692b375\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2bd5d362f32241ac0c55c7b2f9de5f33\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\43c9ca620ece87f1a13b1b208a29b107\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\02db41d7d25a8c1e85e9a2bcb4ee3442\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\1bee987281c2459b3b4ed88927551654\transformed\lifecycle-service-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9ccfdd6fd0f7e529108f6c3dbfbd8d03\transformed\lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\e208464a20b035b8064c6a370fc20322\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8f3e462c5938a6944d69509b4d522364\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2aa4b69371048caf00dc1c4cd85ce3d6\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8bd3e803f6910584ff9c45e58387161d\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\48f9f3eaae43e347b434764665d4afcf\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b31c4be4546e9403bd90fed2a1b2ce82\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f690ec351373585eee3a42887215fc9\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba9d12b34f211d2c51de2869d79626c1\transformed\core-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c46ca5646988417b2c46c2322ad610f\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\2888833ed77fa4bbfa7b81d3b4c8e929\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b0ac34922e035b7ebcf3bb8c54ab838\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\246fc236acbafb613f1d414e972d5588\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e6ecd4995b1c36752fd1d689defa1b6\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d5d80ec9ea4b92e6a394c21abf0511b\transformed\core-1.9.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\e9a6995f03fc1f72b830795b36fce4a0\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8cd6f891d9b7224515830d4f287d3799\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6c6be7f3a364293f1c44c0724d0be7b6\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\5b7736f8ad4f8b0e8913fb1bf84adae4\transformed\lifecycle-livedata-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\00554425c018ed785d8607360dffe7ff\transformed\lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a714e1f6c0a4fa3ba6a18be5908708c1\transformed\lifecycle-viewmodel-compose-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\2e1e0a755749a1238f97434281347ef6\transformed\ui-1.4.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\989e0b2ad691e9eadb8e98e96dfffb07\transformed\ui-tooling-1.4.2\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a9c920a4d12e1a7990dd599b81795375\transformed\datastore-preferences-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c5331ee9e22e5e8cc9bd82406467c1e\transformed\datastore-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e324f8f99865a73fa2fbab2b48bb412\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1dc960d647e1664ac46cee97743f92a5\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\1efe85d5d29cb6e3418fbc86bc2d653f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\b2df765c4b8d6a571941b6ba5754f96b\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\a13f4b04296c3d5a271d1942a1cd1fd7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e4dd1a0dc8ad23cff2a7aea5739f488b\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a19a1317b92a1514d9842337af201b8f\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1a4084c78f4e7519239d1b9e42ca3447\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e6609382ff2e2ab3ec04bdf7b351b024\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0759397cb6a1dee95c9302b3c7162ed6\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\92cdcd631eac8042193105cd3272ca19\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
INJECTED from D:\kotlin\app\src\main\AndroidManifest.xml:2:1-50:12
INJECTED from D:\kotlin\app\src\main\AndroidManifest.xml:2:1-50:12
INJECTED from D:\kotlin\app\src\main\AndroidManifest.xml:2:1-50:12
	package
		INJECTED from D:\kotlin\app\src\main\AndroidManifest.xml
		ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:2:1-50:12
		INJECTED from D:\kotlin\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\kotlin\app\src\main\AndroidManifest.xml
		ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:2:1-50:12
		INJECTED from D:\kotlin\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\kotlin\app\src\main\AndroidManifest.xml
		ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:2:1-50:12
		INJECTED from D:\kotlin\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:2:11-69
application
ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:5:5-48:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\2612807d36fe3e67805289f3d7106e95\transformed\ui-test-manifest-1.4.2\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\2612807d36fe3e67805289f3d7106e95\transformed\ui-test-manifest-1.4.2\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\1691789794c9b474ae1fde820463afff\transformed\material-1.9.0\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\1691789794c9b474ae1fde820463afff\transformed\material-1.9.0\AndroidManifest.xml:24:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03203728a12ba003217fa2050e281895\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03203728a12ba003217fa2050e281895\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e6ecd4995b1c36752fd1d689defa1b6\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e6ecd4995b1c36752fd1d689defa1b6\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d5d80ec9ea4b92e6a394c21abf0511b\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d5d80ec9ea4b92e6a394c21abf0511b\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8cd6f891d9b7224515830d4f287d3799\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8cd6f891d9b7224515830d4f287d3799\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.compose.ui:ui-tooling:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\989e0b2ad691e9eadb8e98e96dfffb07\transformed\ui-tooling-1.4.2\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\989e0b2ad691e9eadb8e98e96dfffb07\transformed\ui-tooling-1.4.2\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\1efe85d5d29cb6e3418fbc86bc2d653f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\1efe85d5d29cb6e3418fbc86bc2d653f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\a13f4b04296c3d5a271d1942a1cd1fd7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\a13f4b04296c3d5a271d1942a1cd1fd7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d5d80ec9ea4b92e6a394c21abf0511b\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:13:9-35
	android:label
		ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:11:9-41
	android:fullBackupContent
		ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:9:9-54
	android:roundIcon
		ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:12:9-54
	tools:targetApi
		ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:15:9-29
	android:icon
		ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:10:9-43
	android:allowBackup
		ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:7:9-35
	android:theme
		ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:14:9-44
	android:dataExtractionRules
		ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:8:9-65
	android:name
		ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:6:9-42
activity#com.sdwu.kotlin.MainActivity
ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:16:9-26:20
	android:label
		ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:19:13-45
	android:exported
		ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:18:13-36
	android:theme
		ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:20:13-48
	android:name
		ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:17:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:21:13-25:29
action#android.intent.action.MAIN
ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:22:17-69
	android:name
		ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:22:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:24:17-77
	android:name
		ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:24:27-74
activity#com.sdwu.kotlin.TraditionalViewActivity
ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:29:9-33:51
	android:label
		ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:32:13-39
	android:exported
		ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:31:13-37
	android:theme
		ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:33:13-48
	android:name
		ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:30:13-52
activity#com.sdwu.kotlin.ViewBindingActivity
ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:36:9-40:51
	android:label
		ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:39:13-42
	android:exported
		ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:38:13-37
	android:theme
		ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:40:13-48
	android:name
		ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:37:13-48
activity#com.sdwu.kotlin.DataBindingActivity
ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:43:9-47:51
	android:label
		ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:46:13-42
	android:exported
		ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:45:13-37
	android:theme
		ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:47:13-48
	android:name
		ADDED from D:\kotlin\app\src\main\AndroidManifest.xml:44:13-48
uses-sdk
INJECTED from D:\kotlin\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\kotlin\app\src\main\AndroidManifest.xml
INJECTED from D:\kotlin\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:databinding-adapters:7.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\7cba6d1d50107772cbdccf5730c7b0d0\transformed\databinding-adapters-7.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-adapters:7.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\7cba6d1d50107772cbdccf5730c7b0d0\transformed\databinding-adapters-7.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:7.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\9c243a274344d3cff9ad13476bef664d\transformed\databinding-ktx-7.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:7.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\9c243a274344d3cff9ad13476bef664d\transformed\databinding-ktx-7.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:7.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\0a63cdb034580af66a7d7bbb98748e8e\transformed\databinding-runtime-7.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:7.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\0a63cdb034580af66a7d7bbb98748e8e\transformed\databinding-runtime-7.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:7.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\5a48a72824caccc0a0b209c71679e22e\transformed\viewbinding-7.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:7.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\5a48a72824caccc0a0b209c71679e22e\transformed\viewbinding-7.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\bc4489298d587de27050a8722972e255\transformed\navigation-common-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\bc4489298d587de27050a8722972e255\transformed\navigation-common-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4e9c948669127a86352a47b550f04ea\transformed\navigation-runtime-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4e9c948669127a86352a47b550f04ea\transformed\navigation-runtime-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\a69fde0e27b5cd5b9fb0e3f1cdfb5218\transformed\navigation-common-ktx-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\a69fde0e27b5cd5b9fb0e3f1cdfb5218\transformed\navigation-common-ktx-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f8bc8630387cdd342816ed037599ee8\transformed\navigation-runtime-ktx-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f8bc8630387cdd342816ed037599ee8\transformed\navigation-runtime-ktx-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-compose:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\950a0cf5c30b352203b3921859734d45\transformed\navigation-compose-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-compose:2.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\950a0cf5c30b352203b3921859734d45\transformed\navigation-compose-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\b599f6252b24759b04dd323019160ed1\transformed\material3-1.0.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\b599f6252b24759b04dd323019160ed1\transformed\material3-1.0.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-ripple:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\3c6c905bc8ff15b1962a067a93371957\transformed\material-ripple-1.4.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-ripple:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\3c6c905bc8ff15b1962a067a93371957\transformed\material-ripple-1.4.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-icons-core:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\3007b05ab8637ca40532a9780ba8e6d3\transformed\material-icons-core-1.4.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-icons-core:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\3007b05ab8637ca40532a9780ba8e6d3\transformed\material-icons-core-1.4.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\e6b6c65cc66fb105eb79ffee46f53e09\transformed\material-1.4.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\e6b6c65cc66fb105eb79ffee46f53e09\transformed\material-1.4.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.animation:animation-core:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\18e13c2d56608b06ff1b9bca4dae5070\transformed\animation-core-1.4.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.animation:animation-core:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\18e13c2d56608b06ff1b9bca4dae5070\transformed\animation-core-1.4.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.animation:animation:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\6e75e18b2f71f3b0425a7fcb371765c9\transformed\animation-1.4.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.animation:animation:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\6e75e18b2f71f3b0425a7fcb371765c9\transformed\animation-1.4.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.foundation:foundation-layout:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\39ab7a0754950697df7dcd96ce6b97cd\transformed\foundation-layout-1.4.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.foundation:foundation-layout:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\39ab7a0754950697df7dcd96ce6b97cd\transformed\foundation-layout-1.4.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.foundation:foundation:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\f0f60d84375b87dca194f9502a29a651\transformed\foundation-1.4.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.foundation:foundation:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\f0f60d84375b87dca194f9502a29a651\transformed\foundation-1.4.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-data:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\9b65950f1ea2bfbbac498015515f20c1\transformed\ui-tooling-data-1.4.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-data:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\9b65950f1ea2bfbbac498015515f20c1\transformed\ui-tooling-data-1.4.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-unit:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\1dd7c75426d658f6907c6a1498c9ae72\transformed\ui-unit-1.4.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-unit:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\1dd7c75426d658f6907c6a1498c9ae72\transformed\ui-unit-1.4.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-geometry:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\fa4784411229205cfe72f2e599ff5e7f\transformed\ui-geometry-1.4.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-geometry:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\fa4784411229205cfe72f2e599ff5e7f\transformed\ui-geometry-1.4.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-util:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\cf6f68681c2b97f7c01d1faee5024981\transformed\ui-util-1.4.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-util:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\cf6f68681c2b97f7c01d1faee5024981\transformed\ui-util-1.4.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-text:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\b739ddf5a02ad80c3c9d2ff0563d3db8\transformed\ui-text-1.4.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-text:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\b739ddf5a02ad80c3c9d2ff0563d3db8\transformed\ui-text-1.4.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\9a5a932042ca3e3d1aece17042a86878\transformed\ui-tooling-preview-1.4.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\9a5a932042ca3e3d1aece17042a86878\transformed\ui-tooling-preview-1.4.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\2612807d36fe3e67805289f3d7106e95\transformed\ui-test-manifest-1.4.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\2612807d36fe3e67805289f3d7106e95\transformed\ui-test-manifest-1.4.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\014b0ee46d8850a2460e2fc49385341b\transformed\ui-graphics-1.4.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\014b0ee46d8850a2460e2fc49385341b\transformed\ui-graphics-1.4.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\1691789794c9b474ae1fde820463afff\transformed\material-1.9.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\1691789794c9b474ae1fde820463afff\transformed\material-1.9.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03203728a12ba003217fa2050e281895\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03203728a12ba003217fa2050e281895\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\d8da8574d7719bfc535718af78797121\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\d8da8574d7719bfc535718af78797121\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\bc227b5d8c8bab74364961e342d51b40\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\bc227b5d8c8bab74364961e342d51b40\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b09a2abe8efccf0a75ad733256a66598\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b09a2abe8efccf0a75ad733256a66598\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\e42f31351760b4fa20e1e3e66b86d48e\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\e42f31351760b4fa20e1e3e66b86d48e\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\b1f14132fe204d986ec2c8823e879161\transformed\activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\b1f14132fe204d986ec2c8823e879161\transformed\activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\508da8809b00a85ba24edeb29e50c8a5\transformed\activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\508da8809b00a85ba24edeb29e50c8a5\transformed\activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\006b0a896555d551c6886ea3299dd657\transformed\activity-compose-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\006b0a896555d551c6886ea3299dd657\transformed\activity-compose-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\dcb04e3df3850e91504661affb76e7a5\transformed\runtime-saveable-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\dcb04e3df3850e91504661affb76e7a5\transformed\runtime-saveable-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\af56ec1a1210c92d81ccb23867a629ea\transformed\runtime-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\af56ec1a1210c92d81ccb23867a629ea\transformed\runtime-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-livedata:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\c1929efc9164d3c9b3947e92a2c3f9c5\transformed\runtime-livedata-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-livedata:1.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\c1929efc9164d3c9b3947e92a2c3f9c5\transformed\runtime-livedata-1.4.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a5683d67a3fd9cba1a19f5cc0a7079c7\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a5683d67a3fd9cba1a19f5cc0a7079c7\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a9da685414e3b79c379e8174cf4dde0f\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a9da685414e3b79c379e8174cf4dde0f\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\5f8a6576588630b56d2ccae8fee59c54\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\5f8a6576588630b56d2ccae8fee59c54\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\33fbc14a9dd06ad6d3881ba939131e54\transformed\lifecycle-livedata-core-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\33fbc14a9dd06ad6d3881ba939131e54\transformed\lifecycle-livedata-core-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a4d00cc5374daccbd011f42370418362\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a4d00cc5374daccbd011f42370418362\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\37a1f4fb8846d24ad3cba56fd692b375\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\37a1f4fb8846d24ad3cba56fd692b375\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2bd5d362f32241ac0c55c7b2f9de5f33\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2bd5d362f32241ac0c55c7b2f9de5f33\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\43c9ca620ece87f1a13b1b208a29b107\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\43c9ca620ece87f1a13b1b208a29b107\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\02db41d7d25a8c1e85e9a2bcb4ee3442\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\02db41d7d25a8c1e85e9a2bcb4ee3442\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\1bee987281c2459b3b4ed88927551654\transformed\lifecycle-service-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\1bee987281c2459b3b4ed88927551654\transformed\lifecycle-service-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9ccfdd6fd0f7e529108f6c3dbfbd8d03\transformed\lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9ccfdd6fd0f7e529108f6c3dbfbd8d03\transformed\lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\e208464a20b035b8064c6a370fc20322\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\e208464a20b035b8064c6a370fc20322\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8f3e462c5938a6944d69509b4d522364\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8f3e462c5938a6944d69509b4d522364\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2aa4b69371048caf00dc1c4cd85ce3d6\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2aa4b69371048caf00dc1c4cd85ce3d6\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8bd3e803f6910584ff9c45e58387161d\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8bd3e803f6910584ff9c45e58387161d\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\48f9f3eaae43e347b434764665d4afcf\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\48f9f3eaae43e347b434764665d4afcf\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b31c4be4546e9403bd90fed2a1b2ce82\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b31c4be4546e9403bd90fed2a1b2ce82\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f690ec351373585eee3a42887215fc9\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f690ec351373585eee3a42887215fc9\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba9d12b34f211d2c51de2869d79626c1\transformed\core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ba9d12b34f211d2c51de2869d79626c1\transformed\core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c46ca5646988417b2c46c2322ad610f\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c46ca5646988417b2c46c2322ad610f\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\2888833ed77fa4bbfa7b81d3b4c8e929\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\2888833ed77fa4bbfa7b81d3b4c8e929\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b0ac34922e035b7ebcf3bb8c54ab838\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b0ac34922e035b7ebcf3bb8c54ab838\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\246fc236acbafb613f1d414e972d5588\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\246fc236acbafb613f1d414e972d5588\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e6ecd4995b1c36752fd1d689defa1b6\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e6ecd4995b1c36752fd1d689defa1b6\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d5d80ec9ea4b92e6a394c21abf0511b\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d5d80ec9ea4b92e6a394c21abf0511b\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\e9a6995f03fc1f72b830795b36fce4a0\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\e9a6995f03fc1f72b830795b36fce4a0\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8cd6f891d9b7224515830d4f287d3799\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8cd6f891d9b7224515830d4f287d3799\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6c6be7f3a364293f1c44c0724d0be7b6\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6c6be7f3a364293f1c44c0724d0be7b6\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\5b7736f8ad4f8b0e8913fb1bf84adae4\transformed\lifecycle-livedata-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\5b7736f8ad4f8b0e8913fb1bf84adae4\transformed\lifecycle-livedata-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\00554425c018ed785d8607360dffe7ff\transformed\lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\00554425c018ed785d8607360dffe7ff\transformed\lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a714e1f6c0a4fa3ba6a18be5908708c1\transformed\lifecycle-viewmodel-compose-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a714e1f6c0a4fa3ba6a18be5908708c1\transformed\lifecycle-viewmodel-compose-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\2e1e0a755749a1238f97434281347ef6\transformed\ui-1.4.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\2e1e0a755749a1238f97434281347ef6\transformed\ui-1.4.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\989e0b2ad691e9eadb8e98e96dfffb07\transformed\ui-tooling-1.4.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\989e0b2ad691e9eadb8e98e96dfffb07\transformed\ui-tooling-1.4.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a9c920a4d12e1a7990dd599b81795375\transformed\datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a9c920a4d12e1a7990dd599b81795375\transformed\datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c5331ee9e22e5e8cc9bd82406467c1e\transformed\datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c5331ee9e22e5e8cc9bd82406467c1e\transformed\datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e324f8f99865a73fa2fbab2b48bb412\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9e324f8f99865a73fa2fbab2b48bb412\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1dc960d647e1664ac46cee97743f92a5\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1dc960d647e1664ac46cee97743f92a5\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\1efe85d5d29cb6e3418fbc86bc2d653f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\1efe85d5d29cb6e3418fbc86bc2d653f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\b2df765c4b8d6a571941b6ba5754f96b\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\b2df765c4b8d6a571941b6ba5754f96b\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\a13f4b04296c3d5a271d1942a1cd1fd7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\a13f4b04296c3d5a271d1942a1cd1fd7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e4dd1a0dc8ad23cff2a7aea5739f488b\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e4dd1a0dc8ad23cff2a7aea5739f488b\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a19a1317b92a1514d9842337af201b8f\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a19a1317b92a1514d9842337af201b8f\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1a4084c78f4e7519239d1b9e42ca3447\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1a4084c78f4e7519239d1b9e42ca3447\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e6609382ff2e2ab3ec04bdf7b351b024\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e6609382ff2e2ab3ec04bdf7b351b024\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0759397cb6a1dee95c9302b3c7162ed6\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0759397cb6a1dee95c9302b3c7162ed6\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\92cdcd631eac8042193105cd3272ca19\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\92cdcd631eac8042193105cd3272ca19\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
INJECTED from D:\kotlin\app\src\main\AndroidManifest.xml
INJECTED from D:\kotlin\app\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from D:\kotlin\app\src\main\AndroidManifest.xml
		ADDED from D:\kotlin\app\src\main\AndroidManifest.xml
		INJECTED from D:\kotlin\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\kotlin\app\src\main\AndroidManifest.xml
		ADDED from D:\kotlin\app\src\main\AndroidManifest.xml
		INJECTED from D:\kotlin\app\src\main\AndroidManifest.xml
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\2612807d36fe3e67805289f3d7106e95\transformed\ui-test-manifest-1.4.2\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\2612807d36fe3e67805289f3d7106e95\transformed\ui-test-manifest-1.4.2\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\2612807d36fe3e67805289f3d7106e95\transformed\ui-test-manifest-1.4.2\AndroidManifest.xml:24:13-63
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e6ecd4995b1c36752fd1d689defa1b6\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8cd6f891d9b7224515830d4f287d3799\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8cd6f891d9b7224515830d4f287d3799\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\1efe85d5d29cb6e3418fbc86bc2d653f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\1efe85d5d29cb6e3418fbc86bc2d653f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e6ecd4995b1c36752fd1d689defa1b6\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e6ecd4995b1c36752fd1d689defa1b6\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e6ecd4995b1c36752fd1d689defa1b6\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e6ecd4995b1c36752fd1d689defa1b6\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e6ecd4995b1c36752fd1d689defa1b6\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e6ecd4995b1c36752fd1d689defa1b6\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e6ecd4995b1c36752fd1d689defa1b6\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d5d80ec9ea4b92e6a394c21abf0511b\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d5d80ec9ea4b92e6a394c21abf0511b\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d5d80ec9ea4b92e6a394c21abf0511b\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
permission#com.sdwu.kotlin.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d5d80ec9ea4b92e6a394c21abf0511b\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d5d80ec9ea4b92e6a394c21abf0511b\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d5d80ec9ea4b92e6a394c21abf0511b\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d5d80ec9ea4b92e6a394c21abf0511b\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d5d80ec9ea4b92e6a394c21abf0511b\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
uses-permission#com.sdwu.kotlin.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d5d80ec9ea4b92e6a394c21abf0511b\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d5d80ec9ea4b92e6a394c21abf0511b\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8cd6f891d9b7224515830d4f287d3799\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8cd6f891d9b7224515830d4f287d3799\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8cd6f891d9b7224515830d4f287d3799\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\989e0b2ad691e9eadb8e98e96dfffb07\transformed\ui-tooling-1.4.2\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\989e0b2ad691e9eadb8e98e96dfffb07\transformed\ui-tooling-1.4.2\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\989e0b2ad691e9eadb8e98e96dfffb07\transformed\ui-tooling-1.4.2\AndroidManifest.xml:24:13-71
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f7339a8108937fc6d25e8ac348752de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
