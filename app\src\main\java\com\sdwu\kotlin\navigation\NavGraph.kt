package com.sdwu.kotlin.navigation

import android.util.Log
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import com.sdwu.kotlin.screens.*
import com.sdwu.kotlin.utils.ErrorLogger

/**
 * 应用导航图
 * 定义所有页面的导航路由和页面组件
 */
@Composable
fun NavGraph(navController: NavHostController) {
    // 使用LaunchedEffect来记录导航图初始化日志
    LaunchedEffect(Unit) {
        try {
            Log.d("NavGraph", "开始初始化导航图")
            ErrorLogger.logInfo("NavGraph", "导航图初始化开始")
        } catch (e: Exception) {
            Log.e("NavGraph", "导航图日志记录失败", e)
        }
    }

    NavHost(
        navController = navController,
        startDestination = Routes.HOME
    ) {
        // 首页
        composable(Routes.HOME) {
            // 使用LaunchedEffect来记录页面加载日志
            LaunchedEffect(Unit) {
                try {
                    Log.d("NavGraph", "加载首页")
                    ErrorLogger.logInfo("NavGraph", "正在加载首页")
                } catch (e: Exception) {
                    Log.e("NavGraph", "首页日志记录失败", e)
                }
            }
            HomeScreen(navController)
        }

        // 个人资料页
        composable(Routes.PROFILE) {
            LaunchedEffect(Unit) {
                try {
                    Log.d("NavGraph", "加载个人资料页面")
                    ErrorLogger.logInfo("NavGraph", "正在加载个人资料页面")
                } catch (e: Exception) {
                    Log.e("NavGraph", "个人资料页面日志记录失败", e)
                }
            }
            // 临时使用简化版本进行测试
            SimpleProfileScreen(navController)
        }

        // 设置页
        composable(Routes.SETTINGS) {
            LaunchedEffect(Unit) {
                try {
                    Log.d("NavGraph", "加载设置页面")
                    ErrorLogger.logInfo("NavGraph", "正在加载设置页面")
                } catch (e: Exception) {
                    Log.e("NavGraph", "设置页面日志记录失败", e)
                }
            }
            SettingsScreen(navController)
        }

        // 详情页（带参数）
        composable(Routes.DETAIL) { backStackEntry ->
            val itemId = backStackEntry.arguments?.getString("itemId")

            LaunchedEffect(itemId) {
                try {
                    Log.d("NavGraph", "加载详情页面")
                    Log.d("NavGraph", "详情页面参数 itemId: $itemId")
                    ErrorLogger.logInfo("NavGraph", "正在加载详情页面，itemId: $itemId")
                } catch (e: Exception) {
                    Log.e("NavGraph", "详情页面日志记录失败", e)
                }
            }
            DetailScreen(navController, itemId)
        }
    }

    // 使用LaunchedEffect来记录导航图完成日志
    LaunchedEffect(Unit) {
        try {
            Log.d("NavGraph", "导航图初始化完成")
            ErrorLogger.logInfo("NavGraph", "导航图初始化完成")
        } catch (e: Exception) {
            Log.e("NavGraph", "导航图完成日志记录失败", e)
        }
    }
}
