apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'

android {
    namespace 'com.sdwu.kotlin'
    compileSdk 33

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
        
    defaultConfig {
        applicationId "com.sdwu.kotlin"
        minSdk 29
        targetSdk 33
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary true
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    kotlinOptions {
        jvmTarget = '11'
    }
    buildFeatures {
        compose true
        viewBinding true
        // 暂时禁用dataBinding以避免版本兼容问题
         dataBinding true
    }
    composeOptions {
        kotlinCompilerExtensionVersion '1.4.6'
    }
    packagingOptions {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
        }
    }
}

kotlin {
    jvmToolchain(11)
}

dependencies {
    implementation 'org.jetbrains.kotlin:kotlin-stdlib:1.8.20'
    implementation 'androidx.core:core-ktx:1.8.0'
    implementation platform('org.jetbrains.kotlin:kotlin-bom:1.8.0')
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.3.1'
    implementation 'androidx.activity:activity-compose:1.5.1'
    implementation platform('androidx.compose:compose-bom:2023.04.01')
    implementation 'androidx.compose.ui:ui'
    implementation 'androidx.compose.ui:ui-graphics'
    implementation 'androidx.compose.ui:ui-tooling-preview'
    implementation 'androidx.compose.material3:material3'
    implementation 'androidx.navigation:navigation-compose:2.6.0'
    implementation 'androidx.core:core-ktx:1.8.0'

    // MVVM Architecture dependencies
    implementation 'androidx.lifecycle:lifecycle-viewmodel-compose:2.6.1'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.6.1'
    implementation 'androidx.compose.runtime:runtime-livedata:1.4.3'

    // Coroutines for async operations
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4'

    // 暂时移除Room依赖以避免SQLite权限问题
    // 使用内存存储替代数据库
    // implementation 'androidx.room:room-runtime:2.4.3'
    // implementation 'androidx.room:room-ktx:2.4.3'
    // kapt 'androidx.room:room-compiler:2.4.3'

    // DataStore for preferences
    implementation 'androidx.datastore:datastore-preferences:1.0.0'

    // DataBinding and ViewBinding support
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.activity:activity-ktx:1.7.2'
    implementation 'com.google.android.material:material:1.9.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'

    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'
    androidTestImplementation platform('androidx.compose:compose-bom:2023.04.01')
    androidTestImplementation 'androidx.compose.ui:ui-test-junit4'
    debugImplementation 'androidx.compose.ui:ui-tooling'
    debugImplementation 'androidx.compose.ui:ui-test-manifest'
}