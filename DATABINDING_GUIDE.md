# Android DataBinding 使用指南

## 📋 概述

本指南展示了如何在Android Kotlin项目中使用DataBinding，结合MVVM架构实现双向数据绑定。

## 🔧 配置DataBinding

### 1. 在build.gradle中启用DataBinding

```gradle
android {
    buildFeatures {
        dataBinding true
        viewBinding true
    }
}

dependencies {
    // DataBinding支持库
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.9.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
}
```

### 2. 创建DataBinding布局

```xml
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data>
        <variable
            name="viewModel"
            type="com.sdwu.kotlin.viewmodel.DataBindingProfileViewModel" />
    </data>
    
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        
        <TextView
            android:text="@{viewModel.user.name}"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
            
    </LinearLayout>
</layout>
```

## 🏗️ MVVM + DataBinding架构

### 1. ViewModel设计

```kotlin
class DataBindingProfileViewModel(
    private val userRepository: UserRepository
) : ViewModel() {
    
    // 使用LiveData进行数据绑定
    private val _user = MutableLiveData<User?>()
    val user: LiveData<User?> = _user
    
    private val _isLoading = MutableLiveData(false)
    val isLoading: LiveData<Boolean> = _isLoading
    
    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error
}
```

### 2. Activity中使用DataBinding

```kotlin
class DataBindingActivity : AppCompatActivity() {
    private lateinit var binding: ActivityDataBindingBinding
    private lateinit var viewModel: DataBindingProfileViewModel
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 初始化DataBinding
        binding = DataBindingUtil.setContentView(this, R.layout.activity_data_binding)
        
        // 设置ViewModel
        binding.viewModel = viewModel
        binding.lifecycleOwner = this // 用于LiveData观察
    }
}
```

## 📊 DataBinding表达式

### 1. 基本绑定

```xml
<!-- 文本绑定 -->
<TextView android:text="@{viewModel.user.name}" />

<!-- 可见性绑定 -->
<ProgressBar android:visibility="@{viewModel.isLoading ? View.VISIBLE : View.GONE}" />

<!-- 启用状态绑定 -->
<Button android:enabled="@{!viewModel.isLoading}" />
```

### 2. 字符串格式化

```xml
<!-- 字符串拼接 -->
<TextView android:text="@{`用户: ` + viewModel.user.name + `\n邮箱: ` + viewModel.user.email}" />

<!-- 条件表达式 -->
<TextView android:text="@{viewModel.error != null ? viewModel.error : `没有错误`}" />
```

### 3. 双向数据绑定

```xml
<!-- 双向绑定EditText -->
<EditText android:text="@={viewModel.userName}" />

<!-- 双向绑定CheckBox -->
<CheckBox android:checked="@={viewModel.isEnabled}" />
```

## 🎯 DataBinding优势

### 1. 性能优势
- **编译时检查**: 在编译时检查绑定表达式
- **避免findViewById**: 自动生成绑定类
- **类型安全**: 编译时类型检查

### 2. 代码简化
- **减少样板代码**: 不需要手动设置View属性
- **自动更新**: LiveData变化时自动更新UI
- **双向绑定**: 简化表单处理

### 3. 维护性
- **声明式UI**: 在布局文件中直接声明数据绑定
- **集中管理**: 数据逻辑集中在ViewModel中

## 🔄 与Compose对比

| 特性 | DataBinding | Jetpack Compose |
|------|-------------|-----------------|
| 学习曲线 | 较低 | 较高 |
| 性能 | 良好 | 优秀 |
| 代码量 | 中等 | 较少 |
| 类型安全 | 编译时检查 | 编译时检查 |
| 动画支持 | 有限 | 强大 |
| 社区支持 | 成熟 | 快速发展 |

## 🚀 最佳实践

### 1. ViewModel设计
```kotlin
// 使用LiveData而不是StateFlow（DataBinding更好支持）
val user: LiveData<User?> = _user

// 提供清晰的业务方法
fun updateUserInfo(name: String, email: String)
fun loadUserProfile()
fun clearError()
```

### 2. 布局组织
```xml
<!-- 将复杂逻辑移到ViewModel中 -->
<TextView android:text="@{viewModel.formattedUserInfo}" />

<!-- 而不是在布局中进行复杂计算 -->
<TextView android:text="@{viewModel.user.name + ` (` + viewModel.user.email + `)`}" />
```

### 3. 错误处理
```kotlin
// 在ViewModel中统一处理错误
private fun handleError(e: Exception) {
    _error.value = when (e) {
        is NetworkException -> "网络连接失败"
        is ValidationException -> e.message
        else -> "未知错误"
    }
}
```

## 📱 示例项目结构

```
app/
├── src/main/java/com/sdwu/kotlin/
│   ├── DataBindingActivity.kt          # DataBinding示例Activity
│   ├── viewmodel/
│   │   └── DataBindingProfileViewModel.kt  # 支持DataBinding的ViewModel
│   └── data/
│       ├── model/User.kt               # 数据模型
│       └── repository/UserRepository.kt    # 数据仓库
└── src/main/res/layout/
    └── activity_data_binding.xml       # DataBinding布局文件
```

## 🧪 测试建议

### 1. ViewModel测试
```kotlin
@Test
fun `when user loads, should update LiveData`() {
    // Given
    val user = User("1", "Test", "<EMAIL>")
    
    // When
    viewModel.loadUserProfile()
    
    // Then
    assertEquals(user, viewModel.user.value)
}
```

### 2. DataBinding测试
```kotlin
@Test
fun `when user name changes, should update TextView`() {
    // 使用Espresso测试DataBinding
    onView(withId(R.id.tv_user_name))
        .check(matches(withText("Test User")))
}
```

## 📝 总结

DataBinding是Android开发中一个强大的工具，特别适合：
- 传统View系统项目
- 需要复杂表单处理的应用
- 团队对XML布局更熟悉的项目
- 需要与现有代码库兼容的情况

结合MVVM架构，DataBinding可以提供清晰的数据流和良好的代码组织结构。
