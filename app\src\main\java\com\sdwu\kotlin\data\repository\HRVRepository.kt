package com.sdwu.kotlin.data.repository

import com.sdwu.kotlin.data.model.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.delay
import kotlin.math.*
import kotlin.random.Random

/**
 * HRV数据仓库
 * 管理心率变异性数据的获取、存储和分析
 */
class HRVRepository {
    
    /**
     * 获取实时HRV数据流
     */
    fun getRealtimeHRVData(sessionId: String): Flow<HRVRealtimeData> = flow {
        var progress = 0f
        val targetDuration = 300000L // 5分钟测量
        val startTime = System.currentTimeMillis()
        
        while (progress < 1.0f) {
            val currentTime = System.currentTimeMillis()
            progress = ((currentTime - startTime).toFloat() / targetDuration).coerceAtMost(1.0f)
            
            val realtimeData = HRVRealtimeData(
                sessionId = sessionId,
                currentRMSSD = Random.nextFloat() * 50 + 20, // 20-70ms
                currentHeartRate = Random.nextInt(60, 100),
                measurementProgress = progress,
                dataQuality = Random.nextFloat() * 0.3f + 0.7f, // 0.7-1.0
                isStable = Random.nextBoolean(),
                timestamp = currentTime
            )
            
            emit(realtimeData)
            delay(1000) // 每秒更新一次
        }
    }
    
    /**
     * 获取最新的HRV数据（用于首页展示）
     */
    suspend fun getLatestHRVData(patientId: String): HRVData? {
        delay(300) // 模拟网络延迟
        
        return generateMockHRVData(patientId)
    }
    
    /**
     * 获取HRV历史数据
     */
    suspend fun getHistoricalHRVData(
        patientId: String,
        startTime: Long,
        endTime: Long
    ): List<HRVData> {
        delay(500)
        
        val dataList = mutableListOf<HRVData>()
        val dayInMillis = 24 * 60 * 60 * 1000L
        var currentTime = startTime
        
        while (currentTime <= endTime) {
            dataList.add(generateMockHRVData(patientId, currentTime))
            currentTime += dayInMillis
        }
        
        return dataList
    }
    
    /**
     * 获取HRV趋势数据（用于图表展示）
     */
    suspend fun getHRVTrendData(
        patientId: String,
        days: Int = 7
    ): List<HRVTrendData> {
        delay(400)
        
        val trendData = mutableListOf<HRVTrendData>()
        val dayInMillis = 24 * 60 * 60 * 1000L
        val startTime = System.currentTimeMillis() - (days * dayInMillis)
        
        for (i in 0 until days) {
            val date = startTime + (i * dayInMillis)
            trendData.add(
                HRVTrendData(
                    date = date,
                    avgRMSSD = Random.nextFloat() * 30 + 20, // 20-50ms
                    avgSDNN = Random.nextFloat() * 40 + 30, // 30-70ms
                    avgLFHFRatio = Random.nextFloat() * 3 + 0.5f, // 0.5-3.5
                    stressScore = Random.nextFloat() * 100,
                    recoveryScore = Random.nextFloat() * 100,
                    dataQuality = Random.nextFloat() * 0.3f + 0.7f
                )
            )
        }
        
        return trendData
    }
    
    /**
     * 获取HRV统计数据（用于首页卡片展示）
     */
    suspend fun getHRVStats(patientId: String): HRVStats {
        delay(200)
        
        return HRVStats(
            averageRMSSD = Random.nextFloat() * 30 + 20,
            averageSDNN = Random.nextFloat() * 40 + 30,
            averageLFHFRatio = Random.nextFloat() * 3 + 0.5f,
            stressLevel = Random.nextInt(1, 6), // 1-5级
            recoveryLevel = Random.nextInt(1, 6),
            totalMeasurements = Random.nextInt(5, 30),
            lastMeasurementTime = System.currentTimeMillis() - Random.nextLong(7200000) // 2小时内
        )
    }
    
    /**
     * 开始HRV测量会话
     */
    suspend fun startHRVMeasurement(patientId: String): String {
        delay(100)
        return "hrv_session_${System.currentTimeMillis()}"
    }
    
    /**
     * 停止HRV测量会话
     */
    suspend fun stopHRVMeasurement(sessionId: String): HRVData? {
        delay(200)
        return generateMockHRVData("patient_001")
    }
    
    /**
     * 生成模拟HRV数据
     */
    private fun generateMockHRVData(patientId: String, recordTime: Long = System.currentTimeMillis()): HRVData {
        // 生成模拟R-R间期数据
        val rrIntervals = mutableListOf<RRInterval>()
        val baseInterval = 800f // 基础R-R间期（ms）
        
        for (i in 0 until 300) { // 5分钟的数据
            val timestamp = recordTime + (i * 1000L)
            val variation = Random.nextFloat() * 100 - 50 // ±50ms变化
            val interval = baseInterval + variation
            
            rrIntervals.add(
                RRInterval(
                    timestamp = timestamp,
                    interval = interval,
                    quality = Random.nextFloat() * 0.3f + 0.7f
                )
            )
        }
        
        // 计算时域指标
        val intervals = rrIntervals.map { it.interval }
        val meanRR = intervals.average().toFloat()
        val sdnn = sqrt(intervals.map { (it - meanRR).pow(2) }.average()).toFloat()
        val rmssd = calculateRMSSD(intervals)
        val pnn50 = calculatePNN50(intervals)
        
        val timeDomainMetrics = HRVTimeDomainMetrics(
            meanRR = meanRR,
            sdnn = sdnn,
            rmssd = rmssd,
            pnn50 = pnn50,
            triangularIndex = Random.nextFloat() * 20 + 10,
            tinn = Random.nextFloat() * 200 + 100
        )
        
        // 生成频域指标
        val frequencyDomainMetrics = HRVFrequencyDomainMetrics(
            totalPower = Random.nextFloat() * 5000 + 1000,
            vlf = Random.nextFloat() * 1000 + 200,
            lf = Random.nextFloat() * 1500 + 300,
            hf = Random.nextFloat() * 1000 + 200,
            lfHfRatio = Random.nextFloat() * 3 + 0.5f,
            lfNorm = Random.nextFloat() * 80 + 10,
            hfNorm = Random.nextFloat() * 80 + 10
        )
        
        // 生成非线性指标
        val nonlinearMetrics = HRVNonlinearMetrics(
            sd1 = Random.nextFloat() * 30 + 10,
            sd2 = Random.nextFloat() * 50 + 20,
            sd1Sd2Ratio = Random.nextFloat() * 0.8f + 0.2f,
            approximateEntropy = Random.nextFloat() * 1.5f + 0.5f,
            sampleEntropy = Random.nextFloat() * 2.0f + 0.5f,
            dfa1 = Random.nextFloat() * 0.5f + 0.8f,
            dfa2 = Random.nextFloat() * 0.3f + 0.9f
        )
        
        return HRVData(
            id = "hrv_${System.currentTimeMillis()}",
            patientId = patientId,
            sessionId = "session_${System.currentTimeMillis()}",
            rrIntervals = rrIntervals,
            timeDomainMetrics = timeDomainMetrics,
            frequencyDomainMetrics = frequencyDomainMetrics,
            nonlinearMetrics = nonlinearMetrics,
            measurementDuration = 300000L, // 5分钟
            recordedAt = recordTime,
            status = HRVMeasurementStatus.COMPLETED,
            dataQuality = Random.nextFloat() * 0.3f + 0.7f
        )
    }
    
    /**
     * 计算RMSSD
     */
    private fun calculateRMSSD(intervals: List<Float>): Float {
        if (intervals.size < 2) return 0f
        
        val differences = mutableListOf<Float>()
        for (i in 1 until intervals.size) {
            differences.add(abs(intervals[i] - intervals[i-1]))
        }
        
        return sqrt(differences.map { it.pow(2) }.average()).toFloat()
    }
    
    /**
     * 计算PNN50
     */
    private fun calculatePNN50(intervals: List<Float>): Float {
        if (intervals.size < 2) return 0f
        
        var count = 0
        for (i in 1 until intervals.size) {
            if (abs(intervals[i] - intervals[i-1]) > 50) {
                count++
            }
        }
        
        return (count.toFloat() / (intervals.size - 1)) * 100
    }
}

/**
 * HRV统计数据
 * 用于首页展示
 */
data class HRVStats(
    val averageRMSSD: Float,        // 平均RMSSD
    val averageSDNN: Float,         // 平均SDNN
    val averageLFHFRatio: Float,    // 平均LF/HF比值
    val stressLevel: Int,           // 压力水平（1-5）
    val recoveryLevel: Int,         // 恢复水平（1-5）
    val totalMeasurements: Int,     // 总测量次数
    val lastMeasurementTime: Long   // 最后测量时间
)
