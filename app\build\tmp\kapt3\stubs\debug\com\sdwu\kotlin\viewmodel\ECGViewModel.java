package com.sdwu.kotlin.viewmodel;

/**
 * ECG ViewModel
 * 管理心电图相关的UI状态和业务逻辑
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, xi = 48, d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\u0006\n\u0002\u0010\t\n\u0002\b\u0007\b\u0007\u0018\u0000  2\u00020\u0001:\u0001 B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\u0012\u001a\u00020\u0013J\u000e\u0010\u0014\u001a\u00020\u00132\u0006\u0010\u0015\u001a\u00020\u000bJ\u0010\u0010\u0016\u001a\u00020\u00132\b\b\u0002\u0010\u0017\u001a\u00020\u000bJ$\u0010\u0018\u001a\u00020\u00132\b\b\u0002\u0010\u0017\u001a\u00020\u000b2\b\b\u0002\u0010\u0019\u001a\u00020\u001a2\b\b\u0002\u0010\u001b\u001a\u00020\u001aJ\b\u0010\u001c\u001a\u00020\u0013H\u0014J\u0006\u0010\u001d\u001a\u00020\u0013J\u0010\u0010\u001e\u001a\u00020\u00132\b\b\u0002\u0010\u0017\u001a\u00020\u000bJ\u0006\u0010\u001f\u001a\u00020\u0013R\u0016\u0010\u0005\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\n\u001a\u0004\u0018\u00010\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\f\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0017\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\t0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u000f\u00a8\u0006!"}, d2 = {"Lcom/sdwu/kotlin/viewmodel/ECGViewModel;", "Landroidx/lifecycle/ViewModel;", "ecgRepository", "Lcom/sdwu/kotlin/data/repository/ECGRepository;", "(Lcom/sdwu/kotlin/data/repository/ECGRepository;)V", "_realtimeData", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/sdwu/kotlin/data/model/ECGRealtimeData;", "_uiState", "Lcom/sdwu/kotlin/viewmodel/ECGUiState;", "currentSessionId", "", "realtimeData", "Lkotlinx/coroutines/flow/StateFlow;", "getRealtimeData", "()Lkotlinx/coroutines/flow/StateFlow;", "uiState", "getUiState", "clearError", "", "getAnalysisResult", "waveformId", "loadECGStats", "patientId", "loadHistoricalData", "startTime", "", "endTime", "onCleared", "refresh", "startRealtimeMonitoring", "stopRealtimeMonitoring", "Companion", "app_debug"})
public final class ECGViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.sdwu.kotlin.data.repository.ECGRepository ecgRepository = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "ECGViewModel";
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.sdwu.kotlin.viewmodel.ECGUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.sdwu.kotlin.viewmodel.ECGUiState> uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.sdwu.kotlin.data.model.ECGRealtimeData> _realtimeData = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.sdwu.kotlin.data.model.ECGRealtimeData> realtimeData = null;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String currentSessionId;
    @org.jetbrains.annotations.NotNull()
    public static final com.sdwu.kotlin.viewmodel.ECGViewModel.Companion Companion = null;
    
    public ECGViewModel(@org.jetbrains.annotations.NotNull()
    com.sdwu.kotlin.data.repository.ECGRepository ecgRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.sdwu.kotlin.viewmodel.ECGUiState> getUiState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.sdwu.kotlin.data.model.ECGRealtimeData> getRealtimeData() {
        return null;
    }
    
    /**
     * 加载ECG统计数据
     */
    public final void loadECGStats(@org.jetbrains.annotations.NotNull()
    java.lang.String patientId) {
    }
    
    /**
     * 开始实时ECG监测
     */
    public final void startRealtimeMonitoring(@org.jetbrains.annotations.NotNull()
    java.lang.String patientId) {
    }
    
    /**
     * 停止实时ECG监测
     */
    public final void stopRealtimeMonitoring() {
    }
    
    /**
     * 获取历史ECG数据
     */
    public final void loadHistoricalData(@org.jetbrains.annotations.NotNull()
    java.lang.String patientId, long startTime, long endTime) {
    }
    
    /**
     * 获取ECG分析结果
     */
    public final void getAnalysisResult(@org.jetbrains.annotations.NotNull()
    java.lang.String waveformId) {
    }
    
    /**
     * 清除错误状态
     */
    public final void clearError() {
    }
    
    /**
     * 刷新数据
     */
    public final void refresh() {
    }
    
    @java.lang.Override()
    protected void onCleared() {
    }
    
    @kotlin.Metadata(mv = {1, 8, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/sdwu/kotlin/viewmodel/ECGViewModel$Companion;", "", "()V", "TAG", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}