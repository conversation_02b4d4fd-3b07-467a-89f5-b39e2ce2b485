package com.sdwu.kotlin.screens

import android.util.Log
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.sdwu.kotlin.KotlinApplication
import com.sdwu.kotlin.viewmodel.HomeViewModel
import com.sdwu.kotlin.viewmodel.ECGViewModel
import com.sdwu.kotlin.viewmodel.HRVViewModel
import com.sdwu.kotlin.data.repository.ECGRepository
import com.sdwu.kotlin.data.repository.HRVRepository
import com.sdwu.kotlin.components.ECGDataCard
import com.sdwu.kotlin.components.HRVDataCard
import com.sdwu.kotlin.components.HRVMetricsOverviewCard
import com.sdwu.kotlin.utils.ErrorLogger
import com.sdwu.kotlin.utils.NavigationErrorHandler
import com.sdwu.kotlin.utils.ComposeNavigationHelper
import com.sdwu.kotlin.components.SafeNavigationButton
import com.sdwu.kotlin.ui.theme.*


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(navController: NavController) {
    // 页面加载监控
    ComposeNavigationHelper.MonitorPageLoad(
        pageName = "HomeScreen",
        onLoadStart = { Log.d("HomeScreen", "首页开始加载") },
        onLoadComplete = { Log.d("HomeScreen", "首页加载完成") },
        onLoadError = { e -> Log.e("HomeScreen", "首页加载失败", e) }
    )

    // 导航状态监控
    ComposeNavigationHelper.MonitorNavigationState(navController, "HomeScreen")

    val context = LocalContext.current
    val appContainer = (context.applicationContext as KotlinApplication).appContainer

    // 创建ViewModel实例
    val viewModel: HomeViewModel = viewModel {
        HomeViewModel(appContainer.homeRepository)
    }

    // 创建ECG和HRV ViewModel
    val ecgViewModel: ECGViewModel = viewModel {
        ECGViewModel(appContainer.ecgRepository)
    }

    val hrvViewModel: HRVViewModel = viewModel {
        HRVViewModel(appContainer.hrvRepository)
    }

    // 收集UI状态
    val uiState by viewModel.uiState.collectAsState()
    val searchQuery by viewModel.searchQuery.collectAsState()

    // 收集ECG和HRV状态
    val ecgUiState by ecgViewModel.uiState.collectAsState()
    val hrvUiState by hrvViewModel.uiState.collectAsState()

    // 显示错误信息
    uiState.error?.let { error ->
        LaunchedEffect(error) {
            Log.e("HomeScreen", "UI状态错误: $error")
            ErrorLogger.logError("HomeScreen", "UI状态错误", Exception(error))
        }
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        PinkGradientStart.copy(alpha = 0.15f),
                        PinkGradientEnd.copy(alpha = 0.05f),
                        Color.White
                    )
                )
            )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // 粉色主题标题区域
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 6.dp),
                colors = CardDefaults.cardColors(
                    containerColor = Color.White
                ),
                shape = RoundedCornerShape(16.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            brush = Brush.horizontalGradient(
                                colors = listOf(
                                    HRVPrimary.copy(alpha = 0.1f),
                                    HRVSecondary.copy(alpha = 0.05f)
                                )
                            )
                        )
                        .padding(20.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Favorite,
                            contentDescription = "HRV监测",
                            tint = HRVPrimary,
                            modifier = Modifier.size(32.dp)
                        )
                        Spacer(modifier = Modifier.width(12.dp))
                        Column {
                            Text(
                                text = "HRV压力测试",
                                style = MaterialTheme.typography.headlineSmall,
                                fontWeight = FontWeight.Bold,
                                color = HRVPrimary
                            )
                            Text(
                                text = "BUILD RESILIENCE, CHERISH WELL-BEING",
                                style = MaterialTheme.typography.bodySmall,
                                color = HRVSecondary,
                                fontWeight = FontWeight.Medium
                            )
                        }
                    }

                    IconButton(
                        onClick = { viewModel.refreshData() },
                        modifier = Modifier
                            .background(
                                color = HRVPrimary.copy(alpha = 0.1f),
                                shape = RoundedCornerShape(12.dp)
                            )
                    ) {
                        Icon(
                            Icons.Default.Refresh,
                            contentDescription = "刷新",
                            tint = HRVPrimary
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(20.dp))

            // HRV指标详细展示卡片
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(20.dp)
            ) {
                // 主要HRV指标概览卡片
                item {
                    HRVMetricsOverviewCard(
                        stats = hrvUiState.stats,
                        trendData = hrvUiState.trendData,
                        isLoading = hrvUiState.isLoading,
                        onCardClick = {
                            Log.d("HomeScreen", "点击HRV指标卡片")
                            // TODO: 导航到HRV详情页面
                        }
                    )
                }

                // ECG数据卡片（保持原有功能）
//                item {
//                    ECGDataCard(
//                        stats = ecgUiState.stats,
//                        latestData = ecgUiState.latestWaveformData,
//                        isLoading = ecgUiState.isLoading,
//                        onCardClick = {
//                            Log.d("HomeScreen", "点击ECG卡片")
//                            // TODO: 导航到ECG详情页面
//                        }
//                    )
//                }

                // 导航按钮区域 - 粉色主题
                item {
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        elevation = CardDefaults.cardElevation(defaultElevation = 6.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = Color.White
                        ),
                        shape = RoundedCornerShape(16.dp)
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .background(
                                    brush = Brush.verticalGradient(
                                        colors = listOf(
                                            HRVPrimary.copy(alpha = 0.05f),
                                            Color.White
                                        )
                                    )
                                )
                                .padding(20.dp)
                        ) {
                            Text(
                                text = "快速导航",
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.Bold,
                                color = HRVPrimary,
                                modifier = Modifier.padding(bottom = 16.dp)
                            )

                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceEvenly
                            ) {
                                // 个人资料按钮 - 粉色主题
                                var profileNavigating by remember { mutableStateOf(false) }
                                var profileError by remember { mutableStateOf<String?>(null) }

                                Button(
                                    onClick = {
                                        if (!profileNavigating) {
                                            profileNavigating = true
                                            profileError = null

                                            try {
                                                Log.d("HomeScreen", "=== 开始导航到个人资料 ===")
                                                val success = NavigationErrorHandler.safeNavigateTo(
                                                    navController = navController,
                                                    route = "profile",
                                                    from = "home"
                                                )

                                                if (!success) {
                                                    val errorMsg = "导航到个人资料失败"
                                                    Log.e("HomeScreen", errorMsg)
                                                    profileError = errorMsg
                                                }

                                            } catch (e: Exception) {
                                                val errorMsg = "导航过程中发生异常: ${e.message}"
                                                Log.e("HomeScreen", errorMsg, e)
                                                ErrorLogger.logError("HomeScreen", "导航异常", e)
                                                profileError = errorMsg
                                            } finally {
                                                profileNavigating = false
                                            }
                                        }
                                    },
                                    enabled = !profileNavigating,
                                    modifier = Modifier
                                        .weight(1f)
                                        .height(48.dp),
                                    colors = ButtonDefaults.buttonColors(
                                        containerColor = HRVPrimary,
                                        contentColor = Color.White
                                    ),
                                    shape = RoundedCornerShape(12.dp)
                                ) {
                                    if (profileNavigating) {
                                        CircularProgressIndicator(
                                            modifier = Modifier.size(16.dp),
                                            strokeWidth = 2.dp,
                                            color = Color.White
                                        )
                                        Spacer(modifier = Modifier.width(8.dp))
                                    }
                                    Text(
                                        "个人资料",
                                        fontWeight = FontWeight.Medium
                                    )
                                }

                                Spacer(modifier = Modifier.width(12.dp))

                                // 设置按钮 - 粉色主题
                                var settingsNavigating by remember { mutableStateOf(false) }
                                Button(
                                    onClick = {
                                        if (!settingsNavigating) {
                                            settingsNavigating = true
                                            val success = NavigationErrorHandler.safeNavigateTo(
                                                navController = navController,
                                                route = "settings",
                                                from = "home"
                                            )
                                            if (!success) {
                                                Log.e("HomeScreen", "导航到设置失败")
                                            }
                                            settingsNavigating = false
                                        }
                                    },
                                    enabled = !settingsNavigating,
                                    modifier = Modifier
                                        .weight(1f)
                                        .height(48.dp),
                                    colors = ButtonDefaults.buttonColors(
                                        containerColor = HRVSecondary,
                                        contentColor = Color.White
                                    ),
                                    shape = RoundedCornerShape(12.dp)
                                ) {
                                    if (settingsNavigating) {
                                        CircularProgressIndicator(
                                            modifier = Modifier.size(16.dp),
                                            strokeWidth = 2.dp,
                                            color = Color.White
                                        )
                                        Spacer(modifier = Modifier.width(8.dp))
                                    }
                                    Text(
                                        "设置",
                                        fontWeight = FontWeight.Medium
                                    )
                                }
                            }
                        }
                    }
                }
            }

            // 显示错误信息
            ecgUiState.error?.let { error ->
                LaunchedEffect(error) {
                    Log.e("HomeScreen", "ECG错误: $error")
                }
            }

            hrvUiState.error?.let { error ->
                LaunchedEffect(error) {
                    Log.e("HomeScreen", "HRV错误: $error")
                }
            }
        }
    }
}
