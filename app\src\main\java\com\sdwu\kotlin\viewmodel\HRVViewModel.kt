package com.sdwu.kotlin.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.sdwu.kotlin.data.model.*
import com.sdwu.kotlin.data.repository.HRVRepository
import com.sdwu.kotlin.data.repository.HRVStats
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import android.util.Log

/**
 * HRV ViewModel
 * 管理心率变异性相关的UI状态和业务逻辑
 */
class HRVViewModel(
    private val hrvRepository: HRVRepository
) : ViewModel() {

    companion object {
        private const val TAG = "HRVViewModel"
    }

    // UI状态
    private val _uiState = MutableStateFlow(HRVUiState())
    val uiState: StateFlow<HRVUiState> = _uiState.asStateFlow()

    // 实时HRV数据
    private val _realtimeData = MutableStateFlow<HRVRealtimeData?>(null)
    val realtimeData: StateFlow<HRVRealtimeData?> = _realtimeData.asStateFlow()

    // 当前测量会话ID
    private var currentSessionId: String? = null

    init {
        Log.d(TAG, "HRVViewModel初始化")
        loadHRVStats()
        loadTrendData()
    }

    /**
     * 加载HRV统计数据
     */
    fun loadHRVStats(patientId: String = "default_patient") {
        viewModelScope.launch {
            try {
                Log.d(TAG, "开始加载HRV统计数据")
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                
                val stats = hrvRepository.getHRVStats(patientId)
                val latestData = hrvRepository.getLatestHRVData(patientId)
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    stats = stats,
                    latestData = latestData,
                    error = null
                )
                
                Log.d(TAG, "HRV统计数据加载成功: $stats")
                
            } catch (e: Exception) {
                Log.e(TAG, "加载HRV统计数据失败", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "加载HRV数据失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 加载HRV趋势数据
     */
    fun loadTrendData(patientId: String = "default_patient", days: Int = 7) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "加载HRV趋势数据，天数: $days")
                
                val trendData = hrvRepository.getHRVTrendData(patientId, days)
                
                _uiState.value = _uiState.value.copy(
                    trendData = trendData,
                    error = null
                )
                
                Log.d(TAG, "HRV趋势数据加载成功，共${trendData.size}条记录")
                
            } catch (e: Exception) {
                Log.e(TAG, "加载HRV趋势数据失败", e)
                _uiState.value = _uiState.value.copy(
                    error = "加载趋势数据失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 开始HRV测量
     */
    fun startHRVMeasurement(patientId: String = "default_patient") {
        viewModelScope.launch {
            try {
                Log.d(TAG, "开始HRV测量")
                
                // 开始测量会话
                currentSessionId = hrvRepository.startHRVMeasurement(patientId)
                Log.d(TAG, "HRV测量会话已开始: $currentSessionId")
                
                _uiState.value = _uiState.value.copy(isMeasuring = true, error = null)
                
                // 收集实时数据
                currentSessionId?.let { sessionId ->
                    hrvRepository.getRealtimeHRVData(sessionId)
                        .catch { e ->
                            Log.e(TAG, "实时HRV数据流异常", e)
                            _uiState.value = _uiState.value.copy(
                                isMeasuring = false,
                                error = "实时测量异常: ${e.message}"
                            )
                        }
                        .collect { data ->
                            _realtimeData.value = data
                            Log.d(TAG, "收到实时HRV数据: RMSSD=${data.currentRMSSD}, Progress=${data.measurementProgress}")
                            
                            // 测量完成
                            if (data.measurementProgress >= 1.0f) {
                                completeMeasurement()
                            }
                        }
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "启动HRV测量失败", e)
                _uiState.value = _uiState.value.copy(
                    isMeasuring = false,
                    error = "启动测量失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 停止HRV测量
     */
    fun stopHRVMeasurement() {
        viewModelScope.launch {
            try {
                Log.d(TAG, "停止HRV测量")
                
                currentSessionId?.let { sessionId ->
                    val result = hrvRepository.stopHRVMeasurement(sessionId)
                    Log.d(TAG, "HRV测量结果: $result")
                    
                    _uiState.value = _uiState.value.copy(
                        isMeasuring = false,
                        latestData = result
                    )
                }
                
                _realtimeData.value = null
                currentSessionId = null
                
                // 刷新统计数据
                loadHRVStats()
                
            } catch (e: Exception) {
                Log.e(TAG, "停止HRV测量失败", e)
                _uiState.value = _uiState.value.copy(
                    error = "停止测量失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 完成测量
     */
    private fun completeMeasurement() {
        viewModelScope.launch {
            try {
                Log.d(TAG, "HRV测量完成")
                
                currentSessionId?.let { sessionId ->
                    val result = hrvRepository.stopHRVMeasurement(sessionId)
                    
                    _uiState.value = _uiState.value.copy(
                        isMeasuring = false,
                        latestData = result,
                        measurementCompleted = true
                    )
                }
                
                _realtimeData.value = null
                currentSessionId = null
                
                // 刷新数据
                loadHRVStats()
                loadTrendData()
                
            } catch (e: Exception) {
                Log.e(TAG, "完成HRV测量时出错", e)
                _uiState.value = _uiState.value.copy(
                    isMeasuring = false,
                    error = "测量完成时出错: ${e.message}"
                )
            }
        }
    }

    /**
     * 获取历史HRV数据
     */
    fun loadHistoricalData(
        patientId: String = "default_patient",
        startTime: Long = System.currentTimeMillis() - 30 * 24 * 60 * 60 * 1000L, // 30天前
        endTime: Long = System.currentTimeMillis()
    ) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "加载历史HRV数据")
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                
                val historicalData = hrvRepository.getHistoricalHRVData(patientId, startTime, endTime)
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    historicalData = historicalData,
                    error = null
                )
                
                Log.d(TAG, "历史HRV数据加载成功，共${historicalData.size}条记录")
                
            } catch (e: Exception) {
                Log.e(TAG, "加载历史HRV数据失败", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "加载历史数据失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 清除错误状态
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    /**
     * 清除测量完成状态
     */
    fun clearMeasurementCompleted() {
        _uiState.value = _uiState.value.copy(measurementCompleted = false)
    }

    /**
     * 刷新数据
     */
    fun refresh() {
        Log.d(TAG, "刷新HRV数据")
        loadHRVStats()
        loadTrendData()
    }

    override fun onCleared() {
        super.onCleared()
        Log.d(TAG, "HRVViewModel清理")
        
        // 确保停止测量
        currentSessionId?.let {
            viewModelScope.launch {
                try {
                    hrvRepository.stopHRVMeasurement(it)
                } catch (e: Exception) {
                    Log.e(TAG, "清理时停止HRV测量失败", e)
                }
            }
        }
    }
}

/**
 * HRV UI状态
 */
data class HRVUiState(
    val isLoading: Boolean = false,
    val isMeasuring: Boolean = false,
    val measurementCompleted: Boolean = false,
    val stats: HRVStats? = null,
    val latestData: HRVData? = null,
    val trendData: List<HRVTrendData> = emptyList(),
    val historicalData: List<HRVData> = emptyList(),
    val error: String? = null
)
