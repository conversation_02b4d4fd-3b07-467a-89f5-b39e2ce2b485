package com.sdwu.kotlin.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.sdwu.kotlin.data.model.*
import com.sdwu.kotlin.data.repository.ECGRepository
import com.sdwu.kotlin.data.repository.ECGStats
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import android.util.Log

/**
 * ECG ViewModel
 * 管理心电图相关的UI状态和业务逻辑
 */
class ECGViewModel(
    private val ecgRepository: ECGRepository
) : ViewModel() {

    companion object {
        private const val TAG = "ECGViewModel"
    }

    // UI状态
    private val _uiState = MutableStateFlow(ECGUiState())
    val uiState: StateFlow<ECGUiState> = _uiState.asStateFlow()

    // 实时ECG数据
    private val _realtimeData = MutableStateFlow<ECGRealtimeData?>(null)
    val realtimeData: StateFlow<ECGRealtimeData?> = _realtimeData.asStateFlow()

    // 当前测量会话ID
    private var currentSessionId: String? = null

    init {
        Log.d(TAG, "ECGViewModel初始化")
        loadECGStats()
    }

    /**
     * 加载ECG统计数据
     */
    fun loadECGStats(patientId: String = "default_patient") {
        viewModelScope.launch {
            try {
                Log.d(TAG, "开始加载ECG统计数据")
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                
                val stats = ecgRepository.getECGStats(patientId)
                val latestData = ecgRepository.getLatestECGData(patientId)
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    stats = stats,
                    latestWaveformData = latestData,
                    error = null
                )
                
                Log.d(TAG, "ECG统计数据加载成功: $stats")
                
            } catch (e: Exception) {
                Log.e(TAG, "加载ECG统计数据失败", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "加载ECG数据失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 开始实时ECG监测
     */
    fun startRealtimeMonitoring(patientId: String = "default_patient") {
        viewModelScope.launch {
            try {
                Log.d(TAG, "开始实时ECG监测")
                
                // 开始测量会话
                currentSessionId = ecgRepository.startMeasurementSession(patientId)
                Log.d(TAG, "ECG测量会话已开始: $currentSessionId")
                
                _uiState.value = _uiState.value.copy(isMonitoring = true, error = null)
                
                // 收集实时数据
                currentSessionId?.let { sessionId ->
                    ecgRepository.getRealtimeECGData(sessionId)
                        .catch { e ->
                            Log.e(TAG, "实时ECG数据流异常", e)
                            _uiState.value = _uiState.value.copy(
                                isMonitoring = false,
                                error = "实时监测异常: ${e.message}"
                            )
                        }
                        .collect { data ->
                            _realtimeData.value = data
                            Log.d(TAG, "收到实时ECG数据: HR=${data.heartRate}, Quality=${data.signalQuality}")
                        }
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "启动实时ECG监测失败", e)
                _uiState.value = _uiState.value.copy(
                    isMonitoring = false,
                    error = "启动监测失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 停止实时ECG监测
     */
    fun stopRealtimeMonitoring() {
        viewModelScope.launch {
            try {
                Log.d(TAG, "停止实时ECG监测")
                
                currentSessionId?.let { sessionId ->
                    val success = ecgRepository.stopMeasurementSession(sessionId)
                    Log.d(TAG, "ECG测量会话停止结果: $success")
                }
                
                _uiState.value = _uiState.value.copy(isMonitoring = false)
                _realtimeData.value = null
                currentSessionId = null
                
            } catch (e: Exception) {
                Log.e(TAG, "停止实时ECG监测失败", e)
                _uiState.value = _uiState.value.copy(
                    error = "停止监测失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 获取历史ECG数据
     */
    fun loadHistoricalData(
        patientId: String = "default_patient",
        startTime: Long = System.currentTimeMillis() - 7 * 24 * 60 * 60 * 1000L, // 7天前
        endTime: Long = System.currentTimeMillis()
    ) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "加载历史ECG数据")
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                
                val historicalData = ecgRepository.getHistoricalECGData(patientId, startTime, endTime)
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    historicalData = historicalData,
                    error = null
                )
                
                Log.d(TAG, "历史ECG数据加载成功，共${historicalData.size}条记录")
                
            } catch (e: Exception) {
                Log.e(TAG, "加载历史ECG数据失败", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "加载历史数据失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 获取ECG分析结果
     */
    fun getAnalysisResult(waveformId: String) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "获取ECG分析结果: $waveformId")
                
                val analysisResult = ecgRepository.getAnalysisResult(waveformId)
                
                _uiState.value = _uiState.value.copy(
                    analysisResult = analysisResult,
                    error = null
                )
                
                Log.d(TAG, "ECG分析结果获取成功: $analysisResult")
                
            } catch (e: Exception) {
                Log.e(TAG, "获取ECG分析结果失败", e)
                _uiState.value = _uiState.value.copy(
                    error = "获取分析结果失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 清除错误状态
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    /**
     * 刷新数据
     */
    fun refresh() {
        Log.d(TAG, "刷新ECG数据")
        loadECGStats()
    }

    override fun onCleared() {
        super.onCleared()
        Log.d(TAG, "ECGViewModel清理")
        
        // 确保停止监测
        currentSessionId?.let {
            viewModelScope.launch {
                try {
                    ecgRepository.stopMeasurementSession(it)
                } catch (e: Exception) {
                    Log.e(TAG, "清理时停止ECG会话失败", e)
                }
            }
        }
    }
}

/**
 * ECG UI状态
 */
data class ECGUiState(
    val isLoading: Boolean = false,
    val isMonitoring: Boolean = false,
    val stats: ECGStats? = null,
    val latestWaveformData: ECGWaveformData? = null,
    val historicalData: List<ECGWaveformData> = emptyList(),
    val analysisResult: ECGAnalysisResult? = null,
    val error: String? = null
)
