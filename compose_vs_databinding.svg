<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义渐变色 -->
    <linearGradient id="composeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6200EA;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3700B3;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="dataBindingGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2E7D32;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="sharedGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF9800;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F57C00;stop-opacity:1" />
    </linearGradient>
    
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
            refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#00000030"/>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="1400" height="1000" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="700" y="30" text-anchor="middle" font-family="Arial, sans-serif" 
        font-size="26" font-weight="bold" fill="#333">
    Jetpack Compose vs DataBinding 架构对比
  </text>
  
  <!-- Jetpack Compose 流程 (左侧) -->
  <g id="compose-flow">
    <!-- 标题 -->
    <rect x="50" y="60" width="600" height="45" fill="url(#composeGradient)" rx="8" filter="url(#shadow)"/>
    <text x="350" y="90" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="20" font-weight="bold" fill="white">
      🚀 Jetpack Compose (声明式UI)
    </text>
    
    <!-- 1. 架构特点 -->
    <rect x="70" y="125" width="560" height="90" fill="#F3E5F5" stroke="#6200EA" stroke-width="2" rx="8"/>
    <text x="90" y="150" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#3700B3">
      1. 架构特点
    </text>
    <text x="90" y="170" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • 纯Kotlin代码，无需XML布局文件
    </text>
    <text x="90" y="185" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • 声明式UI：描述UI应该是什么样子，而不是如何构建
    </text>
    <text x="90" y="200" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • 函数式编程：@Composable函数组合构建UI
    </text>
    
    <!-- 2. 状态管理 -->
    <rect x="70" y="235" width="560" height="90" fill="#F3E5F5" stroke="#6200EA" stroke-width="2" rx="8"/>
    <text x="90" y="260" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#3700B3">
      2. 状态管理
    </text>
    <text x="90" y="280" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • State/MutableState：本地状态管理
    </text>
    <text x="90" y="295" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • StateFlow.collectAsState()：观察ViewModel状态
    </text>
    <text x="90" y="310" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • remember：跨重组保持状态
    </text>
    
    <!-- 3. 代码示例 -->
    <rect x="70" y="345" width="560" height="120" fill="#F3E5F5" stroke="#6200EA" stroke-width="2" rx="8"/>
    <text x="90" y="370" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#3700B3">
      3. 代码示例
    </text>
    <text x="90" y="390" font-family="Courier New, monospace" font-size="11" fill="#333">
      @Composable
    </text>
    <text x="90" y="405" font-family="Courier New, monospace" font-size="11" fill="#333">
      fun UserProfile(viewModel: ProfileViewModel) {
    </text>
    <text x="90" y="420" font-family="Courier New, monospace" font-size="11" fill="#333">
          val uiState by viewModel.uiState.collectAsState()
    </text>
    <text x="90" y="435" font-family="Courier New, monospace" font-size="11" fill="#333">
          Text(text = uiState.user?.name ?: "")
    </text>
    <text x="90" y="450" font-family="Courier New, monospace" font-size="11" fill="#333">
      }
    </text>
    
    <!-- 4. 优势 -->
    <rect x="70" y="485" width="560" height="105" fill="#F3E5F5" stroke="#6200EA" stroke-width="2" rx="8"/>
    <text x="90" y="510" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#3700B3">
      4. 核心优势
    </text>
    <text x="90" y="530" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • 🎨 强大的动画系统和自定义绘制能力
    </text>
    <text x="90" y="545" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • ⚡ 智能重组：只更新变化的UI部分
    </text>
    <text x="90" y="560" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • 🔧 类型安全：编译时检查，运行时稳定
    </text>
    <text x="90" y="575" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • 🚀 现代化开发体验，Google官方推荐
    </text>
  </g>
  
  <!-- DataBinding 流程 (右侧) -->
  <g id="databinding-flow">
    <!-- 标题 -->
    <rect x="750" y="60" width="600" height="45" fill="url(#dataBindingGradient)" rx="8" filter="url(#shadow)"/>
    <text x="1050" y="90" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="20" font-weight="bold" fill="white">
      📋 DataBinding (XML + 表达式绑定)
    </text>
    
    <!-- 1. 架构特点 -->
    <rect x="770" y="125" width="560" height="90" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2" rx="8"/>
    <text x="790" y="150" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2E7D32">
      1. 架构特点
    </text>
    <text x="790" y="170" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • XML布局 + Kotlin代码的混合模式
    </text>
    <text x="790" y="185" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • 命令式UI：通过绑定表达式连接数据和视图
    </text>
    <text x="790" y="200" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • 基于传统View系统的增强版本
    </text>
    
    <!-- 2. 状态管理 -->
    <rect x="770" y="235" width="560" height="90" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2" rx="8"/>
    <text x="790" y="260" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2E7D32">
      2. 状态管理
    </text>
    <text x="790" y="280" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • LiveData：自动观察数据变化
    </text>
    <text x="790" y="295" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • Observable Fields：双向数据绑定
    </text>
    <text x="790" y="310" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • lifecycleOwner：生命周期感知
    </text>
    
    <!-- 3. 代码示例 -->
    <rect x="770" y="345" width="560" height="120" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2" rx="8"/>
    <text x="790" y="370" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2E7D32">
      3. 代码示例
    </text>
    <text x="790" y="390" font-family="Courier New, monospace" font-size="11" fill="#333">
      &lt;TextView android:text="@{viewModel.user.name}"
    </text>
    <text x="790" y="405" font-family="Courier New, monospace" font-size="11" fill="#333">
                android:visibility="@{viewModel.isLoading
    </text>
    <text x="790" y="420" font-family="Courier New, monospace" font-size="11" fill="#333">
                ? View.VISIBLE : View.GONE}" /&gt;
    </text>
    <text x="790" y="435" font-family="Courier New, monospace" font-size="11" fill="#333">
      // Kotlin: binding.viewModel = viewModel
    </text>
    <text x="790" y="450" font-family="Courier New, monospace" font-size="11" fill="#333">
      // binding.lifecycleOwner = this
    </text>
    
    <!-- 4. 优势 -->
    <rect x="770" y="485" width="560" height="105" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2" rx="8"/>
    <text x="790" y="510" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2E7D32">
      4. 核心优势
    </text>
    <text x="790" y="530" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • 📝 自动数据绑定：无需手动更新UI
    </text>
    <text x="790" y="545" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • 🔄 双向绑定：表单处理更简单
    </text>
    <text x="790" y="560" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • 🏗️ 渐进式迁移：可与现有View系统共存
    </text>
    <text x="790" y="575" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • 📚 学习成本低：基于熟悉的XML布局
    </text>
  </g>
  
  <!-- 详细对比表格 -->
  <g id="comparison-table">
    <rect x="100" y="620" width="1200" height="280" fill="#FFFFFF" stroke="#DDD" stroke-width="2" rx="8" filter="url(#shadow)"/>
    <text x="700" y="650" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="18" font-weight="bold" fill="#333">
      📊 详细特性对比
    </text>
    
    <!-- 表格头部 -->
    <rect x="120" y="665" width="200" height="35" fill="#F5F5F5" stroke="#DDD"/>
    <rect x="320" y="665" width="400" height="35" fill="#F3E5F5" stroke="#DDD"/>
    <rect x="720" y="665" width="400" height="35" fill="#E8F5E8" stroke="#DDD"/>
    
    <text x="220" y="687" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#333">特性</text>
    <text x="520" y="687" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#6200EA">Jetpack Compose</text>
    <text x="920" y="687" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#4CAF50">DataBinding</text>
    
    <!-- 表格内容 -->
    <g id="table-rows">
      <!-- 第1行：UI构建方式 -->
      <rect x="120" y="700" width="200" height="30" fill="#FAFAFA" stroke="#DDD"/>
      <rect x="320" y="700" width="400" height="30" fill="#FAFAFA" stroke="#DDD"/>
      <rect x="720" y="700" width="400" height="30" fill="#FAFAFA" stroke="#DDD"/>
      <text x="130" y="720" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">UI构建方式</text>
      <text x="330" y="720" font-family="Arial, sans-serif" font-size="12" fill="#333">纯Kotlin @Composable函数</text>
      <text x="730" y="720" font-family="Arial, sans-serif" font-size="12" fill="#333">XML布局 + 绑定表达式</text>
      
      <!-- 第2行：状态管理 -->
      <rect x="120" y="730" width="200" height="30" fill="#FFFFFF" stroke="#DDD"/>
      <rect x="320" y="730" width="400" height="30" fill="#FFFFFF" stroke="#DDD"/>
      <rect x="720" y="730" width="400" height="30" fill="#FFFFFF" stroke="#DDD"/>
      <text x="130" y="750" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">状态管理</text>
      <text x="330" y="750" font-family="Arial, sans-serif" font-size="12" fill="#333">State/StateFlow + collectAsState()</text>
      <text x="730" y="750" font-family="Arial, sans-serif" font-size="12" fill="#333">LiveData + Observable Fields</text>
      
      <!-- 第3行：性能 -->
      <rect x="120" y="760" width="200" height="30" fill="#FAFAFA" stroke="#DDD"/>
      <rect x="320" y="760" width="400" height="30" fill="#FAFAFA" stroke="#DDD"/>
      <rect x="720" y="760" width="400" height="30" fill="#FAFAFA" stroke="#DDD"/>
      <text x="130" y="780" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">性能</text>
      <text x="330" y="780" font-family="Arial, sans-serif" font-size="12" fill="#333">⭐⭐⭐⭐⭐ 智能重组，极致优化</text>
      <text x="730" y="780" font-family="Arial, sans-serif" font-size="12" fill="#333">⭐⭐⭐⭐ 编译时优化，性能良好</text>
      
      <!-- 第4行：学习曲线 -->
      <rect x="120" y="790" width="200" height="30" fill="#FFFFFF" stroke="#DDD"/>
      <rect x="320" y="790" width="400" height="30" fill="#FFFFFF" stroke="#DDD"/>
      <rect x="720" y="790" width="400" height="30" fill="#FFFFFF" stroke="#DDD"/>
      <text x="130" y="810" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">学习曲线</text>
      <text x="330" y="810" font-family="Arial, sans-serif" font-size="12" fill="#333">⭐⭐⭐ 需要学习新的编程范式</text>
      <text x="730" y="810" font-family="Arial, sans-serif" font-size="12" fill="#333">⭐⭐ 基于熟悉的XML，容易上手</text>
      
      <!-- 第5行：动画支持 -->
      <rect x="120" y="820" width="200" height="30" fill="#FAFAFA" stroke="#DDD"/>
      <rect x="320" y="820" width="400" height="30" fill="#FAFAFA" stroke="#DDD"/>
      <rect x="720" y="820" width="400" height="30" fill="#FAFAFA" stroke="#DDD"/>
      <text x="130" y="840" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">动画支持</text>
      <text x="330" y="840" font-family="Arial, sans-serif" font-size="12" fill="#333">⭐⭐⭐⭐⭐ 强大的动画API</text>
      <text x="730" y="840" font-family="Arial, sans-serif" font-size="12" fill="#333">⭐⭐ 有限的动画支持</text>
      
      <!-- 第6行：调试难度 -->
      <rect x="120" y="850" width="200" height="30" fill="#FFFFFF" stroke="#DDD"/>
      <rect x="320" y="850" width="400" height="30" fill="#FFFFFF" stroke="#DDD"/>
      <rect x="720" y="850" width="400" height="30" fill="#FFFFFF" stroke="#DDD"/>
      <text x="130" y="870" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">调试难度</text>
      <text x="330" y="870" font-family="Arial, sans-serif" font-size="12" fill="#333">⭐⭐ 纯Kotlin，调试工具完善</text>
      <text x="730" y="870" font-family="Arial, sans-serif" font-size="12" fill="#333">⭐⭐⭐ XML表达式调试较困难</text>
    </g>
  </g>
  
  <!-- 使用场景推荐 -->
  <g id="recommendations">
    <rect x="100" y="920" width="580" height="70" fill="#E3F2FD" stroke="#2196F3" stroke-width="2" rx="8"/>
    <text x="390" y="945" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="16" font-weight="bold" fill="#1565C0">
      🎯 Compose 适用场景
    </text>
    <text x="120" y="965" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • 新项目开发 • 复杂动画需求 • 动态UI构建 • 现代化开发体验
    </text>
    <text x="120" y="980" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • 跨平台需求 • 高性能要求 • 团队技术栈升级
    </text>
    
    <rect x="720" y="920" width="580" height="70" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2" rx="8"/>
    <text x="1010" y="945" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="16" font-weight="bold" fill="#2E7D32">
      🎯 DataBinding 适用场景
    </text>
    <text x="740" y="965" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • 现有项目维护 • 复杂表单处理 • 团队熟悉XML • 渐进式迁移
    </text>
    <text x="740" y="980" font-family="Arial, sans-serif" font-size="13" fill="#333">
      • 双向数据绑定需求 • 与传统View系统集成
    </text>
  </g>
  
  <!-- 对比箭头 -->
  <line x1="650" y1="350" x2="750" y2="350" stroke="#666" stroke-width="3" stroke-dasharray="8,4"/>
  <text x="700" y="345" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#666">
    VS
  </text>
  
  <!-- 流程箭头 -->
  <g id="flow-arrows">
    <!-- Compose 流程箭头 -->
    <line x1="350" y1="215" x2="350" y2="230" stroke="#6200EA" stroke-width="3" marker-end="url(#arrowhead)"/>
    <line x1="350" y1="325" x2="350" y2="340" stroke="#6200EA" stroke-width="3" marker-end="url(#arrowhead)"/>
    <line x1="350" y1="465" x2="350" y2="480" stroke="#6200EA" stroke-width="3" marker-end="url(#arrowhead)"/>
    
    <!-- DataBinding 流程箭头 -->
    <line x1="1050" y1="215" x2="1050" y2="230" stroke="#4CAF50" stroke-width="3" marker-end="url(#arrowhead)"/>
    <line x1="1050" y1="325" x2="1050" y2="340" stroke="#4CAF50" stroke-width="3" marker-end="url(#arrowhead)"/>
    <line x1="1050" y1="465" x2="1050" y2="480" stroke="#4CAF50" stroke-width="3" marker-end="url(#arrowhead)"/>
  </g>
</svg>
