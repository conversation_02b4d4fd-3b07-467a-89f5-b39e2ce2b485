package com.sdwu.kotlin;

/**
 * 应用程序类
 * 初始化全局依赖和配置
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0007\b\u0007\u0018\u0000 \u00112\u00020\u0001:\u0001\u0011B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\n\u001a\u00020\u000bH\u0002J\b\u0010\f\u001a\u00020\u000bH\u0002J\b\u0010\r\u001a\u00020\u000bH\u0002J\u0006\u0010\u000e\u001a\u00020\tJ\b\u0010\u000f\u001a\u00020\u000bH\u0016J\b\u0010\u0010\u001a\u00020\u000bH\u0016R\u001e\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0004@BX\u0086.\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007R\u000e\u0010\b\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0012"}, d2 = {"Lcom/sdwu/kotlin/KotlinApplication;", "Landroid/app/Application;", "()V", "<set-?>", "Lcom/sdwu/kotlin/di/AppContainer;", "appContainer", "getAppContainer", "()Lcom/sdwu/kotlin/di/AppContainer;", "isInitialized", "", "initializeAppContainer", "", "initializeApplication", "initializeCrashHandler", "isApplicationInitialized", "onCreate", "onTerminate", "Companion", "app_debug"})
public final class KotlinApplication extends android.app.Application {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "KotlinApplication";
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.sdwu.kotlin.KotlinApplication instance;
    private com.sdwu.kotlin.di.AppContainer appContainer;
    private boolean isInitialized = false;
    @org.jetbrains.annotations.NotNull()
    public static final com.sdwu.kotlin.KotlinApplication.Companion Companion = null;
    
    public KotlinApplication() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.sdwu.kotlin.di.AppContainer getAppContainer() {
        return null;
    }
    
    @java.lang.Override()
    public void onCreate() {
    }
    
    /**
     * 初始化应用程序组件
     */
    private final void initializeApplication() {
    }
    
    /**
     * 初始化崩溃处理器
     */
    private final void initializeCrashHandler() {
    }
    
    /**
     * 初始化依赖注入容器
     */
    private final void initializeAppContainer() {
    }
    
    /**
     * 检查应用程序是否已正确初始化
     */
    public final boolean isApplicationInitialized() {
        return false;
    }
    
    @java.lang.Override()
    public void onTerminate() {
    }
    
    @kotlin.Metadata(mv = {1, 8, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0007\u001a\u0004\u0018\u00010\u0006R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0005\u001a\u0004\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\b"}, d2 = {"Lcom/sdwu/kotlin/KotlinApplication$Companion;", "", "()V", "TAG", "", "instance", "Lcom/sdwu/kotlin/KotlinApplication;", "getInstance", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        /**
         * 获取应用程序实例
         */
        @org.jetbrains.annotations.Nullable()
        public final com.sdwu.kotlin.KotlinApplication getInstance() {
            return null;
        }
    }
}