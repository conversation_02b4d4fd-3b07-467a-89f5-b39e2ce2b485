package com.sdwu.kotlin.utils

import android.util.Log
import androidx.compose.runtime.*
import androidx.navigation.NavController

/**
 * Compose专用的导航辅助工具
 * 提供与Compose兼容的安全导航功能
 */
object ComposeNavigationHelper {
    
    private const val TAG = "ComposeNavigationHelper"
    
    /**
     * 在Compose中安全地执行导航操作
     * @param navController 导航控制器
     * @param route 目标路由
     * @param from 来源页面
     * @param onSuccess 导航成功回调
     * @param onError 导航失败回调
     */
    @Composable
    fun SafeNavigate(
        navController: NavController,
        route: String,
        from: String = "unknown",
        onSuccess: (() -> Unit)? = null,
        onError: ((String) -> Unit)? = null
    ) {
        LaunchedEffect(route) {
            try {
                Log.d(TAG, "开始导航: $from -> $route")
                ErrorLogger.logNavigation(TAG, from, route)
                
                val success = NavigationErrorHandler.safeNavigateTo(
                    navController = navController,
                    route = route,
                    from = from
                )
                
                if (success) {
                    Log.d(TAG, "导航成功: $from -> $route")
                    onSuccess?.invoke()
                } else {
                    val errorMsg = "导航失败: $from -> $route"
                    Log.e(TAG, errorMsg)
                    onError?.invoke(errorMsg)
                }
                
            } catch (e: Exception) {
                val errorMsg = "导航异常: $from -> $route, ${e.message}"
                Log.e(TAG, errorMsg, e)
                ErrorLogger.logError(TAG, errorMsg, e)
                onError?.invoke(errorMsg)
            }
        }
    }
    
    /**
     * 在Compose中安全地执行返回操作
     * @param navController 导航控制器
     * @param from 来源页面
     * @param onSuccess 返回成功回调
     * @param onError 返回失败回调
     */
    @Composable
    fun SafePopBack(
        navController: NavController,
        from: String = "unknown",
        onSuccess: (() -> Unit)? = null,
        onError: ((String) -> Unit)? = null
    ) {
        LaunchedEffect(Unit) {
            try {
                Log.d(TAG, "开始返回: $from")
                ErrorLogger.logNavigation(TAG, from, "back")
                
                val success = NavigationErrorHandler.safePopBackStack(navController, from)
                
                if (success) {
                    Log.d(TAG, "返回成功: $from")
                    onSuccess?.invoke()
                } else {
                    val errorMsg = "返回失败: $from"
                    Log.w(TAG, errorMsg)
                    onError?.invoke(errorMsg)
                }
                
            } catch (e: Exception) {
                val errorMsg = "返回异常: $from, ${e.message}"
                Log.e(TAG, errorMsg, e)
                ErrorLogger.logError(TAG, errorMsg, e)
                onError?.invoke(errorMsg)
            }
        }
    }
    
    /**
     * 监控导航状态变化
     * @param navController 导航控制器
     * @param tag 日志标签
     */
    @Composable
    fun MonitorNavigationState(
        navController: NavController,
        tag: String = "NavigationMonitor"
    ) {
        val currentDestination = navController.currentDestination
        
        LaunchedEffect(currentDestination) {
            try {
                val route = currentDestination?.route ?: "unknown"
                Log.d(tag, "导航状态变化: 当前路由 = $route")
                ErrorLogger.logInfo(tag, "导航到: $route")
                
                // 生成导航状态报告
                val stateReport = NavigationErrorHandler.getCurrentNavigationState(navController)
                Log.d(tag, "导航状态: $stateReport")
                
            } catch (e: Exception) {
                Log.e(tag, "导航状态监控失败", e)
                ErrorLogger.logError(tag, "导航状态监控失败", e)
            }
        }
    }
    
    /**
     * 监控页面加载
     * @param pageName 页面名称
     * @param onLoadStart 加载开始回调
     * @param onLoadComplete 加载完成回调
     * @param onLoadError 加载错误回调
     */
    @Composable
    fun MonitorPageLoad(
        pageName: String,
        onLoadStart: (() -> Unit)? = null,
        onLoadComplete: (() -> Unit)? = null,
        onLoadError: ((Exception) -> Unit)? = null
    ) {
        LaunchedEffect(Unit) {
            try {
                Log.d(TAG, "页面开始加载: $pageName")
                ErrorLogger.logInfo(TAG, "页面加载开始: $pageName")
                onLoadStart?.invoke()
                
                // 模拟页面加载完成
                kotlinx.coroutines.delay(100)
                
                Log.d(TAG, "页面加载完成: $pageName")
                ErrorLogger.logInfo(TAG, "页面加载完成: $pageName")
                onLoadComplete?.invoke()
                
            } catch (e: Exception) {
                Log.e(TAG, "页面加载失败: $pageName", e)
                ErrorLogger.logError(TAG, "页面加载失败: $pageName", e)
                onLoadError?.invoke(e)
            }
        }
    }
    
    /**
     * 创建导航状态
     * @param initialRoute 初始路由
     * @return 导航状态
     */
    @Composable
    fun rememberNavigationState(initialRoute: String = "home"): NavigationState {
        return remember {
            NavigationState(
                currentRoute = initialRoute,
                isNavigating = false,
                lastError = null
            )
        }
    }
    
    /**
     * 导航状态数据类
     */
    data class NavigationState(
        var currentRoute: String,
        var isNavigating: Boolean,
        var lastError: String?
    )
    
    /**
     * 执行带状态的导航
     * @param navigationState 导航状态
     * @param navController 导航控制器
     * @param route 目标路由
     * @param from 来源页面
     */
    fun navigateWithState(
        navigationState: NavigationState,
        navController: NavController,
        route: String,
        from: String = "unknown"
    ) {
        if (navigationState.isNavigating) {
            Log.w(TAG, "导航正在进行中，忽略新的导航请求")
            return
        }
        
        navigationState.isNavigating = true
        navigationState.lastError = null
        
        val success = NavigationErrorHandler.safeNavigateTo(
            navController = navController,
            route = route,
            from = from
        )
        
        if (success) {
            navigationState.currentRoute = route
            Log.d(TAG, "导航成功，更新状态: $route")
        } else {
            navigationState.lastError = "导航失败: $from -> $route"
            Log.e(TAG, "导航失败: $from -> $route")
        }
        
        navigationState.isNavigating = false
    }
    
    /**
     * 检查导航是否安全
     * @param navController 导航控制器
     * @param route 目标路由
     * @return 是否安全
     */
    fun isNavigationSafe(navController: NavController, route: String): Boolean {
        return try {
            // 检查NavController状态
            val graph = navController.graph
            val currentDestination = navController.currentDestination
            
            // 检查路由是否存在
            val destination = graph.findNode(route)
            destination != null
            
        } catch (e: Exception) {
            Log.e(TAG, "导航安全检查失败", e)
            false
        }
    }
}
