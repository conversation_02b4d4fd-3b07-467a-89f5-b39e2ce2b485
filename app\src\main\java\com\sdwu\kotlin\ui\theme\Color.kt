package com.sdwu.kotlin.ui.theme

import androidx.compose.ui.graphics.Color

// 粉色主题色彩
val Pink80 = Color(0xFFFFB3E6)  // 浅粉色
val PinkGrey80 = Color(0xFFE8C2DC)  // 粉灰色
val Rose80 = Color(0xFFFFCCE5)  // 玫瑰粉

val Pink40 = Color(0xFFE91E63)  // 主粉色
val PinkGrey40 = Color(0xFFAD1457)  // 深粉色
val Rose40 = Color(0xFFC2185B)  // 深玫瑰粉

// HRV指标相关颜色
val HRVPrimary = Color(0xFFE91E63)  // 主要粉色
val HRVSecondary = Color(0xFFFF4081)  // 次要粉色
val HRVAccent = Color(0xFFFF80AB)  // 强调粉色
val HRVBackground = Color(0xFFFCE4EC)  // 背景粉色
val HRVSurface = Color(0xFFF8BBD9)  // 表面粉色

// 渐变色
val PinkGradientStart = Color(0xFFFF6EC7)
val PinkGradientEnd = Color(0xFFFF9A9E)