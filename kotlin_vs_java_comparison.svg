<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="2400" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="1600" height="2400" fill="#f8f9fa"/>

  <!-- Title -->
  <text x="800" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="#2c3e50">
    <PERSON><PERSON><PERSON> vs Java 全面对比 - 详细代码示例
  </text>
  
  <!-- Example 1: Null Safety -->
  <g id="example1">
    <text x="50" y="80" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#2c3e50">
      1. 空安全 (Null Safety)
    </text>

    <!-- Kotlin Code -->
    <rect x="50" y="100" width="750" height="140" fill="#e8f4fd" stroke="#7f39fb" stroke-width="2" rx="5"/>
    <text x="60" y="120" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#7f39fb">Kotlin:</text>
    <text x="60" y="140" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">var name: String? = null  // 可空类型</text>
    <text x="60" y="155" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">var nonNullName: String = "张三"  // 非空类型</text>
    <text x="60" y="170" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">println(name?.length ?: "名字为空")  // 安全调用</text>
    <text x="60" y="185" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">name?.let { println("名字: $it") }  // let函数</text>
    <text x="60" y="200" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">val length = name!!.length  // 强制解包(不推荐)</text>
    <text x="60" y="215" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">// 编译器强制处理null情况</text>
    <text x="60" y="230" font-family="Arial, sans-serif" font-size="11" fill="#27ae60">✓ 编译时安全检查，避免NPE</text>

    <!-- Java Code -->
    <rect x="820" y="100" width="750" height="140" fill="#fff8e1" stroke="#ed8b00" stroke-width="2" rx="5"/>
    <text x="830" y="120" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#ed8b00">Java:</text>
    <text x="830" y="140" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">String name = null;  // 可能为null</text>
    <text x="830" y="155" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">String nonNullName = "张三";</text>
    <text x="830" y="170" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">if (name != null) {  // 手动null检查</text>
    <text x="830" y="185" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    System.out.println("名字长度: " + name.length());</text>
    <text x="830" y="200" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">} else {</text>
    <text x="830" y="215" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    System.out.println("名字为空");</text>
    <text x="830" y="230" font-family="Arial, sans-serif" font-size="11" fill="#e74c3c">⚠ 运行时NullPointerException风险</text>
  </g>

  <!-- Example 2: Data Classes -->
  <g id="example2">
    <text x="50" y="280" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#2c3e50">
      2. 数据类 (Data Classes) 和 POJO
    </text>

    <!-- Kotlin Code -->
    <rect x="50" y="300" width="750" height="160" fill="#e8f4fd" stroke="#7f39fb" stroke-width="2" rx="5"/>
    <text x="60" y="320" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#7f39fb">Kotlin:</text>
    <text x="60" y="340" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">data class User(val name: String, val age: Int, val email: String)</text>
    <text x="60" y="355" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">// 自动生成: equals(), hashCode(), toString(), copy(), componentN()</text>
    <text x="60" y="370" font-family="Courier New, monospace" font-size="11" fill="#2c3e50"></text>
    <text x="60" y="385" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">val user = User("张三", 25, "<EMAIL>")</text>
    <text x="60" y="400" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">val olderUser = user.copy(age = 26)  // 不可变复制</text>
    <text x="60" y="415" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">val (name, age) = user  // 解构声明</text>
    <text x="60" y="430" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">println(user)  // User(name=张三, age=25, email=<EMAIL>)</text>
    <text x="60" y="445" font-family="Arial, sans-serif" font-size="11" fill="#27ae60">✓ 1行代码，功能完整</text>

    <!-- Java Code -->
    <rect x="820" y="300" width="750" height="160" fill="#fff8e1" stroke="#ed8b00" stroke-width="2" rx="5"/>
    <text x="830" y="320" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#ed8b00">Java:</text>
    <text x="830" y="340" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">public class User {</text>
    <text x="830" y="355" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    private String name, email;</text>
    <text x="830" y="370" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    private int age;</text>
    <text x="830" y="385" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    // 构造函数(5行) + getter/setter(18行)</text>
    <text x="830" y="400" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    // + equals(15行) + hashCode(8行) + toString(5行)</text>
    <text x="830" y="415" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    // + copy方法(10行) = 总共60+行代码</text>
    <text x="830" y="430" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">}</text>
    <text x="830" y="445" font-family="Arial, sans-serif" font-size="11" fill="#e74c3c">⚠ 大量样板代码</text>
  </g>
  
  <!-- Example 3: Extension Functions -->
  <g id="example3">
    <text x="50" y="500" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#2c3e50">
      3. 扩展函数 (Extension Functions) 和工具类
    </text>

    <!-- Kotlin Code -->
    <rect x="50" y="520" width="750" height="140" fill="#e8f4fd" stroke="#7f39fb" stroke-width="2" rx="5"/>
    <text x="60" y="540" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#7f39fb">Kotlin:</text>
    <text x="60" y="560" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">fun String.isEmail() = contains("@") && contains(".")</text>
    <text x="60" y="575" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">fun String.capitalize() = replaceFirstChar { it.uppercase() }</text>
    <text x="60" y="590" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">fun List&lt;Int&gt;.average() = sum().toDouble() / size</text>
    <text x="60" y="605" font-family="Courier New, monospace" font-size="11" fill="#2c3e50"></text>
    <text x="60" y="620" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">// 使用方式：</text>
    <text x="60" y="635" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">"<EMAIL>".isEmail()  // true</text>
    <text x="60" y="650" font-family="Arial, sans-serif" font-size="11" fill="#27ae60">✓ 面向对象的调用方式</text>

    <!-- Java Code -->
    <rect x="820" y="520" width="750" height="140" fill="#fff8e1" stroke="#ed8b00" stroke-width="2" rx="5"/>
    <text x="830" y="540" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#ed8b00">Java:</text>
    <text x="830" y="560" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">public class StringUtils {</text>
    <text x="830" y="575" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    public static boolean isEmail(String str) {</text>
    <text x="830" y="590" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">        return str.contains("@") && str.contains(".");</text>
    <text x="830" y="605" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    }</text>
    <text x="830" y="620" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    // 其他工具方法...</text>
    <text x="830" y="635" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">} // StringUtils.isEmail("<EMAIL>")</text>
    <text x="830" y="650" font-family="Arial, sans-serif" font-size="11" fill="#e74c3c">⚠ 静态方法调用，不够直观</text>
  </g>

  <!-- Example 4: String Templates and Formatting -->
  <g id="example4">
    <text x="50" y="700" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#2c3e50">
      4. 字符串模板和格式化
    </text>

    <!-- Kotlin Code -->
    <rect x="50" y="720" width="750" height="120" fill="#e8f4fd" stroke="#7f39fb" stroke-width="2" rx="5"/>
    <text x="60" y="740" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#7f39fb">Kotlin:</text>
    <text x="60" y="760" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">val name = "张三"</text>
    <text x="60" y="775" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">val age = 25</text>
    <text x="60" y="790" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">val score = 95.5</text>
    <text x="60" y="805" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">println("我是$name，今年${age}岁，成绩${score}分")</text>
    <text x="60" y="820" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">println("明年我${age + 1}岁")  // 表达式计算</text>
    <text x="60" y="835" font-family="Arial, sans-serif" font-size="11" fill="#27ae60">✓ 简洁直观，支持表达式</text>

    <!-- Java Code -->
    <rect x="820" y="720" width="750" height="120" fill="#fff8e1" stroke="#ed8b00" stroke-width="2" rx="5"/>
    <text x="830" y="740" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#ed8b00">Java:</text>
    <text x="830" y="760" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">String name = "张三";</text>
    <text x="830" y="775" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">int age = 25;</text>
    <text x="830" y="790" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">double score = 95.5;</text>
    <text x="830" y="805" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">System.out.println("我是" + name + "，今年" + age + "岁，成绩" + score + "分");</text>
    <text x="830" y="820" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">// 或使用 String.format("我是%s，今年%d岁", name, age);</text>
    <text x="830" y="835" font-family="Arial, sans-serif" font-size="11" fill="#e74c3c">⚠ 字符串拼接冗长，易出错</text>
  </g>
  
  <!-- Example 5: When Expression vs Switch -->
  <g id="example5">
    <text x="50" y="880" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#2c3e50">
      5. When表达式 vs Switch/If-else
    </text>

    <!-- Kotlin Code -->
    <rect x="50" y="900" width="750" height="160" fill="#e8f4fd" stroke="#7f39fb" stroke-width="2" rx="5"/>
    <text x="60" y="920" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#7f39fb">Kotlin:</text>
    <text x="60" y="940" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">fun getGrade(score: Int) = when {</text>
    <text x="60" y="955" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    score >= 90 -> "优秀"</text>
    <text x="60" y="970" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    score in 80..89 -> "良好"</text>
    <text x="60" y="985" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    score in 60..79 -> "及格"</text>
    <text x="60" y="1000" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    else -> "不及格"</text>
    <text x="60" y="1015" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">}</text>
    <text x="60" y="1030" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">// 支持范围、类型检查、任意表达式</text>
    <text x="60" y="1045" font-family="Arial, sans-serif" font-size="11" fill="#27ae60">✓ 表达式，更强大灵活</text>

    <!-- Java Code -->
    <rect x="820" y="900" width="750" height="160" fill="#fff8e1" stroke="#ed8b00" stroke-width="2" rx="5"/>
    <text x="830" y="920" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#ed8b00">Java:</text>
    <text x="830" y="940" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">public String getGrade(int score) {</text>
    <text x="830" y="955" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    if (score >= 90) return "优秀";</text>
    <text x="830" y="970" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    else if (score >= 80 && score <= 89) return "良好";</text>
    <text x="830" y="985" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    else if (score >= 60 && score <= 79) return "及格";</text>
    <text x="830" y="1000" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    else return "不及格";</text>
    <text x="830" y="1015" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">}</text>
    <text x="830" y="1030" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">// switch只支持常量，if-else冗长</text>
    <text x="830" y="1045" font-family="Arial, sans-serif" font-size="11" fill="#e74c3c">⚠ 语句，功能有限</text>
  </g>

  <!-- Example 6: Lambda and Higher-order Functions -->
  <g id="example6">
    <text x="50" y="1100" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#2c3e50">
      6. Lambda表达式和高阶函数
    </text>

    <!-- Kotlin Code -->
    <rect x="50" y="1120" width="750" height="140" fill="#e8f4fd" stroke="#7f39fb" stroke-width="2" rx="5"/>
    <text x="60" y="1140" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#7f39fb">Kotlin:</text>
    <text x="60" y="1160" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">val numbers = listOf(1, 2, 3, 4, 5)</text>
    <text x="60" y="1175" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">val result = numbers.filter { it > 2 }</text>
    <text x="60" y="1190" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">                    .map { it * 2 }</text>
    <text x="60" y="1205" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">                    .sum()</text>
    <text x="60" y="1220" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">// 高阶函数：接受函数作为参数</text>
    <text x="60" y="1235" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">fun calculate(x: Int, operation: (Int) -> Int) = operation(x)</text>
    <text x="60" y="1250" font-family="Arial, sans-serif" font-size="11" fill="#27ae60">✓ 简洁的函数式编程</text>

    <!-- Java Code -->
    <rect x="820" y="1120" width="750" height="140" fill="#fff8e1" stroke="#ed8b00" stroke-width="2" rx="5"/>
    <text x="830" y="1140" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#ed8b00">Java:</text>
    <text x="830" y="1160" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">List&lt;Integer&gt; numbers = Arrays.asList(1,2,3,4,5);</text>
    <text x="830" y="1175" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">int result = numbers.stream()</text>
    <text x="830" y="1190" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    .filter(n -> n > 2)</text>
    <text x="830" y="1205" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    .mapToInt(n -> n * 2)</text>
    <text x="830" y="1220" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    .sum();</text>
    <text x="830" y="1235" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">// 需要Function&lt;Integer, Integer&gt;接口</text>
    <text x="830" y="1250" font-family="Arial, sans-serif" font-size="11" fill="#e74c3c">⚠ Stream API冗长，类型复杂</text>
  </g>

  <!-- Example 7: Coroutines vs Threads -->
  <g id="example7">
    <text x="50" y="1300" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#2c3e50">
      7. 协程 (Coroutines) vs 线程
    </text>

    <!-- Kotlin Code -->
    <rect x="50" y="1320" width="750" height="140" fill="#e8f4fd" stroke="#7f39fb" stroke-width="2" rx="5"/>
    <text x="60" y="1340" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#7f39fb">Kotlin:</text>
    <text x="60" y="1360" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">suspend fun fetchData(): String {</text>
    <text x="60" y="1375" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    delay(1000)  // 非阻塞延迟</text>
    <text x="60" y="1390" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    return "数据"</text>
    <text x="60" y="1405" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">}</text>
    <text x="60" y="1420" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">// 使用：</text>
    <text x="60" y="1435" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">GlobalScope.launch { val data = fetchData() }</text>
    <text x="60" y="1450" font-family="Arial, sans-serif" font-size="11" fill="#27ae60">✓ 轻量级，易于使用</text>

    <!-- Java Code -->
    <rect x="820" y="1320" width="750" height="140" fill="#fff8e1" stroke="#ed8b00" stroke-width="2" rx="5"/>
    <text x="830" y="1340" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#ed8b00">Java:</text>
    <text x="830" y="1360" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">public CompletableFuture&lt;String&gt; fetchData() {</text>
    <text x="830" y="1375" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    return CompletableFuture.supplyAsync(() -> {</text>
    <text x="830" y="1390" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">        try { Thread.sleep(1000); } catch (Exception e) {}</text>
    <text x="830" y="1405" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">        return "数据";</text>
    <text x="830" y="1420" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    });</text>
    <text x="830" y="1435" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">}</text>
    <text x="830" y="1450" font-family="Arial, sans-serif" font-size="11" fill="#e74c3c">⚠ 复杂的异步编程</text>
  </g>

  <!-- Example 8: Smart Casts -->
  <g id="example8">
    <text x="50" y="1500" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#2c3e50">
      8. 智能类型转换 vs instanceof
    </text>

    <!-- Kotlin Code -->
    <rect x="50" y="1520" width="750" height="120" fill="#e8f4fd" stroke="#7f39fb" stroke-width="2" rx="5"/>
    <text x="60" y="1540" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#7f39fb">Kotlin:</text>
    <text x="60" y="1560" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">fun processValue(value: Any) {</text>
    <text x="60" y="1575" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    if (value is String) {</text>
    <text x="60" y="1590" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">        println(value.length)  // 自动转换为String</text>
    <text x="60" y="1605" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    }</text>
    <text x="60" y="1620" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">}</text>
    <text x="60" y="1635" font-family="Arial, sans-serif" font-size="11" fill="#27ae60">✓ 编译器自动转换类型</text>

    <!-- Java Code -->
    <rect x="820" y="1520" width="750" height="120" fill="#fff8e1" stroke="#ed8b00" stroke-width="2" rx="5"/>
    <text x="830" y="1540" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#ed8b00">Java:</text>
    <text x="830" y="1560" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">public void processValue(Object value) {</text>
    <text x="830" y="1575" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    if (value instanceof String) {</text>
    <text x="830" y="1590" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">        String str = (String) value;  // 手动转换</text>
    <text x="830" y="1605" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">        System.out.println(str.length());</text>
    <text x="830" y="1620" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    }</text>
    <text x="830" y="1635" font-family="Arial, sans-serif" font-size="11" fill="#e74c3c">⚠ 需要手动类型转换</text>
  </g>

  <!-- Example 9: Default Parameters -->
  <g id="example9">
    <text x="50" y="1680" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#2c3e50">
      9. 默认参数 vs 方法重载
    </text>

    <!-- Kotlin Code -->
    <rect x="50" y="1700" width="750" height="120" fill="#e8f4fd" stroke="#7f39fb" stroke-width="2" rx="5"/>
    <text x="60" y="1720" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#7f39fb">Kotlin:</text>
    <text x="60" y="1740" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">fun createUser(name: String, age: Int = 18, email: String = "") {</text>
    <text x="60" y="1755" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    // 实现</text>
    <text x="60" y="1770" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">}</text>
    <text x="60" y="1785" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">// 调用：createUser("张三") 或 createUser("李四", 25)</text>
    <text x="60" y="1800" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">// 命名参数：createUser("王五", email = "<EMAIL>")</text>
    <text x="60" y="1815" font-family="Arial, sans-serif" font-size="11" fill="#27ae60">✓ 一个函数处理多种情况</text>

    <!-- Java Code -->
    <rect x="820" y="1700" width="750" height="120" fill="#fff8e1" stroke="#ed8b00" stroke-width="2" rx="5"/>
    <text x="830" y="1720" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#ed8b00">Java:</text>
    <text x="830" y="1740" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">public void createUser(String name) { createUser(name, 18, ""); }</text>
    <text x="830" y="1755" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">public void createUser(String name, int age) { createUser(name, age, ""); }</text>
    <text x="830" y="1770" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">public void createUser(String name, int age, String email) {</text>
    <text x="830" y="1785" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    // 实际实现</text>
    <text x="830" y="1800" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">}</text>
    <text x="830" y="1815" font-family="Arial, sans-serif" font-size="11" fill="#e74c3c">⚠ 需要多个重载方法</text>
  </g>

  <!-- Example 10: Properties vs Getters/Setters -->
  <g id="example10">
    <text x="50" y="1860" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#2c3e50">
      10. 属性 vs Getter/Setter
    </text>

    <!-- Kotlin Code -->
    <rect x="50" y="1880" width="750" height="140" fill="#e8f4fd" stroke="#7f39fb" stroke-width="2" rx="5"/>
    <text x="60" y="1900" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#7f39fb">Kotlin:</text>
    <text x="60" y="1920" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">class Person {</text>
    <text x="60" y="1935" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    var name: String = ""</text>
    <text x="60" y="1950" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">        set(value) { field = value.trim() }</text>
    <text x="60" y="1965" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    val isAdult: Boolean</text>
    <text x="60" y="1980" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">        get() = age >= 18</text>
    <text x="60" y="1995" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">}</text>
    <text x="60" y="2010" font-family="Arial, sans-serif" font-size="11" fill="#27ae60">✓ 简洁的属性语法</text>

    <!-- Java Code -->
    <rect x="820" y="1880" width="750" height="140" fill="#fff8e1" stroke="#ed8b00" stroke-width="2" rx="5"/>
    <text x="830" y="1900" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#ed8b00">Java:</text>
    <text x="830" y="1920" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">public class Person {</text>
    <text x="830" y="1935" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    private String name;</text>
    <text x="830" y="1950" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    public String getName() { return name; }</text>
    <text x="830" y="1965" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    public void setName(String name) { this.name = name.trim(); }</text>
    <text x="830" y="1980" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">    public boolean isAdult() { return age >= 18; }</text>
    <text x="830" y="1995" font-family="Courier New, monospace" font-size="11" fill="#2c3e50">}</text>
    <text x="830" y="2010" font-family="Arial, sans-serif" font-size="11" fill="#e74c3c">⚠ 冗长的getter/setter</text>
  </g>

  <!-- Performance and Ecosystem Comparison -->
  <g id="comparison-table">
    <text x="50" y="2060" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#2c3e50">
      11. 性能和生态系统对比
    </text>

    <rect x="50" y="2080" width="1520" height="200" fill="#ecf0f1" stroke="#34495e" stroke-width="2" rx="10"/>

    <!-- Table Headers -->
    <text x="70" y="2105" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2c3e50">对比维度</text>
    <text x="300" y="2105" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#7f39fb">Kotlin</text>
    <text x="800" y="2105" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#ed8b00">Java</text>

    <!-- Performance -->
    <text x="70" y="2130" font-family="Arial, sans-serif" font-size="14" fill="#2c3e50">运行时性能</text>
    <text x="300" y="2130" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">与Java几乎相同(编译为字节码)</text>
    <text x="800" y="2130" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">JVM优化，成熟的垃圾回收</text>

    <!-- Compilation -->
    <text x="70" y="2150" font-family="Arial, sans-serif" font-size="14" fill="#2c3e50">编译速度</text>
    <text x="300" y="2150" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">略慢于Java(增量编译改善)</text>
    <text x="800" y="2150" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">快速编译</text>

    <!-- Learning Curve -->
    <text x="70" y="2170" font-family="Arial, sans-serif" font-size="14" fill="#2c3e50">学习曲线</text>
    <text x="300" y="2170" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">Java开发者容易上手</text>
    <text x="800" y="2170" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">语法复杂，概念较多</text>

    <!-- Ecosystem -->
    <text x="70" y="2190" font-family="Arial, sans-serif" font-size="14" fill="#2c3e50">生态系统</text>
    <text x="300" y="2190" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">快速发展，Android官方支持</text>
    <text x="800" y="2190" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">成熟丰富，企业级支持</text>

    <!-- Community -->
    <text x="70" y="2210" font-family="Arial, sans-serif" font-size="14" fill="#2c3e50">社区支持</text>
    <text x="300" y="2210" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">活跃增长，JetBrains支持</text>
    <text x="800" y="2210" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">庞大成熟，Oracle官方支持</text>

    <!-- Job Market -->
    <text x="70" y="2230" font-family="Arial, sans-serif" font-size="14" fill="#2c3e50">就业市场</text>
    <text x="300" y="2230" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">Android开发首选，需求增长</text>
    <text x="800" y="2230" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">企业级应用广泛，需求稳定</text>

    <!-- Tooling -->
    <text x="70" y="2250" font-family="Arial, sans-serif" font-size="14" fill="#2c3e50">开发工具</text>
    <text x="300" y="2250" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">IntelliJ IDEA, Android Studio</text>
    <text x="800" y="2250" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">Eclipse, IntelliJ, NetBeans等</text>
  </g>

  <!-- Summary -->
  <g id="summary">
    <rect x="50" y="2300" width="1520" height="80" fill="#ecf0f1" stroke="#34495e" stroke-width="2" rx="10"/>
    <text x="810" y="2325" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#2c3e50">
      总结
    </text>
    <text x="60" y="2345" font-family="Arial, sans-serif" font-size="14" fill="#2c3e50">
      • Kotlin: 现代语言特性，简洁安全，Android开发首选，与Java 100%互操作
    </text>
    <text x="60" y="2360" font-family="Arial, sans-serif" font-size="14" fill="#2c3e50">
      • Java: 成熟稳定，生态丰富，企业级应用广泛，学习资源丰富
    </text>
    <text x="60" y="2375" font-family="Arial, sans-serif" font-size="14" fill="#2c3e50">
      • 选择建议: 新项目推荐Kotlin，现有Java项目可逐步迁移
    </text>
  </g>

  <!-- Arrow marker definition -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e"/>
    </marker>
  </defs>
</svg>
