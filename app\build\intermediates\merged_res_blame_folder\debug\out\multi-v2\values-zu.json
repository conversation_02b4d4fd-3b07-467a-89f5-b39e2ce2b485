{"logs": [{"outputFile": "com.sdwu.kotlin.app-mergeDebugResources-70:/values-zu/values-zu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1691789794c9b474ae1fde820463afff\\transformed\\material-1.9.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,349,426,503,597,685,797,923,1004,1071,1174,1249,1312,1404,1475,1540,1607,1679,1751,1805,1926,1985,2049,2103,2180,2312,2397,2478,2627,2714,2797,2889,2945,3003,3069,3141,3218,3309,3389,3468,3543,3622,3712,3785,3879,3976,4050,4123,4222,4277,4345,4433,4522,4584,4648,4711,4820,4925,5028,5137,5197,5259", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,77,76,76,93,87,111,125,80,66,102,74,62,91,70,64,66,71,71,53,120,58,63,53,76,131,84,80,148,86,82,91,55,57,65,71,76,90,79,78,74,78,89,72,93,96,73,72,98,54,67,87,88,61,63,62,108,104,102,108,59,61,81", "endOffsets": "266,344,421,498,592,680,792,918,999,1066,1169,1244,1307,1399,1470,1535,1602,1674,1746,1800,1921,1980,2044,2098,2175,2307,2392,2473,2622,2709,2792,2884,2940,2998,3064,3136,3213,3304,3384,3463,3538,3617,3707,3780,3874,3971,4045,4118,4217,4272,4340,4428,4517,4579,4643,4706,4815,4920,5023,5132,5192,5254,5336"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,48,50,51,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3007,3085,3162,3239,3333,3421,3533,3659,4368,4514,4617,4863,4926,5018,5089,5154,5221,5293,5365,5419,5540,5599,5663,5717,5794,5926,6011,6092,6241,6328,6411,6503,6559,6617,6683,6755,6832,6923,7003,7082,7157,7236,7326,7399,7493,7590,7664,7737,7836,7891,7959,8047,8136,8198,8262,8325,8434,8539,8642,8751,8811,9185", "endLines": "5,33,34,35,36,37,38,39,40,48,50,51,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,108", "endColumns": "12,77,76,76,93,87,111,125,80,66,102,74,62,91,70,64,66,71,71,53,120,58,63,53,76,131,84,80,148,86,82,91,55,57,65,71,76,90,79,78,74,78,89,72,93,96,73,72,98,54,67,87,88,61,63,62,108,104,102,108,59,61,81", "endOffsets": "316,3080,3157,3234,3328,3416,3528,3654,3735,4430,4612,4687,4921,5013,5084,5149,5216,5288,5360,5414,5535,5594,5658,5712,5789,5921,6006,6087,6236,6323,6406,6498,6554,6612,6678,6750,6827,6918,6998,7077,7152,7231,7321,7394,7488,7585,7659,7732,7831,7886,7954,8042,8131,8193,8257,8320,8429,8534,8637,8746,8806,8868,9262"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8d5d80ec9ea4b92e6a394c21abf0511b\\transformed\\core-1.9.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "113", "startColumns": "4", "startOffsets": "9601", "endColumns": "100", "endOffsets": "9697"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b599f6252b24759b04dd323019160ed1\\transformed\\material3-1.0.1\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,132,206", "endColumns": "76,73,78", "endOffsets": "127,201,280"}, "to": {"startLines": "43,46,49", "startColumns": "4,4,4", "startOffsets": "3917,4204,4435", "endColumns": "76,73,78", "endOffsets": "3989,4273,4509"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bc227b5d8c8bab74364961e342d51b40\\transformed\\appcompat-1.6.1\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,320,432,520,623,738,817,894,985,1078,1173,1267,1367,1460,1555,1649,1740,1833,1914,2018,2121,2219,2326,2433,2538,2695,2791", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "208,315,427,515,618,733,812,889,980,1073,1168,1262,1362,1455,1550,1644,1735,1828,1909,2013,2116,2214,2321,2428,2533,2690,2786,2868"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "321,429,536,648,736,839,954,1033,1110,1201,1294,1389,1483,1583,1676,1771,1865,1956,2049,2130,2234,2337,2435,2542,2649,2754,2911,9443", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "424,531,643,731,834,949,1028,1105,1196,1289,1384,1478,1578,1671,1766,1860,1951,2044,2125,2229,2332,2430,2537,2644,2749,2906,3002,9520"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2e1e0a755749a1238f97434281347ef6\\transformed\\ui-1.4.2\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,282,387,492,582,664,753,846,929,997,1065,1153,1241,1317,1396,1466", "endColumns": "94,81,104,104,89,81,88,92,82,67,67,87,87,75,78,69,123", "endOffsets": "195,277,382,487,577,659,748,841,924,992,1060,1148,1236,1312,1391,1461,1585"}, "to": {"startLines": "41,42,44,45,47,52,53,104,105,106,107,109,110,112,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3740,3835,3994,4099,4278,4692,4774,8873,8966,9049,9117,9267,9355,9525,9702,9781,9851", "endColumns": "94,81,104,104,89,81,88,92,82,67,67,87,87,75,78,69,123", "endOffsets": "3830,3912,4094,4199,4363,4769,4858,8961,9044,9112,9180,9350,9438,9596,9776,9846,9970"}}]}]}