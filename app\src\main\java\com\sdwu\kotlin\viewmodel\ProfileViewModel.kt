package com.sdwu.kotlin.viewmodel

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.sdwu.kotlin.data.model.User
import com.sdwu.kotlin.data.repository.UserRepositoryInterface
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
/**
 * 个人资料ViewModel
 * 管理用户信息的业务逻辑和UI状态
 */
class ProfileViewModel(
    private val userRepository: UserRepositoryInterface
) : ViewModel() {

    companion object {
        private const val TAG = "ProfileViewModel"
    }

    // UI状态
    private val _uiState = MutableStateFlow(ProfileUiState())
    val uiState: StateFlow<ProfileUiState> = _uiState.asStateFlow()

    init {
        Log.d(TAG, "ProfileViewModel初始化开始")
        try {
            loadUserProfile()
            Log.d(TAG, "ProfileViewModel初始化成功")
        } catch (e: Exception) {
            Log.e(TAG, "ProfileViewModel初始化失败", e)
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                error = "初始化失败: ${e.message}"
            )
        }
    }
    
    /**
     * 加载用户资料
     */
    fun loadUserProfile() {
        Log.d(TAG, "开始加载用户资料")
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)

            try {
                Log.d(TAG, "尝试初始化默认用户")
                // 初始化默认用户（如果不存在）
                userRepository.initializeDefaultUser()
                Log.d(TAG, "默认用户初始化完成")

                Log.d(TAG, "尝试获取当前用户")
                // 获取当前用户
                val user = userRepository.getCurrentUser()
                Log.d(TAG, "获取到用户: ${user?.name ?: "null"}")

                _uiState.value = _uiState.value.copy(
                    user = user,
                    isLoading = false,
                    error = null
                )
                Log.d(TAG, "用户资料加载成功")
            } catch (e: Exception) {
                Log.e(TAG, "加载用户资料失败", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "加载用户信息失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 更新用户信息
     */
    fun updateUserInfo(name: String, email: String) {
        val currentUser = _uiState.value.user ?: return
        
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            try {
                val updatedUser = currentUser.copy(
                    name = name,
                    email = email
                )
                
                userRepository.updateUser(updatedUser)
                
                _uiState.value = _uiState.value.copy(
                    user = updatedUser,
                    isLoading = false,
                    error = null,
                    isEditMode = false
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "更新用户信息失败"
                )
            }
        }
    }
    
    /**
     * 进入编辑模式
     */
    fun enterEditMode() {
        _uiState.value = _uiState.value.copy(isEditMode = true)
    }
    
    /**
     * 退出编辑模式
     */
    fun exitEditMode() {
        _uiState.value = _uiState.value.copy(isEditMode = false)
    }
    
    /**
     * 刷新用户信息
     */
    fun refreshUserInfo() {
        loadUserProfile()
    }
    
    /**
     * 清除错误状态
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
    
    /**
     * 验证用户输入
     */
    fun validateUserInput(name: String, email: String): String? {
        return when {
            name.isBlank() -> "用户名不能为空"
            email.isBlank() -> "邮箱不能为空"
            !email.contains("@") -> "邮箱格式不正确"
            else -> null
        }
    }
}

/**
 * 个人资料UI状态数据类
 */
data class ProfileUiState(
    val user: User? = null,
    val isLoading: Boolean = false,
    val error: String? = null,
    val isEditMode: Boolean = false
)
