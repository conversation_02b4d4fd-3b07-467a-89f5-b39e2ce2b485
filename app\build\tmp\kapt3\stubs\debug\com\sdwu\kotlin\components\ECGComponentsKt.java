package com.sdwu.kotlin.components;

@kotlin.Metadata(mv = {1, 8, 0}, k = 2, xi = 48, d1 = {"\u0000X\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\u001a<\u0010\u0000\u001a\u00020\u00012\b\u0010\u0002\u001a\u0004\u0018\u00010\u00032\b\u0010\u0004\u001a\u0004\u0018\u00010\u00052\u0006\u0010\u0006\u001a\u00020\u00072\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\b\b\u0002\u0010\n\u001a\u00020\u000bH\u0007\u001a*\u0010\f\u001a\u00020\u00012\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0010\u001a\u00020\u000e2\b\b\u0002\u0010\n\u001a\u00020\u000bH\u0003\u001a\u001a\u0010\u0011\u001a\u00020\u00012\u0006\u0010\u0012\u001a\u00020\u00052\b\b\u0002\u0010\n\u001a\u00020\u000bH\u0007\u001a2\u0010\u0013\u001a\u00020\u00012\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0010\u001a\u00020\u000e2\b\b\u0002\u0010\n\u001a\u00020\u000bH\u0003\u001a@\u0010\u0016\u001a\u00020\u00012\b\u0010\u0017\u001a\u0004\u0018\u00010\u00182\u0006\u0010\u0019\u001a\u00020\u00072\f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\b\b\u0002\u0010\n\u001a\u00020\u000bH\u0007\u001a\u001a\u0010\u001c\u001a\u00020\u00012\u0006\u0010\u001d\u001a\u00020\u001e2\b\b\u0002\u0010\n\u001a\u00020\u000bH\u0003\u001a\f\u0010\u001f\u001a\u00020\u0001*\u00020 H\u0002\u001a\u001a\u0010!\u001a\u00020\u0001*\u00020 2\f\u0010\"\u001a\b\u0012\u0004\u0012\u00020$0#H\u0002\u00a8\u0006%"}, d2 = {"ECGDataCard", "", "stats", "Lcom/sdwu/kotlin/data/repository/ECGStats;", "latestData", "Lcom/sdwu/kotlin/data/model/ECGWaveformData;", "isLoading", "", "onCardClick", "Lkotlin/Function0;", "modifier", "Landroidx/compose/ui/Modifier;", "ECGStatItem", "label", "", "value", "unit", "ECGWaveformPreview", "waveformData", "RealtimeDataItem", "icon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "RealtimeECGMonitor", "realtimeData", "Lcom/sdwu/kotlin/data/model/ECGRealtimeData;", "isMonitoring", "onStartMonitoring", "onStopMonitoring", "SignalQualityIndicator", "quality", "Lcom/sdwu/kotlin/data/model/ECGQuality;", "drawECGGrid", "Landroidx/compose/ui/graphics/drawscope/DrawScope;", "drawECGWaveform", "dataPoints", "", "Lcom/sdwu/kotlin/data/model/ECGDataPoint;", "app_debug"})
public final class ECGComponentsKt {
    
    /**
     * ECG数据卡片组件
     * 在首页展示ECG统计信息
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void ECGDataCard(@org.jetbrains.annotations.Nullable()
    com.sdwu.kotlin.data.repository.ECGStats stats, @org.jetbrains.annotations.Nullable()
    com.sdwu.kotlin.data.model.ECGWaveformData latestData, boolean isLoading, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onCardClick, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * ECG统计项组件
     */
    @androidx.compose.runtime.Composable()
    private static final void ECGStatItem(java.lang.String label, java.lang.String value, java.lang.String unit, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 信号质量指示器
     */
    @androidx.compose.runtime.Composable()
    private static final void SignalQualityIndicator(com.sdwu.kotlin.data.model.ECGQuality quality, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * ECG波形预览组件
     */
    @androidx.compose.runtime.Composable()
    public static final void ECGWaveformPreview(@org.jetbrains.annotations.NotNull()
    com.sdwu.kotlin.data.model.ECGWaveformData waveformData, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 绘制ECG波形
     */
    private static final void drawECGWaveform(androidx.compose.ui.graphics.drawscope.DrawScope $this$drawECGWaveform, java.util.List<com.sdwu.kotlin.data.model.ECGDataPoint> dataPoints) {
    }
    
    /**
     * 绘制ECG网格
     */
    private static final void drawECGGrid(androidx.compose.ui.graphics.drawscope.DrawScope $this$drawECGGrid) {
    }
    
    /**
     * 实时ECG监测组件
     */
    @androidx.compose.runtime.Composable()
    public static final void RealtimeECGMonitor(@org.jetbrains.annotations.Nullable()
    com.sdwu.kotlin.data.model.ECGRealtimeData realtimeData, boolean isMonitoring, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onStartMonitoring, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onStopMonitoring, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 实时数据项组件
     */
    @androidx.compose.runtime.Composable()
    private static final void RealtimeDataItem(androidx.compose.ui.graphics.vector.ImageVector icon, java.lang.String label, java.lang.String value, java.lang.String unit, androidx.compose.ui.Modifier modifier) {
    }
}