package com.sdwu.kotlin.data.model

/**
 * 用户数据模型
 * 用于数据存储和UI展示
 */
data class User(
    val id: String,
    val name: String,
    val email: String,
    val registrationDate: String,
    val avatarUrl: String? = null
)

/**
 * 用户设置数据模型
 */
data class UserSettings(
    val darkMode: Boolean = false,
    val notificationsEnabled: Boolean = true,
    val language: String = "zh-CN"
)

/**
 * 首页列表项数据模型
 */
data class HomeItem(
    val id: String,
    val title: String,
    val description: String,
    val imageUrl: String? = null,
    val createdAt: Long = System.currentTimeMillis()
)

/**
 * 详情页数据模型
 */
data class ItemDetail(
    val id: String,
    val title: String,
    val description: String,
    val content: String,
    val imageUrl: String? = null,
    val tags: List<String> = emptyList(),
    val createdAt: Long,
    val updatedAt: Long
)
