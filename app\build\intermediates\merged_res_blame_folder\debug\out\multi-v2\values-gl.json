{"logs": [{"outputFile": "com.sdwu.kotlin.app-mergeDebugResources-70:/values-gl/values-gl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b599f6252b24759b04dd323019160ed1\\transformed\\material3-1.0.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,133,215", "endColumns": "77,81,77", "endOffsets": "128,210,288"}, "to": {"startLines": "43,46,49", "startColumns": "4,4,4", "startOffsets": "4016,4304,4553", "endColumns": "77,81,77", "endOffsets": "4089,4381,4626"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1691789794c9b474ae1fde820463afff\\transformed\\material-1.9.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,275,356,436,521,623,719,824,957,1037,1115,1211,1290,1353,1448,1512,1581,1644,1718,1782,1838,1959,2017,2079,2135,2212,2351,2439,2519,2659,2739,2819,2909,2965,3021,3087,3166,3247,3335,3414,3491,3573,3662,3746,3838,3931,4032,4106,4198,4300,4352,4418,4510,4598,4660,4724,4787,4898,5000,5106,5209,5269,5329", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,80,79,84,101,95,104,132,79,77,95,78,62,94,63,68,62,73,63,55,120,57,61,55,76,138,87,79,139,79,79,89,55,55,65,78,80,87,78,76,81,88,83,91,92,100,73,91,101,51,65,91,87,61,63,62,110,101,105,102,59,59,84", "endOffsets": "270,351,431,516,618,714,819,952,1032,1110,1206,1285,1348,1443,1507,1576,1639,1713,1777,1833,1954,2012,2074,2130,2207,2346,2434,2514,2654,2734,2814,2904,2960,3016,3082,3161,3242,3330,3409,3486,3568,3657,3741,3833,3926,4027,4101,4193,4295,4347,4413,4505,4593,4655,4719,4782,4893,4995,5101,5204,5264,5324,5409"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,48,50,51,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3075,3156,3236,3321,3423,3519,3624,3757,4475,4631,4727,4978,5041,5136,5200,5269,5332,5406,5470,5526,5647,5705,5767,5823,5900,6039,6127,6207,6347,6427,6507,6597,6653,6709,6775,6854,6935,7023,7102,7179,7261,7350,7434,7526,7619,7720,7794,7886,7988,8040,8106,8198,8286,8348,8412,8475,8586,8688,8794,8897,8957,9340", "endLines": "5,33,34,35,36,37,38,39,40,48,50,51,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,108", "endColumns": "12,80,79,84,101,95,104,132,79,77,95,78,62,94,63,68,62,73,63,55,120,57,61,55,76,138,87,79,139,79,79,89,55,55,65,78,80,87,78,76,81,88,83,91,92,100,73,91,101,51,65,91,87,61,63,62,110,101,105,102,59,59,84", "endOffsets": "320,3151,3231,3316,3418,3514,3619,3752,3832,4548,4722,4801,5036,5131,5195,5264,5327,5401,5465,5521,5642,5700,5762,5818,5895,6034,6122,6202,6342,6422,6502,6592,6648,6704,6770,6849,6930,7018,7097,7174,7256,7345,7429,7521,7614,7715,7789,7881,7983,8035,8101,8193,8281,8343,8407,8470,8581,8683,8789,8892,8952,9012,9420"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8d5d80ec9ea4b92e6a394c21abf0511b\\transformed\\core-1.9.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "113", "startColumns": "4", "startOffsets": "9760", "endColumns": "100", "endOffsets": "9856"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bc227b5d8c8bab74364961e342d51b40\\transformed\\appcompat-1.6.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,313,421,506,607,735,821,902,994,1088,1185,1279,1379,1473,1569,1664,1756,1848,1929,2037,2144,2251,2360,2465,2579,2756,2855", "endColumns": "103,103,107,84,100,127,85,80,91,93,96,93,99,93,95,94,91,91,80,107,106,106,108,104,113,176,98,82", "endOffsets": "204,308,416,501,602,730,816,897,989,1083,1180,1274,1374,1468,1564,1659,1751,1843,1924,2032,2139,2246,2355,2460,2574,2751,2850,2933"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "325,429,533,641,726,827,955,1041,1122,1214,1308,1405,1499,1599,1693,1789,1884,1976,2068,2149,2257,2364,2471,2580,2685,2799,2976,9600", "endColumns": "103,103,107,84,100,127,85,80,91,93,96,93,99,93,95,94,91,91,80,107,106,106,108,104,113,176,98,82", "endOffsets": "424,528,636,721,822,950,1036,1117,1209,1303,1400,1494,1594,1688,1784,1879,1971,2063,2144,2252,2359,2466,2575,2680,2794,2971,3070,9678"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2e1e0a755749a1238f97434281347ef6\\transformed\\ui-1.4.2\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,284,393,494,583,662,755,847,935,1008,1078,1163,1253,1330,1412,1484", "endColumns": "95,82,108,100,88,78,92,91,87,72,69,84,89,76,81,71,121", "endOffsets": "196,279,388,489,578,657,750,842,930,1003,1073,1158,1248,1325,1407,1479,1601"}, "to": {"startLines": "41,42,44,45,47,52,53,104,105,106,107,109,110,112,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3837,3933,4094,4203,4386,4806,4885,9017,9109,9197,9270,9425,9510,9683,9861,9943,10015", "endColumns": "95,82,108,100,88,78,92,91,87,72,69,84,89,76,81,71,121", "endOffsets": "3928,4011,4198,4299,4470,4880,4973,9104,9192,9265,9335,9505,9595,9755,9938,10010,10132"}}]}]}