package com.sdwu.kotlin.viewmodel;

/**
 * HRV UI状态
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, xi = 48, d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0018\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001Bg\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t\u0012\u000e\b\u0002\u0010\n\u001a\b\u0012\u0004\u0012\u00020\f0\u000b\u0012\u000e\b\u0002\u0010\r\u001a\b\u0012\u0004\u0012\u00020\t0\u000b\u0012\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u000f\u00a2\u0006\u0002\u0010\u0010J\t\u0010\u001c\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001d\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\u001f\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J\u000b\u0010 \u001a\u0004\u0018\u00010\tH\u00c6\u0003J\u000f\u0010!\u001a\b\u0012\u0004\u0012\u00020\f0\u000bH\u00c6\u0003J\u000f\u0010\"\u001a\b\u0012\u0004\u0012\u00020\t0\u000bH\u00c6\u0003J\u000b\u0010#\u001a\u0004\u0018\u00010\u000fH\u00c6\u0003Jk\u0010$\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t2\u000e\b\u0002\u0010\n\u001a\b\u0012\u0004\u0012\u00020\f0\u000b2\u000e\b\u0002\u0010\r\u001a\b\u0012\u0004\u0012\u00020\t0\u000b2\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u000fH\u00c6\u0001J\u0013\u0010%\u001a\u00020\u00032\b\u0010&\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\'\u001a\u00020(H\u00d6\u0001J\t\u0010)\u001a\u00020\u000fH\u00d6\u0001R\u0013\u0010\u000e\u001a\u0004\u0018\u00010\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0017\u0010\r\u001a\b\u0012\u0004\u0012\u00020\t0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010\u0015R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0004\u0010\u0015R\u0013\u0010\b\u001a\u0004\u0018\u00010\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0015R\u0013\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001aR\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\f0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0014\u00a8\u0006*"}, d2 = {"Lcom/sdwu/kotlin/viewmodel/HRVUiState;", "", "isLoading", "", "isMeasuring", "measurementCompleted", "stats", "Lcom/sdwu/kotlin/data/repository/HRVStats;", "latestData", "Lcom/sdwu/kotlin/data/model/HRVData;", "trendData", "", "Lcom/sdwu/kotlin/data/model/HRVTrendData;", "historicalData", "error", "", "(ZZZLcom/sdwu/kotlin/data/repository/HRVStats;Lcom/sdwu/kotlin/data/model/HRVData;Ljava/util/List;Ljava/util/List;Ljava/lang/String;)V", "getError", "()Ljava/lang/String;", "getHistoricalData", "()Ljava/util/List;", "()Z", "getLatestData", "()Lcom/sdwu/kotlin/data/model/HRVData;", "getMeasurementCompleted", "getStats", "()Lcom/sdwu/kotlin/data/repository/HRVStats;", "getTrendData", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class HRVUiState {
    private final boolean isLoading = false;
    private final boolean isMeasuring = false;
    private final boolean measurementCompleted = false;
    @org.jetbrains.annotations.Nullable()
    private final com.sdwu.kotlin.data.repository.HRVStats stats = null;
    @org.jetbrains.annotations.Nullable()
    private final com.sdwu.kotlin.data.model.HRVData latestData = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.sdwu.kotlin.data.model.HRVTrendData> trendData = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.sdwu.kotlin.data.model.HRVData> historicalData = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String error = null;
    
    public HRVUiState(boolean isLoading, boolean isMeasuring, boolean measurementCompleted, @org.jetbrains.annotations.Nullable()
    com.sdwu.kotlin.data.repository.HRVStats stats, @org.jetbrains.annotations.Nullable()
    com.sdwu.kotlin.data.model.HRVData latestData, @org.jetbrains.annotations.NotNull()
    java.util.List<com.sdwu.kotlin.data.model.HRVTrendData> trendData, @org.jetbrains.annotations.NotNull()
    java.util.List<com.sdwu.kotlin.data.model.HRVData> historicalData, @org.jetbrains.annotations.Nullable()
    java.lang.String error) {
        super();
    }
    
    public final boolean isLoading() {
        return false;
    }
    
    public final boolean isMeasuring() {
        return false;
    }
    
    public final boolean getMeasurementCompleted() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.sdwu.kotlin.data.repository.HRVStats getStats() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.sdwu.kotlin.data.model.HRVData getLatestData() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.sdwu.kotlin.data.model.HRVTrendData> getTrendData() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.sdwu.kotlin.data.model.HRVData> getHistoricalData() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getError() {
        return null;
    }
    
    public HRVUiState() {
        super();
    }
    
    public final boolean component1() {
        return false;
    }
    
    public final boolean component2() {
        return false;
    }
    
    public final boolean component3() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.sdwu.kotlin.data.repository.HRVStats component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.sdwu.kotlin.data.model.HRVData component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.sdwu.kotlin.data.model.HRVTrendData> component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.sdwu.kotlin.data.model.HRVData> component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.sdwu.kotlin.viewmodel.HRVUiState copy(boolean isLoading, boolean isMeasuring, boolean measurementCompleted, @org.jetbrains.annotations.Nullable()
    com.sdwu.kotlin.data.repository.HRVStats stats, @org.jetbrains.annotations.Nullable()
    com.sdwu.kotlin.data.model.HRVData latestData, @org.jetbrains.annotations.NotNull()
    java.util.List<com.sdwu.kotlin.data.model.HRVTrendData> trendData, @org.jetbrains.annotations.NotNull()
    java.util.List<com.sdwu.kotlin.data.model.HRVData> historicalData, @org.jetbrains.annotations.Nullable()
    java.lang.String error) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}