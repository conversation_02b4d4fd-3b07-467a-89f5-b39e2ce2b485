<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义渐变色 -->
    <linearGradient id="dataBindingGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2E7D32;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="viewBindingGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2196F3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1565C0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="sharedGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF9800;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F57C00;stop-opacity:1" />
    </linearGradient>
    
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
            refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- 背景 -->
  <rect width="1200" height="800" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="600" y="30" text-anchor="middle" font-family="Arial, sans-serif" 
        font-size="24" font-weight="bold" fill="#333">
    DataBinding vs ViewBinding 执行流程对比
  </text>
  
  <!-- DataBinding 流程 (左侧) -->
  <g id="databinding-flow">
    <!-- 标题 -->
    <rect x="50" y="60" width="500" height="40" fill="url(#dataBindingGradient)" rx="5"/>
    <text x="300" y="85" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="18" font-weight="bold" fill="white">
      DataBinding 执行流程
    </text>
    
    <!-- 1. 编译时生成 -->
    <rect x="70" y="120" width="460" height="60" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2" rx="5"/>
    <text x="90" y="140" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2E7D32">
      1. 编译时 (Compile Time)
    </text>
    <text x="90" y="158" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 解析 &lt;layout&gt; 标签和 &lt;data&gt; 变量
    </text>
    <text x="90" y="172" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 生成 ActivityDataBindingBinding.java 类
    </text>
    
    <!-- 2. Activity初始化 -->
    <rect x="70" y="200" width="460" height="80" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2" rx="5"/>
    <text x="90" y="220" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2E7D32">
      2. Activity 初始化
    </text>
    <text x="90" y="238" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • DataBindingUtil.setContentView(this, R.layout.activity_data_binding)
    </text>
    <text x="90" y="252" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • binding.viewModel = viewModel
    </text>
    <text x="90" y="266" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • binding.lifecycleOwner = this
    </text>
    
    <!-- 3. 自动绑定 -->
    <rect x="70" y="300" width="460" height="100" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2" rx="5"/>
    <text x="90" y="320" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2E7D32">
      3. 自动数据绑定 (核心优势)
    </text>
    <text x="90" y="338" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • LiveData 自动观察: user.observe() 自动调用
    </text>
    <text x="90" y="352" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 表达式绑定: @{viewModel.user.name} 自动更新
    </text>
    <text x="90" y="366" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 双向绑定: @={viewModel.nameInput} 自动同步
    </text>
    <text x="90" y="380" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 条件绑定: @{viewModel.isLoading ? View.VISIBLE : View.GONE}
    </text>
    
    <!-- 4. 运行时更新 -->
    <rect x="70" y="420" width="460" height="80" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2" rx="5"/>
    <text x="90" y="440" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2E7D32">
      4. 运行时自动更新
    </text>
    <text x="90" y="458" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • ViewModel 数据变化 → 自动触发 UI 更新
    </text>
    <text x="90" y="472" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 无需手动调用 setText() 或 setVisibility()
    </text>
    <text x="90" y="486" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 生命周期感知，自动管理观察者
    </text>
  </g>
  
  <!-- ViewBinding 流程 (右侧) -->
  <g id="viewbinding-flow">
    <!-- 标题 -->
    <rect x="650" y="60" width="500" height="40" fill="url(#viewBindingGradient)" rx="5"/>
    <text x="900" y="85" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="18" font-weight="bold" fill="white">
      ViewBinding 执行流程
    </text>
    
    <!-- 1. 编译时生成 -->
    <rect x="670" y="120" width="460" height="60" fill="#E3F2FD" stroke="#2196F3" stroke-width="2" rx="5"/>
    <text x="690" y="140" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#1565C0">
      1. 编译时 (Compile Time)
    </text>
    <text x="690" y="158" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 解析普通布局文件 (无需 &lt;layout&gt; 标签)
    </text>
    <text x="690" y="172" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 生成 ActivityViewBindingBinding.java 类
    </text>
    
    <!-- 2. Activity初始化 -->
    <rect x="670" y="200" width="460" height="80" fill="#E3F2FD" stroke="#2196F3" stroke-width="2" rx="5"/>
    <text x="690" y="220" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#1565C0">
      2. Activity 初始化
    </text>
    <text x="690" y="238" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • binding = ActivityViewBindingBinding.inflate(layoutInflater)
    </text>
    <text x="690" y="252" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • setContentView(binding.root)
    </text>
    <text x="690" y="266" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 手动设置 ViewModel 实例
    </text>
    
    <!-- 3. 手动绑定 -->
    <rect x="670" y="300" width="460" height="100" fill="#E3F2FD" stroke="#2196F3" stroke-width="2" rx="5"/>
    <text x="690" y="320" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#1565C0">
      3. 手动数据绑定 (需要编码)
    </text>
    <text x="690" y="338" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 手动设置监听器: binding.btnSave.setOnClickListener { }
    </text>
    <text x="690" y="352" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 手动观察数据: viewModel.uiState.collect { }
    </text>
    <text x="690" y="366" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 手动更新UI: binding.etName.setText(user.name)
    </text>
    <text x="690" y="380" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 手动控制可见性: binding.progressBar.visibility = View.VISIBLE
    </text>
    
    <!-- 4. 运行时更新 -->
    <rect x="670" y="420" width="460" height="80" fill="#E3F2FD" stroke="#2196F3" stroke-width="2" rx="5"/>
    <text x="690" y="440" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#1565C0">
      4. 运行时手动更新
    </text>
    <text x="690" y="458" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • ViewModel 数据变化 → 手动在 collect/observe 中更新 UI
    </text>
    <text x="690" y="472" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 需要显式调用 setText()、setVisibility() 等方法
    </text>
    <text x="690" y="486" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 需要手动管理生命周期和观察者
    </text>
  </g>
  
  <!-- 共同特点 -->
  <g id="shared-features">
    <rect x="200" y="530" width="800" height="120" fill="#FFF3E0" stroke="#FF9800" stroke-width="2" rx="5"/>
    <text x="600" y="555" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="16" font-weight="bold" fill="#F57C00">
      共同特点
    </text>
    <text x="220" y="580" font-family="Arial, sans-serif" font-size="13" fill="#333">
      ✓ 编译时安全：都在编译时生成绑定类，避免 findViewById() 的空指针异常
    </text>
    <text x="220" y="600" font-family="Arial, sans-serif" font-size="13" fill="#333">
      ✓ 性能优化：都比 findViewById() 更高效，避免重复的视图查找
    </text>
    <text x="220" y="620" font-family="Arial, sans-serif" font-size="13" fill="#333">
      ✓ 类型安全：都提供强类型的视图引用，减少类型转换错误
    </text>
    <text x="220" y="640" font-family="Arial, sans-serif" font-size="13" fill="#333">
      ✓ 代码简洁：都减少了样板代码，提高开发效率
    </text>
  </g>
  
  <!-- 关键区别对比 -->
  <g id="key-differences">
    <rect x="50" y="670" width="500" height="100" fill="#FFEBEE" stroke="#F44336" stroke-width="2" rx="5"/>
    <text x="300" y="695" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="14" font-weight="bold" fill="#C62828">
      DataBinding 特点
    </text>
    <text x="70" y="715" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 支持表达式绑定 @{expression}
    </text>
    <text x="70" y="730" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 自动数据观察和UI更新
    </text>
    <text x="70" y="745" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 学习成本高，调试复杂
    </text>
    <text x="70" y="760" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 编译时间较长
    </text>
    
    <rect x="650" y="670" width="500" height="100" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2" rx="5"/>
    <text x="900" y="695" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="14" font-weight="bold" fill="#2E7D32">
      ViewBinding 特点
    </text>
    <text x="670" y="715" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 仅提供视图引用，无表达式
    </text>
    <text x="670" y="730" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 需要手动处理数据绑定
    </text>
    <text x="670" y="745" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 简单易懂，调试方便
    </text>
    <text x="670" y="760" font-family="Arial, sans-serif" font-size="12" fill="#333">
      • 编译速度快
    </text>
  </g>
  
  <!-- 流程箭头 -->
  <g id="flow-arrows">
    <!-- DataBinding 流程箭头 -->
    <line x1="300" y1="180" x2="300" y2="195" stroke="#4CAF50" stroke-width="3" marker-end="url(#arrowhead)"/>
    <line x1="300" y1="280" x2="300" y2="295" stroke="#4CAF50" stroke-width="3" marker-end="url(#arrowhead)"/>
    <line x1="300" y1="400" x2="300" y2="415" stroke="#4CAF50" stroke-width="3" marker-end="url(#arrowhead)"/>
    
    <!-- ViewBinding 流程箭头 -->
    <line x1="900" y1="180" x2="900" y2="195" stroke="#2196F3" stroke-width="3" marker-end="url(#arrowhead)"/>
    <line x1="900" y1="280" x2="900" y2="295" stroke="#2196F3" stroke-width="3" marker-end="url(#arrowhead)"/>
    <line x1="900" y1="400" x2="900" y2="415" stroke="#2196F3" stroke-width="3" marker-end="url(#arrowhead)"/>
  </g>
  
  <!-- 对比箭头 -->
  <line x1="550" y1="350" x2="650" y2="350" stroke="#666" stroke-width="2" stroke-dasharray="5,5"/>
  <text x="600" y="345" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#666">
    VS
  </text>
</svg>
