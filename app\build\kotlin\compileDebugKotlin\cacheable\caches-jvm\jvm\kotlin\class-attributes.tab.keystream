!com.sdwu.kotlin.KotlinApplication+com.sdwu.kotlin.KotlinApplication.Companioncom.sdwu.kotlin.MainActivity&com.sdwu.kotlin.MainActivity.Companion&com.sdwu.kotlin.data.model.ECGLeadType%com.sdwu.kotlin.data.model.ECGQuality(com.sdwu.kotlin.data.model.ECGRhythmType'com.sdwu.kotlin.data.model.ECGDataPoint*com.sdwu.kotlin.data.model.ECGWaveformData*com.sdwu.kotlin.data.model.ECGRealtimeData)com.sdwu.kotlin.data.model.ECGAbnormality,com.sdwu.kotlin.data.model.ECGAnalysisResult*com.sdwu.kotlin.data.model.HRVAnalysisType/com.sdwu.kotlin.data.model.HRVMeasurementStatus%com.sdwu.kotlin.data.model.RRInterval/com.sdwu.kotlin.data.model.HRVTimeDomainMetrics4com.sdwu.kotlin.data.model.HRVFrequencyDomainMetrics.com.sdwu.kotlin.data.model.HRVNonlinearMetrics"com.sdwu.kotlin.data.model.HRVData,com.sdwu.kotlin.data.model.HRVAnalysisConfig,com.sdwu.kotlin.data.model.HRVFilterSettings'com.sdwu.kotlin.data.model.HRVTrendData*com.sdwu.kotlin.data.model.HRVRealtimeDatacom.sdwu.kotlin.data.model.User'com.sdwu.kotlin.data.model.UserSettings#com.sdwu.kotlin.data.model.HomeItem%com.sdwu.kotlin.data.model.ItemDetail-com.sdwu.kotlin.data.repository.ECGRepository(com.sdwu.kotlin.data.repository.ECGStats-com.sdwu.kotlin.data.repository.HRVRepository(<EMAIL><com.sdwu.kotlin.data.repository.SettingsRepository.Companion7com.sdwu.kotlin.data.repository.UserRepositoryInterfacecom.sdwu.kotlin.di.AppContainer)com.sdwu.kotlin.di.AppContainer.Companion+com.sdwu.kotlin.navigation.NavigationHelper!com.sdwu.kotlin.navigation.Routes-com.sdwu.kotlin.utils.ComposeNavigationHelper=com.sdwu.kotlin.utils.ComposeNavigationHelper.NavigationState"com.sdwu.kotlin.utils.CrashHandler,com.sdwu.kotlin.utils.CrashHandler.Companion com.sdwu.kotlin.utils.DebugUtils!com.sdwu.kotlin.utils.ErrorLogger,com.sdwu.kotlin.utils.NavigationErrorHandler$com.sdwu.kotlin.utils.NavigationTest'com.sdwu.kotlin.utils.ProfileScreenTest)com.sdwu.kotlin.viewmodel.DetailViewModel'com.sdwu.kotlin.viewmodel.DetailUiState&com.sdwu.kotlin.viewmodel.ECGViewModel0com.sdwu.kotlin.viewmodel.ECGViewModel.Companion$com.sdwu.kotlin.viewmodel.ECGUiState&com.sdwu.kotlin.viewmodel.HRVViewModel0com.sdwu.kotlin.viewmodel.HRVViewModel.Companion$com.sdwu.kotlin.viewmodel.HRVUiState'com.sdwu.kotlin.viewmodel.HomeViewModel%com.sdwu.kotlin.viewmodel.HomeUiState*com.sdwu.kotlin.viewmodel.ProfileViewModel4com.sdwu.kotlin.viewmodel.ProfileViewModel.Companion(com.sdwu.kotlin.viewmodel.ProfileUiState+com.sdwu.kotlin.viewmodel.SettingsViewModel)com.sdwu.kotlin.viewmodel.SettingsUiStatecom.sdwu.kotlin.BR$com.sdwu.kotlin.DataBinderMapperImpl2com.sdwu.kotlin.DataBinderMapperImpl.InnerBrLookup,androidx.databinding.library.baseAdapters.BR8com.sdwu.kotlin.DataBinderMapperImpl.InnerLayoutIdLookup                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    